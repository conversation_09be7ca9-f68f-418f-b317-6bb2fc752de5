plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
// 这里切换dxjdkey.properties或key.properties
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}
apply plugin: 'mediation-auto-adapter'
apply plugin: 'com.huawei.agconnect'
android {
    namespace "com.dxjk.xw"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.dxjk.xw"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 26 //flutter.minSdkVersion  安卓8
        //targetSdkVersion flutter.targetSdkVersion
        targetSdkVersion 31
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk {
            //设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters "armeabi", "armeabi-v7a", "arm64-v8a"//, "x86"//"armeabi", "armeabi-v7a", "arm64-v8a"//"x86","x86_64"
        }

        multiDexEnabled true
//        manifestPlaceholders = [
//                UMENG_APPKEY: '64fefc8ab2f6fa00ba4f53b2',
//                UMENG_MESSAGE_SECRET: 'bdcbc3d5b784592c192d4fff454ad4e7',
//                UMENG_CHANNEL: 'appstore',
//
//                HUAWEI_APP_ID: '您申请的华为通道appid',
//
//                HONOR_APP_ID: '您申请的荣耀通道appid',
//
//                XIAOMI_APP_ID: '2882303761520237640',
//                XIAOMI_APP_KEY: '5602023756640',
//
//                OPPO_APP_KEY: '49b4e03805094ddd8afed4b689e1f230',
//                OPPO_APP_SECRET: 'bf5a2af9571040b4ac8caf2ffe9068e7',
//
//                VIVO_APP_ID: '105640412',
//                VIVO_APP_KEY: 'fd0cb9c43810bd0076062a771feb456d',
//
//                MEIZU_APP_ID: '您申请的魅族通道appid',
//                MEIZU_APP_KEY: '您申请的魅族通道appkey',
//        ]

        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY : "b4d0b660fa2cffe2fc8e07a0", // NOTE: JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.


                XIAOMI_APPID  : "MI-2882303761520344318",
                XIAOMI_APPKEY : "MI-5162034415318",

                VIVO_APPKEY : "e81629b9bf6a525e0141019f62f3f39a", // VIVO平台注册的appkey
                VIVO_APPID : "105787068", // VIVO平台注册的appid

                OPPO_APPKEY : "OP-fdffeb539a094c0f97cb655662e954df", // OPPO平台注册的appkey
                OPPO_APPID : "OP-32426535", // OPPO平台注册的appid
                OPPO_APPSECRET: "OP-31bea281d94e4994a8dfb275ff7cad3f", //OPPO平台注册的appsecret

                HONOR_APPID : "104469071", // Honor平台注册的APP ID
        ]
    }

    packagingOptions {
        pickFirst 'lib/arm64-v8a/libzeus_direct_dex.so'
        pickFirst 'lib/armeabi-v7a/libzeus_direct_dex.so'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ?
                    file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            // 禁用混淆，避免一些flutter插件不能正常使用！
            minifyEnabled false
//            useProguard false
            shrinkResources false
            debuggable false // 放开release模型下的日志输出
            signingConfig signingConfigs.release
        }
    }

    repositories {
        flatDir { dirs 'libs' }
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError false
    }

    aaptOptions {
        noCompress = ['.ress', '.resource', '.obb'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation(name:"com.heytap.msp_3.5.2",ext:"aar")
    implementation(name:"HiPushSDK-8.0.12.307",ext:"aar")
//    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation 'com.android.support:multidex:1.0.3'
    //3D地图so及jar,已经包含定位和搜索功能无需单独引用
    implementation 'com.amap.api:3dmap-location-search:latest.integration'
//    implementation ("com.pangle.cn:ads-sdk-pro:*******")
//            {
//        exclude group: 'com.bykv.vk.openvk.api.proto', module: 'bridge'
//        exclude group: 'com.ss.android.socialbase.downloader', module: 'downloader'
//    }
//    implementation ("com.volcengine.android:douyin_sdk:3.0.2"){
//        exclude group: 'com.volcengine', module: 'zeuslivesdkapi'
//        exclude group: 'com.bytedance.ies.social.base', module: 'downloader'
//    }
    implementation 'com.alibaba:fastjson:1.2.7'

    implementation project(':x-aidl-eventbus')
    implementation project(':unityLibrary')

    implementation 'com.umeng.umsdk:common:+'// (必选)版本号
    implementation 'com.umeng.umsdk:asms:+'// asms包依赖(必选)

    //友盟分享核心组件
    implementation 'com.umeng.umsdk:share-core:+'
    //友盟分享依赖v4包
    implementation 'com.android.support:support-v4:25.0.0'
    //微信
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.24'
    implementation 'com.umeng.umsdk:share-wx:+'
    //抖音
    implementation 'com.bytedance.ies.ugc.aweme:opensdk-china-external:0.1.9.0'
    implementation 'com.bytedance.ies.ugc.aweme:opensdk-common:0.1.9.0'
//    implementation files('src\\main\\libs\\umeng-share-bytedance-7.3.5.jar')

    implementation 'cn.jiguang.sdk.plugin:vivo:5.5.0'
    implementation 'com.huawei.hms:push:6.12.0.300'
    implementation 'cn.jiguang.sdk.plugin:huawei:5.5.0'

    implementation 'cn.jiguang.sdk.plugin:xiaomi:5.4.0'//版本号和对应的JPush版本号相同

    implementation 'cn.jiguang.sdk.plugin:oppo:5.5.0'
    //OPPO 3.1.0 aar 及其以上版本需要添加以下依赖
    implementation 'com.google.code.gson:gson:2.6.2'
    implementation 'commons-codec:commons-codec:1.6'
    implementation 'androidx.annotation:annotation:1.1.0'

    //厂商版本和 JPush SDK 版本保持一致
    implementation 'cn.jiguang.sdk.plugin:honor:5.5.0'

    def open_sdk_version = "0.2.0.8" // 使用最新的版本
    implementation "com.bytedance.ies.ugc.aweme:opensdk-china-external:$open_sdk_version"
    implementation "com.bytedance.ies.ugc.aweme:opensdk-common:$open_sdk_version"
    // 默认网络能力实现。如果不使用默认实现可以不依赖该组件，自行实现OpenNetworkService然后注入。
    implementation "com.bytedance.ies.ugc.aweme:openokhttp-china-external:$open_sdk_version"
    // 默认图片能力实现。如果不使用默认实现可以不依赖该组件，自行实现OpenImageService然后注入。
    implementation "com.bytedance.ies.ugc.aweme:openpicasso-china-external:$open_sdk_version"
//    implementation 'com.github.bumptech.glide:glide:4.12.0'

}

flutter {
    source '../..'
}

/// 自动化打包
/// https://juejin.cn/post/7075259386733527048
def dartEnvironmentVariables = [
        CHANNEL: 'YYB',
        DEBUG: '',
]

if (project.hasProperty('dart-defines')) {
    dartEnvironmentVariables = dartEnvironmentVariables + project.property('dart-defines')
            .split(',')
            .collectEntries { entry ->
                def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
                [(pair.first()): pair.last()]
            }
}

def static releaseTime() {
    return new Date().format("MMddHHmm", TimeZone.getTimeZone("GMT+8"))
}

//例子：打包APK时修改文件名带上渠道参数，还有一些SDK也可以通过这种方式设置参数
//dartEnvironmentVariables.CHANNEL 使用参数
android{
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def outputFile = output.outputFile
                    if (outputFile.name.contains("release")) {
                        outputFileName = "APP_${releaseTime()}_${dartEnvironmentVariables.CHANNEL}.apk"
                    }
            }
    }
}