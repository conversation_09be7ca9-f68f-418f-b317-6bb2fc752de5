// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN
buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
    }
}

allprojects {
   repositories {
      google()
      jcenter()
      flatDir {
        dirs 'libs'
      }
   }
}

apply plugin: 'com.android.library'

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //
    api project('unity-android-resources')
    api files('libs\\unity_common.jar')
    api files('libs\\unity-classes.jar')
}

android {
    compileSdkVersion 29
    buildToolsVersion '28.0.3'

    defaultConfig {
        targetSdkVersion 29
        
        versionCode 3
        versionName "1.3"
        consumerProguardFiles 'proguard-unity.txt', 'proguard-user.txt'
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        //noCompress '.unity3d', '.ress', '.resource', '.obb'
        noCompress = ['.ress', '.resource', '.obb'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-unity.txt', 'proguard-user.txt'
            jniDebuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-unity.txt', 'proguard-user.txt'
        }
    }
}
