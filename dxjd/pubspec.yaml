name: dxjd
description: "dxjd app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.9+24

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_screenutil: ^5.9.0
  routemaster: ^1.0.1
  # 微信分享授权，不含支付
  fluwx: ^4.4.3 # WeChat
  # sign in  with apple
  sign_in_with_apple: ^6.0.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_easyloading: ^3.0.5
#  # umeng_push:
#  umeng_push_sdk: ^2.2.0
  # ali 号码认证
  ali_auth: ^1.3.0
  app_links: ^6.4.0
  pausable_timer: ^3.1.0+3
  # mPass小程序容器
  mop:
    git:
      url: https://github.com/Kayouyou/mop-flutter-sdk.git
  key_value_storage:
    path: packages/key_value_storage
  api:
    path: packages/api
  user_repository:
    path: packages/user_repository
  push_repository:
    path: packages/push_repository
  timing_repository:
    path: packages/timing_repository
  home_repository:
    path: packages/home_repository
  component_library:
    path: packages/component_library
  dxjd_test:
    path: packages/features/TEST
  login:
    path: packages/features/login
  home:
    path: packages/features/home
  mine:
    path: packages/features/mine
  domain_models:
    path: packages/domain_models
  oss_repository:
    path: packages/oss_repository
  tools:
    path: packages/tools
  timing:
    path: packages/timing
  flutter_keyboard_visibility: ^6.0.0
  beizi_ad_sdk:
    path: plugins/beizi_ad_sdk
#  ##极光推送
  jpush_flutter: ^3.0.6
  get: ^4.7.2
  http: ^0.13.6
  package_info_plus: ^7.0.0
  device_info_plus: ^10.0.1
  flutter_html: ^3.0.0-beta.1
  url_launcher: ^6.3.0
  flutter_barrage_craft: ^1.0.1
  qr_flutter: ^4.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  flutter_launcher_icons: "^0.13.1"

fluwx:
  app_id: 'wx3628d3e48d1e93a4'
  debug_logging: true # Logging in debug mode.
  android:
  #    interrupt_wx_request: true # Defaults to true.
  #    flutter_activity: 'MainActivity' # Defaults to app's launcher
  ios:
    universal_link: https://dxjk.daxiangjd.com/app/
#    scene_delegate: true # Defaults to false.
#    no_pay: false # Set to false to disable payment.
#    ignore_security: true # Set to true to disable security seetings.

flutter_launcher_icons:
  android: true #"launcher_icon"
  ios: true
  image_path: "assets/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true  # 添加这一行
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/home_img/
    - assets/data/
    - assets/camera/
    - assets/db/
    - assets/train/
    - assets/exercise/
    - assets/quiz/
    - assets/timing/


  fonts:
    - family: PingFangSC-Regular
      fonts:
        - asset: assets/fonts/PingFangSC-Regular.TTF

    - family: PingFangSC-Medium
      fonts:
        - asset: assets/fonts/PingFangSC-Medium.TTF

    - family: PingFangSC-Semibold
      fonts:
        - asset: assets/fonts/PingFangSC-Semi-bold.TTF
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
