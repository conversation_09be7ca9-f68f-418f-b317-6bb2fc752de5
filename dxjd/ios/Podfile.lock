PODS:
  - Ads-CN-Beta/BUAdSDK (6.9.0.3)
  - Ads-CN-Beta/CSJMediation (6.9.0.3):
    - Ads-CN-Beta/BUAdSDK
  - AdScopeFoundation (5.1.1.2):
    - AdScopeFoundation/AdScopeFoundation (= 5.1.1.2)
  - AdScopeFoundation/AdScopeFoundation (5.1.1.2)
  - ali_auth (1.3.0):
    - Flutter
    - MBProgressHUD
    - MJExtension
    - SDWebImage
  - AMap3DMap (10.1.200):
    - AMapFoundation (>= 1.8.0)
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - amap_flutter_map (0.0.1):
    - AMap3DMap
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - AMPSAdSDK (5.1.0.27):
    - AMPSAdSDK/AMPSAdSDK (= 5.1.0.27)
  - AMPSAdSDK/AMPSAdSDK (5.1.0.27):
    - AdScopeFoundation (~> 5.1.1.2)
  - AMPSASNPAdapter (5.1.5200.1):
    - AMPSASNPAdapter/AMPSASNPAdapter (= 5.1.5200.1)
  - AMPSASNPAdapter/AMPSASNPAdapter (5.1.5200.1):
    - ASNPAdSDK (~> 5.2.0.0)
  - AMPSBDAdapter (5.1.5390.0):
    - AMPSBDAdapter/AMPSBDAdapter (= 5.1.5390.0)
  - AMPSBDAdapter/AMPSBDAdapter (5.1.5390.0):
    - BaiduMobAdSDK
  - AMPSBZAdapter (5.1.490436.2):
    - AMPSBZAdapter/AMPSBZAdapter (= 5.1.490436.2)
  - AMPSBZAdapter/AMPSBZAdapter (5.1.490436.2):
    - BeiZiSDK-iOS/BeiZiSDK-iOS (~> 4.90.4.36)
  - AMPSCSJAdapter (5.1.6901.0):
    - AMPSCSJAdapter/AMPSCSJAdapter (= 5.1.6901.0)
  - AMPSCSJAdapter/AMPSCSJAdapter (5.1.6901.0):
    - Ads-CN-Beta/BUAdSDK
    - Ads-CN-Beta/CSJMediation
  - AMPSGDTAdapter (5.1.41540.1):
    - AMPSGDTAdapter/AMPSGDTAdapter (= 5.1.41540.1)
  - AMPSGDTAdapter/AMPSGDTAdapter (5.1.41540.1):
    - GDTMobSDK (~> 4.15.40)
  - AMPSJDAdapter (5.1.268.0):
    - AMPSJDAdapter/AMPSJDAdapter (= 5.1.268.0)
  - AMPSJDAdapter/AMPSJDAdapter (5.1.268.0):
    - JADYun
  - AMPSKSAdapter (5.1.3376.1):
    - AMPSKSAdapter/AMPSKSAdapter (= 5.1.3376.1)
  - AMPSKSAdapter/AMPSKSAdapter (5.1.3376.1):
    - KSAdSDK
  - AMPSQMAdapter (5.1.134.0):
    - AMPSQMAdapter/AMPSQMAdapter (= 5.1.134.0)
  - AMPSQMAdapter/AMPSQMAdapter (5.1.134.0):
    - QMAdSDK
  - AMPSSGAdapter (5.1.4182.0):
    - AMPSSGAdapter/AMPSSGAdapter (= 5.1.4182.0)
  - AMPSSGAdapter/AMPSSGAdapter (5.1.4182.0):
    - SigmobAd-iOS
  - app_links (0.0.2):
    - Flutter
  - ASNPAdSDK (5.2.0.2):
    - ASNPAdSDK/ASNPAdSDK (= 5.2.0.2)
  - ASNPAdSDK/ASNPAdSDK (5.2.0.2):
    - AdScopeFoundation (~> 5.1.1.1)
  - audioplayers_darwin (0.0.1):
    - Flutter
  - BaiduMobAdSDK (5.391)
  - beizi_ad_sdk (0.0.1):
    - AMPSAdSDK (~> 5.1.0.27)
    - Flutter
  - BeiZiSDK-iOS/BeiZiSDK-iOS (4.90.4.36)
  - camera_avfoundation (0.0.1):
    - Flutter
  - camerawesome (0.0.1):
    - Flutter
  - CocoaAsyncSocket (7.6.5)
  - connectivity (0.0.1):
    - Flutter
    - Reachability
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DouyinOpenSDK (4.2.4)
  - FinApplet (2.46.5)
  - FinAppletExt (2.46.5):
    - FinApplet (= 2.46.5)
  - Flutter (1.0.0)
  - flutter_barrage_craft (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inapp_purchase (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_image (0.0.1):
    - Flutter
  - flutter_secure_storage (3.3.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - FMDB/SQLCipher (2.7.11):
    - SQLCipher (~> 4.0)
  - GDTMobSDK (4.15.40)
  - image_editor_common (1.0.0):
    - Flutter
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - JADYun (2.6.8)
  - JCore (5.0.1)
  - JPush (5.6.0):
    - JCore (>= 4.8.0)
  - jpush_flutter (0.0.2):
    - Flutter
    - JCore (>= 4.8.0)
    - JPush (= 5.6.0)
  - KSAdSDK (********)
  - KTVHTTPCache (3.0.2):
    - CocoaAsyncSocket
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MBProgressHUD (1.2.0)
  - MJExtension (3.4.2)
  - mop (0.1.1):
    - FinApplet (= 2.46.5)
    - FinAppletExt (= 2.46.5)
    - Flutter
  - native_camera (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - QMAdSDK (1.3.4)
  - Reachability (3.7.6)
  - scan (0.0.1):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SigmobAd-iOS (4.18.2):
    - SigmobAd-iOS/WindSDK (= 4.18.2)
  - SigmobAd-iOS/WindSDK (4.18.2)
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - sqflite_sqlcipher (0.0.1):
    - Flutter
    - FMDB/SQLCipher (~> 2.7.5)
    - SQLCipher (= 4.5.4)
  - SQLCipher (4.5.4):
    - SQLCipher/standard (= 4.5.4)
  - SQLCipher/common (4.5.4)
  - SQLCipher/standard (4.5.4):
    - SQLCipher/common
  - Toast (4.1.1)
  - UMAPM (2.0.4):
    - UMCommon
  - UMCCommonLog (2.0.2)
  - UMCommon (7.5.2):
    - UMDevice
  - UMDevice (3.4.0)
  - UMShare/Core (6.10.14):
    - UMCommon
  - UMShare/UI (6.10.14):
    - UMCommon
    - UMShare/Core
  - UMShareWeChat (1.0.0):
    - UMAPM
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMShare/UI
    - UMShareWeChat/SocialLibraries (= 1.0.0)
    - WechatOpenSDK
  - UMShareWeChat/SocialLibraries (1.0.0):
    - UMAPM
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMShare/UI
    - WechatOpenSDK
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_cache (0.0.1):
    - Flutter
    - KTVHTTPCache (~> 3.0.0)
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - WechatOpenSDK (2.0.4)
  - WechatOpenSDK-XCFramework (2.0.4)

DEPENDENCIES:
  - ali_auth (from `.symlinks/plugins/ali_auth/ios`)
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - amap_flutter_map (from `.symlinks/plugins/amap_flutter_map/ios`)
  - AMPSAdSDK (~> 5.1.0.27)
  - AMPSASNPAdapter (~> 5.1.5200.1)
  - AMPSBDAdapter (~> 5.1.5390.0)
  - AMPSBZAdapter (~> 5.1.490436.2)
  - AMPSCSJAdapter (~> 5.1.6901.0)
  - AMPSGDTAdapter (~> 5.1.41540.1)
  - AMPSJDAdapter (~> 5.1.268.0)
  - AMPSKSAdapter (~> 5.1.3376.1)
  - AMPSQMAdapter (~> 5.1.134.0)
  - AMPSSGAdapter (~> 5.1.4182.0)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - beizi_ad_sdk (from `.symlinks/plugins/beizi_ad_sdk/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - camerawesome (from `.symlinks/plugins/camerawesome/ios`)
  - connectivity (from `.symlinks/plugins/connectivity/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - DouyinOpenSDK
  - Flutter (from `Flutter`)
  - flutter_barrage_craft (from `.symlinks/plugins/flutter_barrage_craft/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inapp_purchase (from `.symlinks/plugins/flutter_inapp_purchase/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_image (from `.symlinks/plugins/flutter_native_image/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_editor_common (from `.symlinks/plugins/image_editor_common/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - JCore
  - JPush
  - jpush_flutter (from `.symlinks/plugins/jpush_flutter/ios`)
  - mop (from `.symlinks/plugins/mop/ios`)
  - native_camera (from `.symlinks/plugins/native_camera/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - sqflite_sqlcipher (from `.symlinks/plugins/sqflite_sqlcipher/ios`)
  - UMCommon
  - UMDevice
  - UMShare/UI
  - UMShareWeChat
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_cache (from `.symlinks/plugins/video_cache/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - Ads-CN-Beta
    - AdScopeFoundation
    - AMap3DMap
    - AMapFoundation
    - AMapLocation
    - AMPSAdSDK
    - AMPSASNPAdapter
    - AMPSBDAdapter
    - AMPSBZAdapter
    - AMPSCSJAdapter
    - AMPSGDTAdapter
    - AMPSJDAdapter
    - AMPSKSAdapter
    - AMPSQMAdapter
    - AMPSSGAdapter
    - ASNPAdSDK
    - BaiduMobAdSDK
    - BeiZiSDK-iOS
    - CocoaAsyncSocket
    - DouyinOpenSDK
    - FinApplet
    - FinAppletExt
    - FMDB
    - GDTMobSDK
    - JADYun
    - JCore
    - JPush
    - KSAdSDK
    - KTVHTTPCache
    - libwebp
    - Mantle
    - MBProgressHUD
    - MJExtension
    - QMAdSDK
    - Reachability
    - SDWebImage
    - SDWebImageWebPCoder
    - SigmobAd-iOS
    - SQLCipher
    - Toast
    - UMAPM
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMShare
    - UMShareWeChat
    - WechatOpenSDK
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  ali_auth:
    :path: ".symlinks/plugins/ali_auth/ios"
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  amap_flutter_map:
    :path: ".symlinks/plugins/amap_flutter_map/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  beizi_ad_sdk:
    :path: ".symlinks/plugins/beizi_ad_sdk/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  camerawesome:
    :path: ".symlinks/plugins/camerawesome/ios"
  connectivity:
    :path: ".symlinks/plugins/connectivity/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_barrage_craft:
    :path: ".symlinks/plugins/flutter_barrage_craft/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inapp_purchase:
    :path: ".symlinks/plugins/flutter_inapp_purchase/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_image:
    :path: ".symlinks/plugins/flutter_native_image/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_editor_common:
    :path: ".symlinks/plugins/image_editor_common/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  jpush_flutter:
    :path: ".symlinks/plugins/jpush_flutter/ios"
  mop:
    :path: ".symlinks/plugins/mop/ios"
  native_camera:
    :path: ".symlinks/plugins/native_camera/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  sqflite_sqlcipher:
    :path: ".symlinks/plugins/sqflite_sqlcipher/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_cache:
    :path: ".symlinks/plugins/video_cache/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  Ads-CN-Beta: 5428fcbb80e6e84d38c36034a73663bd71c109d0
  AdScopeFoundation: e78efb0d6e9bf692613469cd8aae26832004d4a2
  ali_auth: ab72f98538b3fd30006bc112cd92cee78181ee45
  AMap3DMap: 06a11a83072857d6076c14060b2e1a676182e84d
  amap_flutter_location: 44ff5beb64f42e0bf5feb402fe299dac0013af6f
  amap_flutter_map: 979e54d227cedac6c7504a2151bfbf3bcf96760a
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  AMPSAdSDK: 2c8f07ac71149de491f50269ab1a356bb4403c40
  AMPSASNPAdapter: e15d09c7e13de06dd24d05321d8609c32492a37b
  AMPSBDAdapter: c70ef813ad34498ce3ebcdc26948a9154b23f674
  AMPSBZAdapter: 6a98aa7f5588dd6335fa4f7a171c6e1723118224
  AMPSCSJAdapter: c53425747b7f41174fb1ff9837596b69c1f9e801
  AMPSGDTAdapter: 832afb52ca6dc24849b13f9f0327cde5066e4ca6
  AMPSJDAdapter: bc12088cbaefd1620a9726f42a52c3aa05dfb3d8
  AMPSKSAdapter: 74e1d6703d5ff45a4289da654319638dd8891bba
  AMPSQMAdapter: cd49c60f09f23e14834ed0a66c5137b3ab4ebea0
  AMPSSGAdapter: 3bf52631527e0760c19b39b89941eee229f006e6
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  ASNPAdSDK: 81c2ca8971702e92ec88591bd500aa80cfb29661
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  BaiduMobAdSDK: 46794a55f9f1b690dd1955dcf98e7bd2dce79d1b
  beizi_ad_sdk: 3bc971dedfb39b4975bccb6e445e0a93a9ddff36
  BeiZiSDK-iOS: 798c2fee3c650ff494ee0ee7a6a5a61d7a1a9125
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  camerawesome: 0f8628efae993cb064ec4f36322e89c237e1064f
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity: c4130b2985d4ef6fd26f9702e886bd5260681467
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  DouyinOpenSDK: a153567ba06ee0fdae56955c34482b2f2768c75d
  FinApplet: 8e523eb7f3d3e218b913558180394f1fdbc15ebb
  FinAppletExt: 1104672a2130010d3532ed107ded22f2a8a70448
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_barrage_craft: ac15b5a3d80b5e6390443fdd214201674c9a3e78
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_inapp_purchase: 5c6a1ac3f11b11d0c8c0321c0c41c1f05805e4c8
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_native_image: 9c0b7451838484458e5b0fae007b86a4c2d4bdfe
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  fluttertoast: e9a18c7be5413da53898f660530c56f35edfba9c
  fluwx: c18fd6c16b03a2187cd07d6e48e32a7801962849
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  GDTMobSDK: ec2e9b392268a556e4d5e6ade6d00885d76a2cc5
  image_editor_common: d6f6644ae4a6de80481e89fe6d0a8c49e30b4b43
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  JADYun: fe7f107f9baa699ef768fd3bb74d2b0aee70bb48
  JCore: 1c42bfa3905a0edb7edacf7e776b4bee0698bacf
  JPush: 6a2895fd4045b8f342e31c14afa7685fb2efe596
  jpush_flutter: 17ffd5498fda2dfee380068dc8f08ec0421a1d84
  KSAdSDK: 209b3b1b6b392f06cb8b67966139cf6859174455
  KTVHTTPCache: 5711692cdf9a5ecfe829b1e16577deb3ffe3dc86
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  mop: bd5ac5bdf7fdf04f838a97a56e7c3ddd45dc5039
  native_camera: 5cedf1a42f2016eb93e6cd20e5a93b1e1eef4971
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  QMAdSDK: 4af9116596cc11095bb80b194aa155e50ee6b325
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  scan: aea35bb4aa59ccc8839c576a18cd57c7d492cc86
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  SigmobAd-iOS: cf4818c551891aa718a4771cee0fd7f608c9daf9
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  sqflite_sqlcipher: 2f7e72fbda46fe3255493ba3f21ebe232ff9a243
  SQLCipher: 905b145f65f349f26da9e60a19901ad24adcd381
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  UMAPM: f038b65a3df4fe8e2df3245642d84cff7f5f63c8
  UMCCommonLog: bea707e50c85cef4b0eb47cc5c7226bb843245ca
  UMCommon: 72513a01ebca2dead52f2112b4d7c6196dbbe412
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  UMShare: a33ebb09c2b2a39fee1f3d09a3977149d7822997
  UMShareWeChat: 324236f7d6d946c47c92300dcbea8a6ff39c5c59
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_cache: 5254cb75c74f3ea0dd22bc1fa7aa7fc1a731a45a
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 78ec7c5b202cab7761af8e2b2b3d0671be6c4ae1
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f
  WechatOpenSDK: 290989072e87b79d52225a01bb4e5e58f6f88593
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f

PODFILE CHECKSUM: 2c149f74d7681023916ed24aa900425419ad5944

COCOAPODS: 1.13.0
