# Uncomment this line to define a global platform for your project
platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup
source 'https://github.com/CocoaPods/Specs.git'
source 'https://gitee.com/XJCode250323489/AdSpec.git'
source 'https://gitee.com/jyadteam/JYSpecs.git'
target 'Runner' do
  use_frameworks!
  use_modular_headers!

  
#   pod 'AMPSAdSDK', '********'
#   pod 'AMPSAdapter', '~> ********' #全渠道，如不需要，可将此行删除，按需导入以下分渠道
#  pod 'AMPSASNPAdapter', '~> 5.1.5107.7'
#  pod 'AMPSGDTAdapter', '~> 5.1.41476.1'
#  pod 'AMPSKSAdapter', '~> 5.1.33645.1'
#  pod 'AMPSGMAdapter', '~> 5.1.6110.1'
#  pod 'AMPSCSJAdapter', '~> 5.1.6110.1'
#  pod 'AMPSBDAdapter', '~> 5.1.5352.0'
#  pod 'AMPSBZAdapter', '~> 5.1.490411.0'

  pod 'JPush'
#   pod 'JOperate'
  pod 'JCore'
  pod 'UMCommon'    #必须集成，由原来的UMCCommon变为了UMCommon
  pod 'UMDevice'       #必须集成
  # U-Share SDK UI模块（分享面板，建议添加）
  pod 'UMShare/UI'
  #  集成微信
  pod 'UMShareWeChat'
  pod 'DouyinOpenSDK'
  pod 'AMPSAdSDK', '~> 5.1.0.27'
  #按需导入以下渠道
  pod 'AMPSASNPAdapter', '~> 5.1.5200.1' #asnp
  pod 'AMPSBZAdapter', '~> 5.1.490436.2'  #倍孜
  pod 'AMPSGDTAdapter', '~> 5.1.41540.1' # 优量汇
  pod 'AMPSKSAdapter', '~> 5.1.3376.1'  #快手
  pod 'AMPSBDAdapter', '~> 5.1.5390.0'  #百度
  pod 'AMPSSGAdapter', '~> 5.1.4182.0'  #sigmob
  pod 'AMPSCSJAdapter', '~> 5.1.6901.0' #穿山甲
  pod 'AMPSJDAdapter', '~> 5.1.268.0'   #京东
  pod 'AMPSQMAdapter', '~> 5.1.134.0'   #趣盟
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

# post_install do |installer|
#   installer.pods_project.targets.each do |target|
#     flutter_additional_ios_build_settings(target)
#   end
# end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
     target.build_configurations.each do |config|
       config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
                        '$(inherited)',
                        'PERMISSION_CAMERA=1',
                         'PERMISSION_PHOTOS=1',
                         'PERMISSION_LOCATION=1',
                         #'PERMISSION_MICROPHONE=1',
       ]
       config.build_settings['ENABLE_BITCODE'] = 'NO'
       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
       config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
     end
  end
end
