// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		04E9D2873F6398CE3F2363A5 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6FD787DBC4534CA25E9E0C9 /* Pods_RunnerTests.framework */; };
		0F7AECAE2C73423900310D6F /* Device.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECAD2C73423900310D6F /* Device.swift */; };
		0F7AECB12C73442A00310D6F /* UnityHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECB02C73442A00310D6F /* UnityHelper.m */; };
		0F7AECD62C734D1A00310D6F /* TakePhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECCE2C734D1A00310D6F /* TakePhotoView.m */; };
		0F7AECD72C734D1A00310D6F /* TakePhotoViewFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECD02C734D1A00310D6F /* TakePhotoViewFactory.m */; };
		0F7AECD82C734D1A00310D6F /* TakePhotoViewPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECD22C734D1A00310D6F /* TakePhotoViewPlugin.m */; };
		0F7AECD92C734D1A00310D6F /* TakePhotoViewRegistran.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F7AECD42C734D1A00310D6F /* TakePhotoViewRegistran.m */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		4357983FE971A0F18CE613E9 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EB4E81CE4439ED418B21F196 /* Pods_Runner.framework */; };
		6C806D942D92D0E400F60FD8 /* ImageDownloader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C806D932D92D0E400F60FD8 /* ImageDownloader.swift */; };
		6CA82FA42DE0534600A6283B /* UnityFramework.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA82FA32DE0534600A6283B /* UnityFramework.framework */; };
		6CA82FA52DE0534600A6283B /* UnityFramework.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA82FA32DE0534600A6283B /* UnityFramework.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		6CCA032D2DCF52D100B67F9A /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A71A2D9A2D7A00BD3A80 /* SwiftUI.framework */; };
		6CD4A71D2D9A2EC300BD3A80 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A71C2D9A2EC300BD3A80 /* Accelerate.framework */; };
		6CD4A71F2D9A2ED900BD3A80 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A71E2D9A2ED900BD3A80 /* AudioToolbox.framework */; };
		6CD4A7212D9A2EE000BD3A80 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7202D9A2EE000BD3A80 /* AVFoundation.framework */; };
		6CD4A7232D9A2EE400BD3A80 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7222D9A2EE400BD3A80 /* CoreGraphics.framework */; };
		6CD4A7252D9A2EE900BD3A80 /* CoreImage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7242D9A2EE900BD3A80 /* CoreImage.framework */; };
		6CD4A7272D9A2EF000BD3A80 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7262D9A2EF000BD3A80 /* CoreLocation.framework */; };
		6CD4A7292D9A2EF500BD3A80 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7282D9A2EF500BD3A80 /* CoreMedia.framework */; };
		6CD4A72B2D9A2EFA00BD3A80 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A72A2D9A2EFA00BD3A80 /* CoreMotion.framework */; };
		6CD4A72D2D9A2F0000BD3A80 /* CoreML.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A72C2D9A2F0000BD3A80 /* CoreML.framework */; };
		6CD4A72F2D9A2F0B00BD3A80 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A72E2D9A2F0B00BD3A80 /* CoreText.framework */; };
		6CD4A7312D9A2F1800BD3A80 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7302D9A2F1800BD3A80 /* ImageIO.framework */; };
		6CD4A7332D9A2F2000BD3A80 /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7322D9A2F2000BD3A80 /* JavaScriptCore.framework */; };
		6CD4A7352D9A2F2800BD3A80 /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7342D9A2F2800BD3A80 /* MapKit.framework */; };
		6CD4A7372D9A2F2C00BD3A80 /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7362D9A2F2C00BD3A80 /* MediaPlayer.framework */; };
		6CD4A7392D9A2F3100BD3A80 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7382D9A2F3100BD3A80 /* MobileCoreServices.framework */; };
		6CD4A73B2D9A2F3600BD3A80 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A73A2D9A2F3600BD3A80 /* QuartzCore.framework */; };
		6CD4A73C2D9A2F7D00BD3A80 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF26022E2C3B956300B2B567 /* UIKit.framework */; };
		6CD4A73E2D9A2F8300BD3A80 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A73D2D9A2F8300BD3A80 /* WebKit.framework */; };
		6CD4A7402D9A2F8800BD3A80 /* DeviceCheck.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A73F2D9A2F8800BD3A80 /* DeviceCheck.framework */; };
		6CD4A7422D9A2F9F00BD3A80 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7412D9A2F9F00BD3A80 /* libbz2.tbd */; };
		6CD4A7442D9A2FA500BD3A80 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7432D9A2FA500BD3A80 /* libiconv.tbd */; };
		6CD4A7462D9A2FAC00BD3A80 /* libresolv.9.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CD4A7452D9A2FAC00BD3A80 /* libresolv.9.tbd */; };
		6CD4A74B2D9A6E3400BD3A80 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602262C3B954500B2B567 /* Foundation.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		EF26021F2C3B94BB00B2B567 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = EF26021E2C3B94BB00B2B567 /* PrivacyInfo.xcprivacy */; };
		EF2602212C3B951300B2B567 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602202C3B951300B2B567 /* AdSupport.framework */; };
		EF2602232C3B952A00B2B567 /* AppTrackingTransparency.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602222C3B952A00B2B567 /* AppTrackingTransparency.framework */; };
		EF2602252C3B953300B2B567 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602242C3B953300B2B567 /* CoreTelephony.framework */; };
		EF2602292C3B954D00B2B567 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602282C3B954D00B2B567 /* SystemConfiguration.framework */; };
		EF26022B2C3B955500B2B567 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF26022A2C3B955500B2B567 /* Security.framework */; };
		EF26022D2C3B955C00B2B567 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EF26022C2C3B955B00B2B567 /* StoreKit.framework */; };
		EF2602312C3B95B400B2B567 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602302C3B95B400B2B567 /* libc++.tbd */; };
		EF2602332C3B95BF00B2B567 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602322C3B95BF00B2B567 /* libz.tbd */; };
		EF2602352C3B95C900B2B567 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602342C3B95C900B2B567 /* libsqlite3.tbd */; };
		EF2602372C3B95D300B2B567 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602362C3B95D300B2B567 /* libxml2.tbd */; };
		EF2602392C3B95DE00B2B567 /* libc++abi.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EF2602382C3B95DE00B2B567 /* libc++abi.tbd */; };
		EF26023E2C3B994D00B2B567 /* SplashManager.m in Sources */ = {isa = PBXBuildFile; fileRef = EF26023D2C3B994D00B2B567 /* SplashManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		6CA82FA62DE0534600A6283B /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				6CA82FA52DE0534600A6283B /* UnityFramework.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		06ADB46373FE26031AB865A3 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		0F7AECAD2C73423900310D6F /* Device.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Device.swift; sourceTree = "<group>"; };
		0F7AECAF2C73442A00310D6F /* UnityHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityHelper.h; sourceTree = "<group>"; };
		0F7AECB02C73442A00310D6F /* UnityHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UnityHelper.m; sourceTree = "<group>"; };
		0F7AECCD2C734D1A00310D6F /* TakePhotoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TakePhotoView.h; sourceTree = "<group>"; };
		0F7AECCE2C734D1A00310D6F /* TakePhotoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TakePhotoView.m; sourceTree = "<group>"; };
		0F7AECCF2C734D1A00310D6F /* TakePhotoViewFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TakePhotoViewFactory.h; sourceTree = "<group>"; };
		0F7AECD02C734D1A00310D6F /* TakePhotoViewFactory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TakePhotoViewFactory.m; sourceTree = "<group>"; };
		0F7AECD12C734D1A00310D6F /* TakePhotoViewPlugin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TakePhotoViewPlugin.h; sourceTree = "<group>"; };
		0F7AECD22C734D1A00310D6F /* TakePhotoViewPlugin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TakePhotoViewPlugin.m; sourceTree = "<group>"; };
		0F7AECD32C734D1A00310D6F /* TakePhotoViewRegistran.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TakePhotoViewRegistran.h; sourceTree = "<group>"; };
		0F7AECD42C734D1A00310D6F /* TakePhotoViewRegistran.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TakePhotoViewRegistran.m; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		28E35128D0D1869D66016DE3 /* Runner.entitlements */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3CC06E028E14125A68FE6D72 /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		4EB385B2F6B1380A273A41DF /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		6C7DB50C2DD21C9700DAC7B9 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		6C7DB50E2DD21E5700DAC7B9 /* jcore-ios-5.0.1.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = "jcore-ios-5.0.1.xcframework"; path = "Pods/JCore/jcore-ios-5.0.1.xcframework"; sourceTree = "<group>"; };
		6C806D932D92D0E400F60FD8 /* ImageDownloader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDownloader.swift; sourceTree = "<group>"; };
		6CA82FA32DE0534600A6283B /* UnityFramework.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UnityFramework.framework; sourceTree = "<group>"; };
		6CD4A71A2D9A2D7A00BD3A80 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		6CD4A71C2D9A2EC300BD3A80 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		6CD4A71E2D9A2ED900BD3A80 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		6CD4A7202D9A2EE000BD3A80 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		6CD4A7222D9A2EE400BD3A80 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		6CD4A7242D9A2EE900BD3A80 /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		6CD4A7262D9A2EF000BD3A80 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		6CD4A7282D9A2EF500BD3A80 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		6CD4A72A2D9A2EFA00BD3A80 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		6CD4A72C2D9A2F0000BD3A80 /* CoreML.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreML.framework; path = System/Library/Frameworks/CoreML.framework; sourceTree = SDKROOT; };
		6CD4A72E2D9A2F0B00BD3A80 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		6CD4A7302D9A2F1800BD3A80 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		6CD4A7322D9A2F2000BD3A80 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		6CD4A7342D9A2F2800BD3A80 /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		6CD4A7362D9A2F2C00BD3A80 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		6CD4A7382D9A2F3100BD3A80 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		6CD4A73A2D9A2F3600BD3A80 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		6CD4A73D2D9A2F8300BD3A80 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		6CD4A73F2D9A2F8800BD3A80 /* DeviceCheck.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DeviceCheck.framework; path = System/Library/Frameworks/DeviceCheck.framework; sourceTree = SDKROOT; };
		6CD4A7412D9A2F9F00BD3A80 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		6CD4A7432D9A2FA500BD3A80 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		6CD4A7452D9A2FAC00BD3A80 /* libresolv.9.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.9.tbd; path = usr/lib/libresolv.9.tbd; sourceTree = SDKROOT; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7C6B56891444A0345B2A9E12 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B6FD787DBC4534CA25E9E0C9 /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CA4F5E6A82912E7710CB5E8E /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		E72E5957C768638464A03645 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		EB4E81CE4439ED418B21F196 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EF26021E2C3B94BB00B2B567 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		EF2602202C3B951300B2B567 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		EF2602222C3B952A00B2B567 /* AppTrackingTransparency.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppTrackingTransparency.framework; path = System/Library/Frameworks/AppTrackingTransparency.framework; sourceTree = SDKROOT; };
		EF2602242C3B953300B2B567 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		EF2602262C3B954500B2B567 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		EF2602282C3B954D00B2B567 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		EF26022A2C3B955500B2B567 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		EF26022C2C3B955B00B2B567 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		EF26022E2C3B956300B2B567 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		EF2602302C3B95B400B2B567 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		EF2602322C3B95BF00B2B567 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		EF2602342C3B95C900B2B567 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		EF2602362C3B95D300B2B567 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		EF2602382C3B95DE00B2B567 /* libc++abi.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++abi.tbd"; path = "usr/lib/libc++abi.tbd"; sourceTree = SDKROOT; };
		EF26023C2C3B994D00B2B567 /* SplashManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SplashManager.h; sourceTree = "<group>"; };
		EF26023D2C3B994D00B2B567 /* SplashManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SplashManager.m; sourceTree = "<group>"; };
		EFC607112C085630009F3887 /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		EFDBA0CF2C1A8AE7006E8C6C /* RunnerProfile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerProfile.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6CD4A7462D9A2FAC00BD3A80 /* libresolv.9.tbd in Frameworks */,
				6CD4A7442D9A2FA500BD3A80 /* libiconv.tbd in Frameworks */,
				6CCA032D2DCF52D100B67F9A /* SwiftUI.framework in Frameworks */,
				6CD4A7422D9A2F9F00BD3A80 /* libbz2.tbd in Frameworks */,
				6CD4A7402D9A2F8800BD3A80 /* DeviceCheck.framework in Frameworks */,
				6CD4A73E2D9A2F8300BD3A80 /* WebKit.framework in Frameworks */,
				6CD4A73C2D9A2F7D00BD3A80 /* UIKit.framework in Frameworks */,
				EF2602292C3B954D00B2B567 /* SystemConfiguration.framework in Frameworks */,
				EF26022D2C3B955C00B2B567 /* StoreKit.framework in Frameworks */,
				EF26022B2C3B955500B2B567 /* Security.framework in Frameworks */,
				6CD4A73B2D9A2F3600BD3A80 /* QuartzCore.framework in Frameworks */,
				6CD4A7392D9A2F3100BD3A80 /* MobileCoreServices.framework in Frameworks */,
				6CD4A7372D9A2F2C00BD3A80 /* MediaPlayer.framework in Frameworks */,
				6CD4A7352D9A2F2800BD3A80 /* MapKit.framework in Frameworks */,
				6CD4A7332D9A2F2000BD3A80 /* JavaScriptCore.framework in Frameworks */,
				6CA82FA42DE0534600A6283B /* UnityFramework.framework in Frameworks */,
				6CD4A7312D9A2F1800BD3A80 /* ImageIO.framework in Frameworks */,
				6CD4A72F2D9A2F0B00BD3A80 /* CoreText.framework in Frameworks */,
				EF2602252C3B953300B2B567 /* CoreTelephony.framework in Frameworks */,
				6CD4A72D2D9A2F0000BD3A80 /* CoreML.framework in Frameworks */,
				6CD4A72B2D9A2EFA00BD3A80 /* CoreMotion.framework in Frameworks */,
				6CD4A7292D9A2EF500BD3A80 /* CoreMedia.framework in Frameworks */,
				6CD4A7272D9A2EF000BD3A80 /* CoreLocation.framework in Frameworks */,
				6CD4A7252D9A2EE900BD3A80 /* CoreImage.framework in Frameworks */,
				6CD4A7232D9A2EE400BD3A80 /* CoreGraphics.framework in Frameworks */,
				6CD4A7212D9A2EE000BD3A80 /* AVFoundation.framework in Frameworks */,
				6CD4A74B2D9A6E3400BD3A80 /* Foundation.framework in Frameworks */,
				6CD4A71F2D9A2ED900BD3A80 /* AudioToolbox.framework in Frameworks */,
				EF2602232C3B952A00B2B567 /* AppTrackingTransparency.framework in Frameworks */,
				EF2602212C3B951300B2B567 /* AdSupport.framework in Frameworks */,
				6CD4A71D2D9A2EC300BD3A80 /* Accelerate.framework in Frameworks */,
				EF2602392C3B95DE00B2B567 /* libc++abi.tbd in Frameworks */,
				EF2602372C3B95D300B2B567 /* libxml2.tbd in Frameworks */,
				EF2602352C3B95C900B2B567 /* libsqlite3.tbd in Frameworks */,
				EF2602332C3B95BF00B2B567 /* libz.tbd in Frameworks */,
				EF2602312C3B95B400B2B567 /* libc++.tbd in Frameworks */,
				4357983FE971A0F18CE613E9 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A58B4E11D46ACB5FDDBB309F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				04E9D2873F6398CE3F2363A5 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0F7AECD52C734D1A00310D6F /* TakePhoto */ = {
			isa = PBXGroup;
			children = (
				0F7AECCD2C734D1A00310D6F /* TakePhotoView.h */,
				0F7AECCE2C734D1A00310D6F /* TakePhotoView.m */,
				0F7AECCF2C734D1A00310D6F /* TakePhotoViewFactory.h */,
				0F7AECD02C734D1A00310D6F /* TakePhotoViewFactory.m */,
				0F7AECD12C734D1A00310D6F /* TakePhotoViewPlugin.h */,
				0F7AECD22C734D1A00310D6F /* TakePhotoViewPlugin.m */,
				0F7AECD32C734D1A00310D6F /* TakePhotoViewRegistran.h */,
				0F7AECD42C734D1A00310D6F /* TakePhotoViewRegistran.m */,
			);
			path = TakePhoto;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		3D5C7F1E8322025F877F2091 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6CA82FA32DE0534600A6283B /* UnityFramework.framework */,
				6C7DB50E2DD21E5700DAC7B9 /* jcore-ios-5.0.1.xcframework */,
				6C7DB50C2DD21C9700DAC7B9 /* CFNetwork.framework */,
				6CD4A7452D9A2FAC00BD3A80 /* libresolv.9.tbd */,
				6CD4A7432D9A2FA500BD3A80 /* libiconv.tbd */,
				6CD4A7412D9A2F9F00BD3A80 /* libbz2.tbd */,
				6CD4A73F2D9A2F8800BD3A80 /* DeviceCheck.framework */,
				6CD4A73D2D9A2F8300BD3A80 /* WebKit.framework */,
				6CD4A73A2D9A2F3600BD3A80 /* QuartzCore.framework */,
				6CD4A7382D9A2F3100BD3A80 /* MobileCoreServices.framework */,
				6CD4A7362D9A2F2C00BD3A80 /* MediaPlayer.framework */,
				6CD4A7342D9A2F2800BD3A80 /* MapKit.framework */,
				6CD4A7322D9A2F2000BD3A80 /* JavaScriptCore.framework */,
				6CD4A7302D9A2F1800BD3A80 /* ImageIO.framework */,
				6CD4A72E2D9A2F0B00BD3A80 /* CoreText.framework */,
				6CD4A72C2D9A2F0000BD3A80 /* CoreML.framework */,
				6CD4A72A2D9A2EFA00BD3A80 /* CoreMotion.framework */,
				6CD4A7282D9A2EF500BD3A80 /* CoreMedia.framework */,
				6CD4A7262D9A2EF000BD3A80 /* CoreLocation.framework */,
				6CD4A7242D9A2EE900BD3A80 /* CoreImage.framework */,
				6CD4A7222D9A2EE400BD3A80 /* CoreGraphics.framework */,
				6CD4A7202D9A2EE000BD3A80 /* AVFoundation.framework */,
				6CD4A71E2D9A2ED900BD3A80 /* AudioToolbox.framework */,
				6CD4A71C2D9A2EC300BD3A80 /* Accelerate.framework */,
				6CD4A71A2D9A2D7A00BD3A80 /* SwiftUI.framework */,
				EF2602382C3B95DE00B2B567 /* libc++abi.tbd */,
				EF2602362C3B95D300B2B567 /* libxml2.tbd */,
				EF2602342C3B95C900B2B567 /* libsqlite3.tbd */,
				EF2602322C3B95BF00B2B567 /* libz.tbd */,
				EF2602302C3B95B400B2B567 /* libc++.tbd */,
				EF26022E2C3B956300B2B567 /* UIKit.framework */,
				EF26022C2C3B955B00B2B567 /* StoreKit.framework */,
				EF26022A2C3B955500B2B567 /* Security.framework */,
				EF2602282C3B954D00B2B567 /* SystemConfiguration.framework */,
				EF2602262C3B954500B2B567 /* Foundation.framework */,
				EF2602242C3B953300B2B567 /* CoreTelephony.framework */,
				EF2602222C3B952A00B2B567 /* AppTrackingTransparency.framework */,
				EF2602202C3B951300B2B567 /* AdSupport.framework */,
				EB4E81CE4439ED418B21F196 /* Pods_Runner.framework */,
				B6FD787DBC4534CA25E9E0C9 /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				BB22B49DA35939891A98603F /* Pods */,
				3D5C7F1E8322025F877F2091 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				0F7AECD52C734D1A00310D6F /* TakePhoto */,
				EFDBA0CF2C1A8AE7006E8C6C /* RunnerProfile.entitlements */,
				EFC607112C085630009F3887 /* RunnerDebug.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				6C806D932D92D0E400F60FD8 /* ImageDownloader.swift */,
				EF26023C2C3B994D00B2B567 /* SplashManager.h */,
				EF26023D2C3B994D00B2B567 /* SplashManager.m */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				28E35128D0D1869D66016DE3 /* Runner.entitlements */,
				EF26021E2C3B94BB00B2B567 /* PrivacyInfo.xcprivacy */,
				0F7AECAD2C73423900310D6F /* Device.swift */,
				0F7AECAF2C73442A00310D6F /* UnityHelper.h */,
				0F7AECB02C73442A00310D6F /* UnityHelper.m */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		BB22B49DA35939891A98603F /* Pods */ = {
			isa = PBXGroup;
			children = (
				06ADB46373FE26031AB865A3 /* Pods-Runner.debug.xcconfig */,
				7C6B56891444A0345B2A9E12 /* Pods-Runner.release.xcconfig */,
				E72E5957C768638464A03645 /* Pods-Runner.profile.xcconfig */,
				CA4F5E6A82912E7710CB5E8E /* Pods-RunnerTests.debug.xcconfig */,
				4EB385B2F6B1380A273A41DF /* Pods-RunnerTests.release.xcconfig */,
				3CC06E028E14125A68FE6D72 /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				563094ED97C60C2D5380643A /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				A58B4E11D46ACB5FDDBB309F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				2E7CC20EAC354B29159BE889 /* [CP] Check Pods Manifest.lock */,
				6CA82FA62DE0534600A6283B /* Embed Frameworks */,
				9286D9A7850A4485C802DBB3 /* [CP] Embed Pods Frameworks */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				8A3AC62681D1CF4A1423095A /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				EF26021F2C3B94BB00B2B567 /* PrivacyInfo.xcprivacy in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2E7CC20EAC354B29159BE889 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		563094ED97C60C2D5380643A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8A3AC62681D1CF4A1423095A /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9286D9A7850A4485C802DBB3 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0F7AECD92C734D1A00310D6F /* TakePhotoViewRegistran.m in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				0F7AECD82C734D1A00310D6F /* TakePhotoViewPlugin.m in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				0F7AECB12C73442A00310D6F /* UnityHelper.m in Sources */,
				0F7AECD72C734D1A00310D6F /* TakePhotoViewFactory.m in Sources */,
				6C806D942D92D0E400F60FD8 /* ImageDownloader.swift in Sources */,
				EF26023E2C3B994D00B2B567 /* SplashManager.m in Sources */,
				0F7AECD62C734D1A00310D6F /* TakePhotoView.m in Sources */,
				0F7AECAE2C73423900310D6F /* Device.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerProfile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 21;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/dxjd_unity/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KTVHTTPCache/KTVHTTPCache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJExtension/MJExtension.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mantle/Mantle.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Reachability/Reachability.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ali_auth/ali_auth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/amap_flutter_location/amap_flutter_location.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/beizi_ad/beizi_ad.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/camera_avfoundation/camera_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity/connectivity.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_image_compress_common/flutter_image_compress_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fluwx/fluwx.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_editor_common/image_editor_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/mop/mop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/native_camera/native_camera.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sign_in_with_apple/sign_in_with_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sqflite/sqflite.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_common_sdk/umeng_common_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_push_sdk/umeng_push_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_cache/video_cache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation/video_player_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/GDTMobSDK\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/WechatOpenSDK-XCFramework/Headers\"",
					"$(SDKROOT)/usr/include/libxml2",
					"\"$(SRCROOT)/Runner\"",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "大象驾到Pro";
				INFOPLIST_KEY_NSCameraUsageDescription = "大象驾到Pro需要访问手机相机来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册以分享图片到抖音";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "大象驾到Pro需要访问手机相册来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "大象驾到Pro需要访问您的跟踪数据，以便提供更好的个性化服务和广告";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/dxjd_unity/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
				);
				MARKETING_VERSION = 1.2.6;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxjk.xw;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA4F5E6A82912E7710CB5E8E /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4QDT8358SU;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.dxjd.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4EB385B2F6B1380A273A41DF /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4QDT8358SU;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.dxjd.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CC06E028E14125A68FE6D72 /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4QDT8358SU;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.dxjd.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 21;
				DEVELOPMENT_TEAM = 4QDT8358SU;
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/dxjd_unity/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KTVHTTPCache/KTVHTTPCache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJExtension/MJExtension.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mantle/Mantle.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Reachability/Reachability.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ali_auth/ali_auth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/amap_flutter_location/amap_flutter_location.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/beizi_ad/beizi_ad.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/camera_avfoundation/camera_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity/connectivity.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_image_compress_common/flutter_image_compress_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fluwx/fluwx.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_editor_common/image_editor_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/mop/mop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/native_camera/native_camera.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sign_in_with_apple/sign_in_with_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sqflite/sqflite.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_common_sdk/umeng_common_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_push_sdk/umeng_push_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_cache/video_cache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation/video_player_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/GDTMobSDK\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/WechatOpenSDK-XCFramework/Headers\"",
					"$(SDKROOT)/usr/include/libxml2",
					"\"$(SRCROOT)/Runner\"",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "大象驾到Pro";
				INFOPLIST_KEY_NSCameraUsageDescription = "大象驾到Pro需要访问手机相机来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册以分享图片到抖音";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "大象驾到Pro需要访问手机相册来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "大象驾到Pro需要访问您的跟踪数据，以便提供更好的个性化服务和广告";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/dxjd_unity/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
				);
				MARKETING_VERSION = 1.2.6;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxjk.xw;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 21;
				DEVELOPMENT_TEAM = 4QDT8358SU;
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/dxjd_unity/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
					"$(PROJECT_DIR)/light\\ 2/Frameworks/Plugins/iOS",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KTVHTTPCache/KTVHTTPCache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD/MBProgressHUD.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJExtension/MJExtension.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mantle/Mantle.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Reachability/Reachability.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ali_auth/ali_auth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/amap_flutter_location/amap_flutter_location.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/beizi_ad/beizi_ad.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/camera_avfoundation/camera_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity/connectivity.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_image_compress_common/flutter_image_compress_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fluwx/fluwx.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_editor_common/image_editor_common.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/mop/mop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/native_camera/native_camera.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sign_in_with_apple/sign_in_with_apple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sqflite/sqflite.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_common_sdk/umeng_common_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/umeng_push_sdk/umeng_push_sdk.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_cache/video_cache.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation/video_player_avfoundation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/GDTMobSDK\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/WechatOpenSDK-XCFramework/Headers\"",
					"$(SDKROOT)/usr/include/libxml2",
					"\"$(SRCROOT)/Runner\"",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "大象驾到Pro";
				INFOPLIST_KEY_NSCameraUsageDescription = "大象驾到Pro需要访问手机相机来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "大象驾到Pro需要访问当前位置来显示当前位置的驾校信息";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册以分享图片到抖音";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "大象驾到Pro需要访问手机相册来实现更改用户的头像和计时培训功能";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "大象驾到Pro需要访问您的跟踪数据，以便提供更好的个性化服务和广告";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/dxjd_unity/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
					"$(PROJECT_DIR)/light\\ 2/Libraries",
				);
				MARKETING_VERSION = 1.2.6;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxjk.xw;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
