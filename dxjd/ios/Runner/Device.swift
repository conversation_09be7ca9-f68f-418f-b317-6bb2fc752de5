//
//  Device.swift
//  Runner
//
//  Created by 渴望 on 2023/9/12.
//

//import UIKit
//
//class Device: NSObject {
//
//}

import UIKit
public extension UIDevice {
    
    var modelName: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        
        
//        switch identifier {
//            //2010年6月8日，第四代iPhone 4发布
//        case "iPhone3,1", "iPhone3,2", "iPhone3,3": return "iPhone 4"
//            //2011年10月4日，第五代iPhone 4S发布
//        case "iPhone4,1": return "iPhone 4s"
//            //2012年9月13日，第六代iPhone 5发布
//        case "iPhone5,1": return "iPhone 5"
//        case "iPhone5,2": return "iPhone 5"
//            //2013年9月10日，第七代iPhone 5C及iPhone 5S发布
//        case "iPhone5,3", "iPhone5,4": return "iPhone 5c"
//        case "iPhone6,1", "iPhone6,2": return "iPhone 5s"
//            //2014年9月10日，第八代iPhone 6及iPhone 6 Plus发布
//        case "iPhone7,2": return "iPhone 6"
//        case "iPhone7,1": return "iPhone 6 Plus"
//            //2015年9月10日，第九代iPhone 6S及iPhone 6S Plus发布
//        case "iPhone8,1": return "iPhone 6s"
//        case "iPhone8,2": return "iPhone 6s Plus"
//            //2016年3月21日，第十代iPhone SE发布
//        case "iPhone8,4": return "iPhone SE"
//            //2016年9月8日，第十一代iPhone 7及iPhone 7 Plus发布
//        case "iPhone9,1", "iPhone9,3": return "iPhone 7"
//        case "iPhone9,2", "iPhone9,4": return "iPhone 7 Plus"
//            //2017年9月13日，第十二代iPhone 8，iPhone 8 Plus，iPhone X发布
//        case "iPhone10,1", "iPhone10,4": return "iPhone 8"
//        case "iPhone10,2", "iPhone10,5": return "iPhone 8 Plus"
//        case "iPhone10,3", "iPhone10,6": return "iPhone X"
//            //2018年9月13日，第十三代iPhone XS，iPhone XS Max，iPhone XR发布
//        case "iPhone11,2": return "iPhone XS"
//        case "iPhone11,4", "iPhone11,6": return "iPhoneXSMax"
//        case "iPhone11,8": return "iPhone XR"
//            //2019年9月11日，第十四代iPhone 11，iPhone 11 Pro，iPhone 11 Pro Max发布
//        case "iPhone12,1": return "iPhone 11"
//        case "iPhone12,3": return "iPhone 11 Pro"
//        case "iPhone12,5": return "iPhone 11 Pro Max"
//        case "iPhone12,8": return "iPhone SE 2020"
//            //2020年10月14日，新款iPhone 12 mini、12、12 Pro、12 Pro Max发布
//        case "iPhone13,1": return "iPhone 12 mini"
//        case "iPhone13,2": return "iPhone 12"
//        case "iPhone13,3": return "iPhone 12 Pro"
//        case "iPhone13,4": return "iPhone 12 Pro Max"
//            //2021年9月15日，新款iPhone 13 mini、13、13 Pro、13 Pro Max发布
//        case "iPhone14,4": return "iPhone 13 mini"
//        case "iPhone14,5": return "iPhone 13"
//        case "iPhone14,2": return "iPhone 13 Pro"
//        case "iPhone14,3": return "iPhone 13 Pro Max"
//            //2022年3月9日，新款iPhone SE发布
//        case"iPhone14,6": return "iPhone SE 2022"
//            //2022年9月16日，新款iPhone 14 plus、14、14 Pro、14 Pro Max发布
//        case "iPhone14,7": return "iPhone 14"
//        case "iPhone14,8": return "iPhone 14 Plus"
//        case "iPhone15,2": return "iPhone 14 Pro"
//        case "iPhone15,3": return "iPhone 14 Pro Max"
//        case "iPhone15,4": return "iPhone 15"
//        case "iPhone15,5": return "iPhone 15 Plus"
//        case "iPhone16,1": return "iPhone 15 Pro"
//        case "iPhone16,2": return "iPhone 15 Pro Max"
//
//            //TODO:iPod
//        case "iPod1,1": return "iPod Touch 1G"
//        case "iPod2,1": return "iPod Touch 2G"
//        case "iPod3,1": return "iPod Touch 3G"
//        case "iPod4,1": return "iPod Touch 4G"
//        case "iPod5,1": return "iPod Touch (5 Gen)"
//        case "iPod7,1": return "iPod touch (6th generation)"
//            //2019年5月发布，更新一种机型：iPod touch (7th generation)
//        case "iPod9,1": return "iPod touch (7th generation)"
//            //TODO:iPad
//        case "iPad1,1": return "iPad 1G"
//        case "iPad2,1","iPad2,2","iPad2,3","iPad2,4": return "iPad 2"
//        case "iPad3,1","iPad3,2","iPad3,3": return "iPad 3"
//        case "iPad3,4","iPad3,5","iPad3,6": return "iPad 4"
//        case "iPad6,11": return "iPad 5 (WiFi)"
//        case "iPad6,12":   return "iPad 5 (Cellular)"
//        case "iPad7,5","iPad7,6": return "iPad (6th generation)"
//        case "iPad7,11","iPad7,12": return "iPad (7th generation)"
//        case "iPad11,6","iPad11,7": return "iPad (8th generation)"
//        case "iPad12,1","iPad12,2": return "iPad (9th generation)"
//            //TODO:iPad Air
//        case "iPad4,1","iPad4,2","iPad4,3": return "iPad Air"
//        case "iPad5,3","iPad5,4": return "iPad Air 2"
//        case "iPad11,3","iPad11,4": return "iPad Air 3"
//        case "iPad13,1","iPad13,2": return "iPad Air 4"
//        case "iPad13,16","iPad13,17": return "iPad Air 5"
//            //TODO:iPad mini
//        case "iPad2,5","iPad2,6","iPad2,7": return "iPad Mini 1G"
//        case "iPad4,4","iPad4,5","iPad4,6": return "iPad Mini 2G"
//        case "iPad4,7","iPad4,8","iPad4,9": return "iPad Mini 3"
//        case "iPad5,1","iPad5,2": return "iPad Mini 4"
//        case "iPad11,1","iPad11,2": return "iPad mini (5th generation)"
//        case "iPad14,1","iPad14,2": return "iPad mini (6th generation)"
//            //TODO:iPad Pro
//        case "iPad6,7","iPad6,8": return "iPad Pro 12.9"
//        case "iPad6,3","iPad6,4": return "iPad Pro 9.7"
//        case "iPad7,1":   return "iPad Pro 12.9 inch 2nd gen (WiFi)"
//        case "iPad7,2":   return "iPad Pro 12.9 inch 2nd gen (Cellular)"
//        case "iPad7,3":   return "iPad Pro 10.5 inch (WiFi)"
//        case "iPad7,4":  return "iPad Pro 10.5 inch (Cellular)"
//        case "iPad8,1","iPad8,2","iPad8,3","iPad8,4": return "iPad Pro (11-inch)"
//        case "iPad8,5","iPad8,6","iPad8,7","iPad8,8": return "iPad Pro (12.9-inch) (3rd generation)"
//        case "iPad8,9","iPad8,10": return "iPad Pro (11-inch) (2nd generation)"
//        case "iPad8,11","iPad8,12": return "iPad Pro (12.9-inch) (4th generation)"
//        case "iPad13,4","iPad13,5","iPad13,6","iPad13,7": return "iPad Pro (11-inch) (3rd generation)"
//        case "iPad13,8","iPad13,9","iPad13,10","iPad13,11": return "iPad Pro (12.9-inch) (5th generation)"
//
//        case "AppleTV2,1": return "Apple TV 2"
//        case "AppleTV3,1","AppleTV3,2": return "Apple TV 3"
//        case "AppleTV5,3": return "Apple TV 4"
//
//        case "i386", "x86_64": return "Simulator"
//
//        default: return identifier
//        }
         
        return identifier
    }
}

