////
////
///.m
////  Runner
////
////  Created by yyy on 2024/7/8.
////
//
//#import "SplashManager.h"
//
//
//#define kScreenWidth [[UIScreen mainScreen] bounds].size.width
//#define kScreenHeight [[UIScreen mainScreen] bounds].size.height
//
//#define kAdapt(value) ((value) * ([UIScreen mainScreen].bounds.size.width / 375.0f))
//
//#define BeiZi_id @"14659"
//#define Splash_BeiZi @"15351"
//#define RewardedVideo_BeiZi @"15355"
//
//
//
//@implementation SplashManager
//
//
//static SplashManager *sharedInstance = nil;
//+ (instancetype)sharedInstance {
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        sharedInstance = [[self alloc] init];
//    });
//    return sharedInstance;
//}
//
//- (instancetype)init {
//    self = [super init];
//    if (self) {
//        // 初始化操作
//    }
//    return self;
//}
//
//
//- (void)preloadBeiZiSplash {
//    
//  [self.splash preloadAd];
//}
//
//- (void)loadBeiZiSplash {
//    
//  [self.splash loadSplashAd];
//}
//
//- (void)ampsSplashAdLoadSuccess:(AMPSSplashAd *)splashAd {
//    
//  
//  NSLog(@"代理回调：SplashAdLoadSuccess------%d",splashAd.isReadyAd);
////    self.resultsLabel.text = [NSString stringWithFormat:@"广告请求成功，耗时:%llu", [[AdScopeDeviceInfo sharedInstance]adScopeMTimeStamp]-self.nowTime];
//  NSLog(@"成功渠道：%@，价格：%ld", self.splash.successAdInfo.adapterClassName, (long)[self.splash eCPM]);
//    for (AMPSAdLoadedInfo *info in self.splash.failAdInfoList.copy) {
////        CookieAMPSLog(@"失败渠道：%@，原因：%@",info.adapterClassName, info.errorMsg);
//    }
//    [self.splash showSplashViewInWindow:[UIApplication sharedApplication].keyWindow];
//}
//- (void)ampsSplashAdLoadFail:(AMPSSplashAd *)splashAd error:(NSError *_Nullable)error {
//  _completionHandler(2);
// 
////    self.resultsLabel.text = [NSString stringWithFormat:@"广告请求失败，耗时:%llu", [[AdScopeDeviceInfo sharedInstance]adScopeMTimeStamp]-self.nowTime];
//    NSLog(@"代理回调：SplashAdLoadFail:%@", error);
//    for (AMPSAdLoadedInfo *info in self.splash.failAdInfoList.copy) {
//      NSLog(@"失败渠道：%@，原因：%@",info.adapterClassName, info.errorMsg);
//    }
//  
//}
//- (void)ampsSplashAdDidShow:(AMPSSplashAd *)splashAd {
//  NSLog(@"代理回调：SplashAdDidShow");
//}
//- (void)ampsSplashAdExposured:(AMPSSplashAd *)splashAd {
//  NSLog(@"代理回调：SplashAdExposured");
//}
//- (void)ampsSplashAdDidClick:(AMPSSplashAd *)splashAd {
//  NSLog(@"代理回调：SplashAdDidClick");
//}
//- (void)ampsSplashAdDidClose:(AMPSSplashAd *)splashAd {
////    self.resultsLabel.text = @"广告已关闭";
//  NSLog(@"代理回调：SplashAdDidClose");
//  _completionHandler(3);
//}
//
//- (AMPSSplashAd *)splash {
//    if (!_splash) {
//      AMPSAdConfiguration *cfg = [[AMPSAdConfiguration alloc] init];
//      UIView *logoView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 120)];
//      logoView.backgroundColor = [UIColor whiteColor];
//      cfg.bottomView = logoView;
//      _splash = [[AMPSSplashAd alloc] initWithSpaceId:Splash_BeiZi adConfiguration:cfg];
//      _splash.delegate = self;
////        __weak typeof(self) weakSelf = self;
////      dispatch_async(dispatch_get_main_queue(), ^{
////          __strong typeof(self) strongSelf = weakSelf;
////                AMPSAdConfiguration *cfg = [[AMPSAdConfiguration alloc] init];
////                UIView *logoView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 120)];
////                logoView.backgroundColor = [UIColor whiteColor];
////                cfg.bottomView = logoView;
////                self.splash = [[AMPSSplashAd alloc] initWithSpaceId:Splash_BeiZi adConfiguration:cfg];
////                self.splash.delegate = self;
////            });
//    }
//    return _splash;
//}
//
//#pragma mark - ======倍姿 开屏======
//
////- (void)load_BeiZi_Splash{
////    self.splash_BeiZi = [[BeiZiSplash alloc]initWithSpaceID:Splash_BeiZi spaceParam:@"" lifeTime:5000];
////    self.splash_BeiZi.launchImage = [UIImage imageNamed:@"flash_logo1"];
////    self.splash_BeiZi.showLaunchImage = YES;
////    self.splash_BeiZi.delegate = self;
////    
////    //加载开屏广告
////    NSLog(@"\n🐯倍姿🐯\n 开始请求");
////    [self.splash_BeiZi BeiZi_loadSplashAd];
////}
///////请求成功
////- (void)BeiZi_splashDidLoadSuccess:(BeiZiSplash *)beiziSplash {
////    NSLog(@"\n🐯倍姿🐯\n 请求成功");
////    [self.splash_BeiZi BeiZi_showSplashAdWithWindow:[UIApplication sharedApplication].keyWindow];
////}
///////请求失败
////- (void)BeiZi_splash:(BeiZiSplash *)beiziSplash didFailToLoadAdWithError:(BeiZiRequestError *)error {
////    _splash_BeiZi = nil;
////    _splash_BeiZi.delegate = nil;
////    NSLog(@"\n🐯倍姿🐯\n 请求失败：%@", error);
////    
////    [self FastJump];
////}
///////展现
////- (void)BeiZi_splashDidPresentScreen:(BeiZiSplash *)beiziSplash {
////    self.isShow = YES;
////    NSLog(@"\n🐯倍姿🐯\n 开屏展现");
////}
///////点击
////- (void)BeiZi_splashDidClick:(BeiZiSplash *)beiziSplash {
////    NSLog(@"\n🐯倍姿🐯\n 开屏点击");
////}
///////消失
////- (void)BeiZi_splashDidDismissScreen:(BeiZiSplash *)beiziSplash {
////    //  如果开屏不二次创建，并且确定不再使用可置空，也可以忽略
////    _splash_BeiZi = nil;
////    _splash_BeiZi.delegate = nil;
////    self.isShow = NO;
////    NSLog(@"\n🐯倍姿🐯\n 开屏消失");
////    
////    [self FastJump];
////}
///////倒计时
////- (void)BeiZi_splashAdLifeTime:(int)lifeTime{
////    //self.skipButton.text = [NSString stringWithFormat:@"%ds跳过", lifeTime];
////}
//
////- (UIView *)BeiZi_splashBottomView{
////    UIView * v = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight*0.2)];
////    v.backgroundColor = [UIColor whiteColor];
////    
////    UIImageView * imgv = [[UIImageView alloc]initWithFrame:CGRectMake((kScreenWidth-kAdapt(165))/2, (kScreenHeight*0.2 - kAdapt(53))/2, kAdapt(165), kAdapt(53))];
////    imgv.image = [UIImage imageNamed:@"ic_ad_logo"];
////    [v addSubview:imgv];
////    
////    return v;
////}
//
//
//#pragma mark - 广告加载状态回调
//- (void)callCompletionHandlerWithResult:(NSInteger)payResult {
//    if (self.completionHandler) {
//        self.completionHandler(payResult);
//    }
//}
//
////#pragma mark - 获取当前活动控制器
////- (UIViewController *)xhq_currentController
////{
////    UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
////    UIViewController *currentVC = [self xhq_currentControllerFrom:rootViewController];
////    return currentVC;
////}
////
////- (UIViewController *)xhq_currentControllerFrom:(UIViewController *)rootController
////{
////    UIViewController *currentVC;
////    
////    if ([rootController presentedViewController])
////    {
////        rootController = [rootController presentedViewController];
////    }
////    
////    if ([rootController isKindOfClass:[UITabBarController class]])
////    {
////        currentVC = [self xhq_currentControllerFrom:[(UITabBarController *)rootController selectedViewController]];
////    }
////    else if ([rootController isKindOfClass:[UINavigationController class]])
////    {
////        currentVC = [self xhq_currentControllerFrom:[(UINavigationController *)rootController visibleViewController]];
////    }
////    else
////    {
////        currentVC = rootController;
////    }
////    return currentVC;
////}
//
//@end
