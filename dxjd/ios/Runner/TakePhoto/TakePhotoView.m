//
//  TakePhotoView.m
//  Runner
//
//  Created by 渴望 on 2024/2/28.
//

#import "TakePhotoView.h"

@implementation TakePhotoView{
    //FlutterIosTextLabel 创建后的标识
    int64_t _viewId;
    //消息回调
    FlutterMethodChannel* _channel;
    UIView * useView;
}


-(instancetype)initWithWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id)args binaryMessenger:(NSObject<FlutterBinaryMessenger> *)messenger{
    if ([super init]) {
        if (frame.size.width==0) {
            frame=CGRectMake(frame.origin.x, frame.origin.y, [UIScreen mainScreen].bounds.size.width * 208 / 375, [UIScreen mainScreen].bounds.size.width * 208 / 375);
        }
        
        useView = [[UIView alloc]initWithFrame:frame];
        useView.clipsToBounds = true;
        useView.layer.cornerRadius = frame.size.width/2;
        useView.backgroundColor = UIColor.blackColor;
        [self kw_initUI];
        
        _viewId = viewId;
        
        
        NSString* channelName = [NSString stringWithFormat:@"com.flutter_to_native_test_textview_%lld", viewId];
        _channel = [FlutterMethodChannel methodChannelWithName:channelName binaryMessenger:messenger];
        __weak __typeof__(self) weakSelf = self;
        [_channel setMethodCallHandler:^(FlutterMethodCall *  call, FlutterResult  result) {
            [weakSelf onMethodCall:call result:result];
        }];

    
    }
    return self;
    
}

-(void)onMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result{
    if ([[call method] isEqualToString:@"takePhoto"]) {
        self.flutterResult = result;
        [self takePhoto];
    }else{
        //其他方法的回调
        [self stopCamera];
    }
}

//- (instancetype)initWithFrame:(CGRect)frame {
//    self = [super initWithFrame:frame];
//    if (self) {
//        [self kw_initUI];
//        self.clipsToBounds = true;
//        self.layer.cornerRadius = 100;
//    }
//    return self;
//}

- (void)kw_initUI{
    // 创建 AVCaptureSession 对象
    AVCaptureSession *captureSession = [[AVCaptureSession alloc] init];
    self.captureSession = captureSession;

    // 获取前置摄像头设备
    AVCaptureDevice *frontCamera = [AVCaptureDevice defaultDeviceWithDeviceType:AVCaptureDeviceTypeBuiltInWideAngleCamera
                                                                     mediaType:AVMediaTypeVideo
                                                                      position:AVCaptureDevicePositionFront];

    // 创建 AVCaptureDeviceInput 对象
    NSError *error = nil;
    AVCaptureDeviceInput *input = [AVCaptureDeviceInput deviceInputWithDevice:frontCamera error:&error];

    // 添加 AVCaptureDeviceInput 到 AVCaptureSession
    if ([captureSession canAddInput:input]) {
        [captureSession addInput:input];
    }

    // 创建 AVCaptureVideoPreviewLayer 对象
    AVCaptureVideoPreviewLayer *previewLayer = [AVCaptureVideoPreviewLayer layerWithSession:captureSession];
    previewLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    previewLayer.frame = useView.bounds;
    // 将 AVCaptureVideoPreviewLayer 添加到视图中
    [useView.layer addSublayer:previewLayer];

    // 开始会话
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
        [captureSession startRunning];
    });
    
    
    
    AVCapturePhotoOutput *photoOutput = [[AVCapturePhotoOutput alloc] init];
    self.photoOutput = photoOutput;
    if ([self.captureSession canAddOutput:photoOutput]) {
        [self.captureSession addOutput:photoOutput];
    }
}

- (void)stopCamera {
    [self.captureSession stopRunning];
}

- (void)takePhoto {
    AVCapturePhotoSettings *photoSettings = [AVCapturePhotoSettings photoSettings];
    [self.photoOutput capturePhotoWithSettings:photoSettings delegate:self];
}

#pragma mark - AVCapturePhotoCaptureDelegate

- (void)captureOutput:(AVCapturePhotoOutput *)output didFinishProcessingPhoto:(AVCapturePhoto *)photo error:(NSError *)error {
    if (error) {
        NSLog(@"Error capturing photo: %@", error.localizedDescription);
        return;
    }
    
    // 获取拍摄的照片数据
    NSData *photoData = [photo fileDataRepresentation];
    //UIImage *image = [UIImage imageWithData:photoData];
    
    NSString *filePath = [self savePhotoToCacheDirectory:photoData];
    if (filePath) {
        // 保存成功，可以使用 filePath 来访问保存的照片文件
        if (self.flutterResult) {
            // 执行 result
            self.flutterResult(filePath);
            
            // 清空 result
            //self.flutterResult = nil;
        }
    }
    
    
}

- (nonnull UIView *)view {
    return useView;
}


- (NSString *)savePhotoToCacheDirectory:(NSData *)photoData {
    // 获取缓存目录路径
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cacheDirectory = [paths objectAtIndex:0];
    
    // 生成唯一的文件名
    NSString *fileName = [NSString stringWithFormat:@"capturedPhoto_%@.jpg", [[NSUUID UUID] UUIDString]];
    NSString *filePath = [cacheDirectory stringByAppendingPathComponent:fileName];
    
    // 将照片数据写入文件
    BOOL success = [photoData writeToFile:filePath atomically:YES];
    if (success) {
        NSLog(@"Photo saved to cache directory: %@", filePath);
        return filePath;
    } else {
        NSLog(@"Failed to save photo to cache directory");
        return nil;
    }
}

@end
