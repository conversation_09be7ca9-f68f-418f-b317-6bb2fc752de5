//
//  TakePhotoView.h
//  Runner
//
//  Created by 渴望 on 2024/2/28.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TakePhotoView : NSObject <FlutterPlatformView,AVCapturePhotoCaptureDelegate>

-(instancetype)initWithWithFrame:(CGRect)frame
                  viewIdentifier:(int64_t)viewId
                       arguments:(id _Nullable)args
                 binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;


@property (nonatomic, strong) AVCaptureSession *captureSession;
@property (nonatomic, strong) AVCapturePhotoOutput *photoOutput;

- (void)takePhoto;
@property (nonatomic, strong) FlutterResult flutterResult;
@end

NS_ASSUME_NONNULL_END
