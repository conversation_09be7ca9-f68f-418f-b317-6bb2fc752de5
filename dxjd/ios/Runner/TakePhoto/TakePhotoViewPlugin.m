//
//  TakePhotoViewPlugin.m
//  Runner
//
//  Created by 渴望 on 2024/2/28.
//

#import "TakePhotoViewPlugin.h"

@implementation TakePhotoViewPlugin

+ (void)registerWithRegistrar:(nonnull NSObject<FlutterPluginRegistrar> *)registrar {
    
    //注册插件
    //注册 FlutterIosTextLabelFactory
    //com.flutter_to_native_test_textview 为flutter 调用此  textLabel 的标识
    [registrar registerViewFactory:[[TakePhotoViewFactory alloc] initWithMessenger:registrar.messenger] withId:@"com.flutter_to_native_test_textview"];
}


@end
