////
////  SplashManager.h
////  Runner
////
////  Created by yyy on 2024/7/8.
////
//
///*
// 
// 使用
// 
// //加载广告 设置完rootVC后调用 否则launchImage 不起作用
// SplashManager.sharedInstance().load_BeiZi_Splash()
// 
// */
//
//#import <Foundation/Foundation.h>
////#import <BeiZiSDK/BeiZiSDK.h>
////#import <AMPSAdSDK/AMPSAdSDK.h>
//
//NS_ASSUME_NONNULL_BEGIN
//
/////1 加载成功 2加载失败
//typedef void (^BeiZiCompletionHandler)(NSInteger result);
//
//@interface SplashManager : NSObject <AMPSSplashAdDelegate>
//
//+ (instancetype)sharedInstance;
//
//@property (nonatomic, strong) AMPSSplashAd *splash;
//@property (nonatomic, assign) BOOL isShow;
//- (void)preloadBeiZiSplash;
//- (void)loadBeiZiSplash;
//
//
////@property (nonatomic, strong) BeiZiRewardedVideo *rewardedVideo;
//@property (nonatomic, copy) BeiZiCompletionHandler completionHandler;
////- (void)load_BeiZi_RewardedVideoCompletionHandler:(BeiZiCompletionHandler)completionHandler;
//
//@end
//
//NS_ASSUME_NONNULL_END
