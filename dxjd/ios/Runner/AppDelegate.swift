import UIKit
import MB<PERSON>rog<PERSON>HUD
import Flutter
// import UMCommonLog
//import UMCommon
import UserNotifications
//import UMPush
//import umeng_push_sdk
import CoreMotion
import UMCommon
import Foundation
import DouyinOpenSDK

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate, DouyinOpenSDKLogDelegate ,UMSocialShareMenuViewDelegate{
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        registerIPlatform()
        shareResgiter()
        controller = (window?.rootViewController as! FlutterViewController)
        DouyinOpenSDKApplicationDelegate.sharedInstance().application(application, didFinishLaunchingWithOptions: launchOptions)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func application(_ application: UIApplication, handleOpen url: URL) -> <PERSON><PERSON> {
        if DouyinOpenSDKApplicationDelegate.sharedInstance().application(application, open: url, sourceApplication: nil, annotation: nil) {
            return true
        }
        return false
    }
    
    override func application(_ application: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {

        if DouyinOpenSDKApplicationDelegate.sharedInstance().application(application, open: url, sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String, annotation: options[UIApplication.OpenURLOptionsKey.annotation]) {
            return true
        }
        return super.application(application, open: url, options: options)
    }
    
    override func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {

        if DouyinOpenSDKApplicationDelegate.sharedInstance().application(application, open: url, sourceApplication: sourceApplication, annotation: annotation) {
            return true
        }

        return false
    }
    
    
    override func applicationDidBecomeActive(_ application: UIApplication) {
        if #available(iOS 14, *) {
            ATTrackingManager.requestTrackingAuthorization { status in
                // Handle the authorization status here
            }
        }
    }
    
    private func registerLogDelegate() {
        DouyinOpenSDKApplicationDelegate.sharedInstance().logDelegate = self
    }
    
    func onLog(_ logInfo: String) {
        // 建议将log打印或者使用您自己的日志基建打印出来，便于查找接入问题。
        print(logInfo);
    }
    
    var controller:FlutterViewController?
    var flutterRuslt:((String) -> Void)?
    let helper = UnityHelper()
    var isClick:Bool = false
    let motionManager = CMMotionManager()
    @objc var shouldAutorotate:Bool = true
    override func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        if (shouldAutorotate) {
            return .allButUpsideDown
        }else{
            return .portrait
        }
    }
}

extension AppDelegate {

        func showLoading(withText text: String? = nil) {
            let hud = MBProgressHUD.showAdded(to: controller!.view!, animated: true)
            hud.label.text = text ?? "加载中..."
            hud.isUserInteractionEnabled = false
            
            // 兼容旧版本的样式设置
            if #available(iOS 13.0, *) {
                hud.contentColor = .label
            } else {
                hud.contentColor = .black
            }
        }
        
        func hideLoading() {
            MBProgressHUD.hide(for: controller!.view!, animated: true)
        }
        func umSocialShareMenuViewDidAppear(){
            
        }
    
        func umSocialShareMenuViewDidDisappear(){
            if(isClick){
                isClick=false;
                return;
            }
            flutterRuslt!("fail")
        }
    
        func isDouyinInstalled() -> Bool {
            if let url = URL(string: "douyinopensdk://") {
            return UIApplication.shared.canOpenURL(url)
        }
            return false
        }
        func isWeChatInstalled() -> Bool {
            if let url = URL(string: "weixin://") {
                return UIApplication.shared.canOpenURL(url)
            }
            return false
        }
        func shareResgiter() {
            guard let vc = window?.rootViewController as? FlutterViewController else {
                return
            }
            let methodChannel = FlutterMethodChannel(name: "xw.dxjk.share", binaryMessenger: vc.binaryMessenger)
            methodChannel.setMethodCallHandler { [weak self] call, result in
                guard self != nil else { return }
                self!.flutterRuslt = result
                switch call.method {
                case "initUm":
                    UMConfigure.initWithAppkey("66c2fb06192e0574e764f8fe", channel: "App Store");
                    /* 设置微信的appKey和appSecret */
                    UMSocialGlobal.shareInstance().universalLinkDic = [
                        NSNumber(value: UMSocialPlatformType.wechatSession.rawValue): "https://dxjk.daxiangjd.com/app/",
                        NSNumber(value: UMSocialPlatformType.douYin.rawValue): "https://dxjk.daxiangjd.com/app/",
                    ]
//                     UMSocialManager.default().setPlaform(UMSocialPlatformType.douYin, appKey: "aws4j9ssoiw1wemj", appSecret: "1c64353b32c1cd15033bd10214bcfb87", redirectURL: "https://dxjk.daxiangjd.com/app/")
                    UMSocialManager.default().setPlaform(UMSocialPlatformType.wechatSession, appKey: "wx3628d3e48d1e93a4", appSecret: "62bab847633cccb97a3fb6616ff84212", redirectURL: "https://dxjk.daxiangjd.com/app/")
                    // 支持所有iOS系统
                    func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
                        let result = UMSocialManager.default().handleOpen(url)
                        if !result {
                            // 其他如支付等SDK的回调
                        }
                        return result
                    }
                    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) -> Bool {
                        if !UMSocialManager.default().handleUniversalLink(userActivity, options: nil) {
                            // 其他SDK的回调
                        }
                        return true
                    }
                    //初始化抖音sdk
                    DouyinOpenSDKApplicationDelegate.sharedInstance().application(UIApplication.shared)
                    UMSocialUIManager.setShareMenuViewDelegate(self)
                    break;
                case "shareImage":
                    let dic:[String:Any] = call.arguments as! [String : Any]
                    UMSocialUIManager.addCustomPlatformWithoutFilted(
                        UMSocialPlatformType.init(rawValue: UMSocialPlatformType.userDefine_Begin.rawValue + 2)!,
                        withPlatformIcon: UIImage(named: "icon_dy"),
                        withPlatformName: "抖音")
                    UMSocialUIManager.setPreDefinePlatforms([NSNumber(integerLiteral:UMSocialPlatformType.wechatSession.rawValue),NSNumber(integerLiteral:UMSocialPlatformType.wechatTimeLine .rawValue),NSNumber(integerLiteral:UMSocialPlatformType.userDefine_Begin.rawValue + 2)])
            
                    UMSocialShareUIConfig.shareInstance().sharePageGroupViewConfig.sharePageGroupViewPostionType = UMSocialSharePageGroupViewPositionType.bottom ///显示位置底部
                    // 获取当前窗口的根视图控制器
                    if let rootViewController = self?.window?.rootViewController {
                          // 在这里使用 rootViewController
                          print("Root ViewController: \(rootViewController)")
                        //显示分享面板
                        UMSocialUIManager.showShareMenuViewInWindow() { platformType, userInfo in
                            self?.isClick=true;
                          if(platformType == UMSocialPlatformType.init(rawValue: UMSocialPlatformType.userDefine_Begin.rawValue + 2)){
                              if(!self!.isDouyinInstalled()){
                                result("noInstall");
                                return;
                             }
                             let req = DouyinOpenSDKShareRequest()
                             self?.registerLogDelegate()
                             //（可选）设置分享的上下文
                             // 建议传入OpenAPI中申请的ShareID，分享结果会通过 Webhooks 进行回调。更多信息，请参见查询视频分享结果及数据
                             req.state = "placehold_state";
                             req.mediaType = DouyinOpenSDKShareMediaType.image
                             let downloader = ImageDownloader()
                             self?.showLoading()
                              downloader.saveBase64ToPhotoLibrary(base64String: dic["base64"] as! String) { localIdentifier, error in
                                    if let error = error {
                                        print("保存失败: \(error.localizedDescription)")
                                        return
                                    }
                                    if let identifier = localIdentifier {
                                        print("图片保存成功，localIdentifier: \(identifier)")
                                        // 这里可以使用返回的 localIdentifier 进行后续操作
                                        req.localIdentifiers = [localIdentifier!] // NSArray<NSString *> * （系统相册的本地标识符）
                                        // 这里可以调用抖音分享方法，传入 localIdentifiers
                                        // 添加标题
                                        req.title = DouyinOpenSDKShareTitle();
                                        req.shareAction = DouyinOpenSDKShareAction.typePublishMedia
                                        req.title.text = "" // 正文
                                        req.landedPageType = DouyinOpenSDKLandedPageType.edit // 编辑页面
                                        req.send { DouyinOpenSDKShareResponse in
                                            if (DouyinOpenSDKShareResponse.shareState  == DouyinOpenSDKShareRespState.stateSuccess) {
                                                result("success");
                                              } else{
                                                  result("fail");
                                              }
                                        }
                                        DispatchQueue.main.async(execute: { [self] in
                                            MBProgressHUD.hide(for:self!.controller!.view!, animated: true)
                                        })
                                    }
                                }
                          }else{
                              //创建分享消息对象
                              let messageObject = UMSocialMessageObject()

                              //创建图片内容对象
                              let shareObject = UMShareImageObject()
                              
                              // 将Base64字符串转换为Data
                              if let contentData = Data(base64Encoded: dic["base64"] as! String) {
                                  // 将Data转换为NSData
                                  let nsData = contentData as NSData
                                  // 打印转换后的NSData
                                  //如果有缩略图，则设置缩略图
                                  shareObject.thumbImage = nsData
                                  shareObject.shareImage = nsData
                              } else {
                                  print("Base64字符串无法解码")
                              }
                              //分享消息对象设置分享内容对象
                              messageObject.shareObject = shareObject
                              if(platformType == UMSocialPlatformType.wechatTimeLine){
                                  if(!self!.isWeChatInstalled()){
                                      result("noInstall");
                                      return;
                                  }
                                  UMSocialManager.default().share(to: UMSocialPlatformType.wechatTimeLine, messageObject: messageObject, currentViewController: rootViewController) { data, error in
                                  }
                                  result("success");
                              }else if(platformType == UMSocialPlatformType.wechatSession){
                                  if(!self!.isWeChatInstalled()){
                                      result("noInstall");
                                      return;
                                  }
                                  UMSocialManager.default().share(to: UMSocialPlatformType.wechatSession, messageObject: messageObject, currentViewController: rootViewController) { data, error in
                                  }
                                  result("success");
                              }
                          }
                        }
                        }
                    break;
                case "linkShare":
                    let dic:[String:Any] = call.arguments as! [String : Any]
                    //创建分享消息对象
                    let messageObject = UMSocialMessageObject()
                    //创建网页内容对象
                    let shareObject = UMShareWebpageObject.shareObject(withTitle: dic["title"] as? String, descr: dic["description"] as? String, thumImage:UIImage(named: "ic_logo"))
                    //设置网页地址
                    shareObject?.webpageUrl = dic["url"] as? String
                    UMSocialUIManager.setPreDefinePlatforms([NSNumber(integerLiteral:UMSocialPlatformType.wechatSession.rawValue),NSNumber(integerLiteral:UMSocialPlatformType.wechatTimeLine .rawValue)])
                    //分享消息对象设置分享内容对象
                    messageObject.shareObject = shareObject
                    // 获取当前窗口的根视图控制器
                    if let rootViewController = self?.window?.rootViewController {
                          // 在这里使用 rootViewController
                          print("Root ViewController: \(rootViewController)")
                        //显示分享面板
                        UMSocialUIManager.showShareMenuViewInWindow() { platformType, userInfo in
                            // 根据获取的platformType确定所选平台进行下一步操作
                             if(platformType == UMSocialPlatformType.wechatTimeLine){
                                 if(!self!.isWeChatInstalled()){
                                     result("noInstall");
                                     return;
                                 }
                                UMSocialManager.default().share(to: UMSocialPlatformType.wechatTimeLine, messageObject: messageObject, currentViewController: rootViewController) { data, error in
                                }
                                 result("success");
                            }else if(platformType == UMSocialPlatformType.wechatSession){
                                if(!self!.isWeChatInstalled()){
                                    result("noInstall");
                                    return;
                                }
                                UMSocialManager.default().share(to: UMSocialPlatformType.wechatSession, messageObject: messageObject, currentViewController: rootViewController) { data, error in
                                }
                                result("success");
                            }
                        }
                        }
                    break;
                case "shareImageAndContentWX":
                    if(!self!.isWeChatInstalled()){
                        result("noInstall");
                        return;
                    }
                    let dic:[String:Any] = call.arguments as! [String : Any]
                    //创建分享消息对象
                    let messageObject = UMSocialMessageObject()
                    //创建图片内容对象
                    let shareObject = UMShareImageObject()
                    // 将Base64字符串转换为Data
                    shareObject.shareImage = dic["image"] as? String
                    shareObject.thumbImage = dic["image"] as? String
                    //分享消息对象设置分享内容对象
                    messageObject.shareObject = shareObject;
                    if let rootViewController = self?.window?.rootViewController {
                          // 在这里使用 rootViewController
                        //调用分享接口
                        UMSocialManager.default().share(to: UMSocialPlatformType.wechatSession, messageObject: messageObject, currentViewController: rootViewController) { data, error in
                        }
                        result("success");
                        }
                    break;
                case "shareImageAndContentDY":
                    if(!self!.isDouyinInstalled()){
                        result("noInstall");
                        return;
                    }
                    let dic:[String:Any] = call.arguments as! [String : Any]
                    let req = DouyinOpenSDKShareRequest()
                    self?.registerLogDelegate()
                    //（可选）设置分享的上下文
                    // 建议传入OpenAPI中申请的ShareID，分享结果会通过 Webhooks 进行回调。更多信息，请参见查询视频分享结果及数据
                    req.state = "placehold_state";
                    req.mediaType = DouyinOpenSDKShareMediaType.image
                    let downloader = ImageDownloader()
                    self?.showLoading()
                    downloader.downloadAndSaveImages(urlStrings: dic["image"] as! [String],view: self!) { localIdentifiers in
                        print("所有图片的 localIdentifier: \(localIdentifiers)")
                        req.localIdentifiers = localIdentifiers // NSArray<NSString *> * （系统相册的本地标识符）
                        // 这里可以调用抖音分享方法，传入 localIdentifiers
                        // 添加标题
                        req.title = DouyinOpenSDKShareTitle();
                        req.shareAction = DouyinOpenSDKShareAction.typePublishMedia
                        let content:String = dic["content"] as! String
                        req.title.text = content // 正文
                        for tag in dic["tags"] as! [String]{
                            var i:Int = content.count;
                            let hashtag = DouyinOpenSDKTitleHashtag()
                            hashtag.text = tag
                            hashtag.index = i
                            i = i+1
                            req.title.hashtags.add(hashtag)
                        }
                        req.landedPageType = DouyinOpenSDKLandedPageType.edit // 编辑页面
                        req.send { DouyinOpenSDKShareResponse in
                            if (DouyinOpenSDKShareResponse.shareState  == DouyinOpenSDKShareRespState.stateSuccess) {
                                  result(["status":"success"]);
                              } else{
                                  result(["status":"fail","errorCode":DouyinOpenSDKShareResponse.shareState.rawValue]);
                              }
                        }
                        DispatchQueue.main.async(execute: { [self] in
                            MBProgressHUD.hide(for:self!.controller!.view!, animated: true)
                        })
                    }

                    break;
                default:
                        result("onAdClosed");
                        break;
                }
            }
        }
    
    
    
            func registerIPlatform() {
                //注册原生拍照view
                TakePhotoViewRegistran.register(with: self)
                
                guard let vc = window?.rootViewController as? FlutterViewController else {
                    return
                }
                let methodChannel = FlutterMethodChannel(name: "xw.dxjk.tools", binaryMessenger: vc.binaryMessenger)
                methodChannel.setMethodCallHandler { [weak self] call, result in
                    guard self != nil else { return }
                    
                    switch call.method {
                    case "getModel":
                        //手机机型
                        result(UIDevice().modelName)
                        break
                    case "initSensor":
                        //初始化传感器
                        break
                    case "deviceMotion":
                        //设备是否垂直
                        var isResult:Bool = false
                        
                        if ((self?.motionManager.isDeviceMotionAvailable) != nil) {
                            self?.motionManager.deviceMotionUpdateInterval = 0.1
                            
                            self?.motionManager.startDeviceMotionUpdates(to: OperationQueue.main) { [weak self] (deviceMotion, error) in
                                // 先检查是否有错误，有错误则直接返回结果
                                if let error = error {
                                    print("Device motion update error: \(error.localizedDescription)")
                                    self?.motionManager.stopDeviceMotionUpdates()
                                    isResult = true
                                    result(false)
                                    return
                                }
                                if let attitude = deviceMotion?.attitude {
                                    let pitch = attitude.pitch * 180 / .pi // x-axis rotation in degrees
                                    print(pitch);
                                    if abs(pitch) >= 50 {
                                        print("Device is vertical and upright (top side up)")
                                        // 设备垂直且顶端朝上的处理逻辑
                                        self?.motionManager.stopDeviceMotionUpdates()
                                        isResult = true
                                        result(true)
                                    } else {
                                        print("Device is not vertical or not upright")
                                        self?.motionManager.stopDeviceMotionUpdates()
                                        // 设备不垂直或不顶端朝上的处理逻辑
                                        isResult = true
                                        result(false)
                                    }
                                }else{
                                    self?.motionManager.stopDeviceMotionUpdates()
                                    isResult = true
                                    result(false)
                                }
                            }
                            
                        }else{
                            isResult = true
                            result(false)
                        }
                        
                        // 1秒内没有回调的情况
                        DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
                            if !isResult {
                                result(false)
                            }
                        }
                        break
                    case "photoBack":
                        break
                    case "imageContainFace":
                        //照片中是否有人脸
                        let dic:[String:Any] = call.arguments as! [String : Any]
                        let path = String(format: "%@", dic["path"] as! CVarArg )
                        let url = URL(fileURLWithPath: path)
                        if let data = try? Data(contentsOf: url),
                           let image = UIImage(data: data) {
                            let opts: [String: Any] = [CIDetectorAccuracy: CIDetectorAccuracyHigh]
                            // 将图像转换为CIImage
                            if let faceImage = CIImage(image: image) {
                                let faceDetector = CIDetector(ofType: CIDetectorTypeFace, context: nil, options: opts)
                                // 识别出人脸数组
                                let features = faceDetector?.features(in: faceImage) ?? []
                                print(features.count)
                                if features.count > 0 {
                                    result(true)
                                } else {
                                    result(false)
                                }
                            } else {
                                result(false)
                            }
                        } else {
                            result(false)
                        }
                        break
                    case "setIsVip":
                        break
                    case "jumpLightSimulation":
                        //跳转到unity
                        let dic:[String:Any] = call.arguments as! [String : Any]
                        self?.helper.flutterViewController = vc
                        self?.helper.platformChannel = methodChannel
                        self?.helper.dic = dic
                        self?.helper.initUnity(CommandLine.argc, argV: CommandLine.unsafeArgv, launchOptions: [:])
                        break
                    case "finishUnity":
                        //退出unity
                        break
                    default:
                        break
                    }
                }
            }
}
