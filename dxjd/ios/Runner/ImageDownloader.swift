import Photos

open class ImageDownloader {

    // 存储最终获取的 PHAsset localIdentifier
    private var imageLocalIdentifiers: [String] = []
    private let semaphore = DispatchSemaphore(value: 0) // 用于同步操作（可选）

    /// 下载图片并保存到相册
    /// - Parameters:
    ///   - urls: 图片URL数组
    ///   - completion: 返回包含所有 localIdentifier 的数组
    func downloadAndSaveImages(urlStrings: [String],view:AppDelegate, completion: @escaping ([String]) -> Void) {
        view.showLoading();
        let group = DispatchGroup()
            var identifiers: [String] = []
            
            for url in urlStrings {
                group.enter()
                let urlPath = URL(string:url)!
                // 1. 下载图片
                downloadImage(from: urlPath) { image in
                    guard let image = image else {
                        print("下载失败: \(url)")
                        group.leave()
                        return
                    }
                    
                    print("下载成功: \(url)")
                    
                    // 2. 保存到相册
                    print("开始保存: \(url)")
                    self.saveImageToAlbum(image: image) { localIdentifier in
                        if let localIdentifier = localIdentifier {
                            identifiers.append(localIdentifier)
                            print("保存成功: \(localIdentifier)")
                        } else {
                            print("保存失败: \(url)")
                        }
                        group.leave() // 确保无论成功与否都 leave()
                    }
                }
            }
            
            // 3. 所有任务完成后回调
            group.notify(queue: .main) {
                view.hideLoading();
                completion(identifiers)
            }
    }

    // MARK: - Private Methods

    /// 下载图片
    private func downloadImage(from url: URL, completion: @escaping (UIImage?) -> Void) {
        URLSession.shared.dataTask(with: url) { data, _, error in
            guard let data = data, error == nil, let image = UIImage(data: data) else {
                print("下载失败: \(url.absoluteString)")
                completion(nil)
                return
            }
            completion(image)
        }.resume()
    }

    /// 保存图片到相册并获取 localIdentifier
    private func saveImageToAlbum(image: UIImage, completion: @escaping (String?) -> Void) {
        var localIdentifier: String?

        PHPhotoLibrary.shared().performChanges({
            // 创建保存请求
            let request = PHAssetChangeRequest.creationRequestForAsset(from: image)
            localIdentifier = request.placeholderForCreatedAsset?.localIdentifier
        }) { success, error in
            DispatchQueue.main.async {
                if success, let localIdentifier = localIdentifier {
                    print("保存成功: \(localIdentifier)")
                    completion(localIdentifier)
                } else {
                    print("保存失败: \(error?.localizedDescription ?? "未知错误")")
                    completion(nil)
                }
            }
        }
    }
    
    func saveBase64ToPhotoLibrary(base64String: String,completion: @escaping (String?, Error?) -> Void) {
        // 2. 将 Base64 字符串转换为 Data
        guard let imageData = Data(base64Encoded: base64String) else {
            completion(nil, NSError(domain: "Base64", code: -2, userInfo: [NSLocalizedDescriptionKey: "Base64 字符串转换失败"]))
            return
        }
        
        // 3. 将 Data 转换为 UIImage
        guard UIImage(data: imageData) != nil else {
            completion(nil, NSError(domain: "Image", code: -3, userInfo: [NSLocalizedDescriptionKey: "图片数据转换失败"]))
            return
        }
        
        // 4. 保存到相册
        PHPhotoLibrary.shared().performChanges({
            // 创建图片创建请求
            let creationRequest = PHAssetCreationRequest.forAsset()
            // 添加图片数据
            creationRequest.addResource(with: .photo, data: imageData, options: nil)
        }) { (success, error) in
            if let error = error {
                completion(nil, error)
                return
            }
            
            // 5. 获取最后保存的图片的 localIdentifier
            let fetchOptions = PHFetchOptions()
            fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            fetchOptions.fetchLimit = 1
            
            let fetchResult = PHAsset.fetchAssets(with: .image, options: fetchOptions)
            if let lastAsset = fetchResult.firstObject {
                completion(lastAsset.localIdentifier, nil)
            } else {
                completion(nil, NSError(domain: "PhotoLibrary", code: -4, userInfo: [NSLocalizedDescriptionKey: "未能获取保存的图片"]))
            }
        }
    }
}
