//
//  UnityHelper.m
//  Runner
//
//  Created by 渴望 on 2023/9/9.
//

#import "UnityHelper.h"
#import "Runner-Swift.h"

/* UnityFrameworkLoad */
UIKIT_STATIC_INLINE UnityFramework* UnityFrameworkLoad()
{
    NSString* bundlePath = nil;
    bundlePath = [[NSBundle mainBundle] bundlePath];
    bundlePath = [bundlePath stringByAppendingString: @"/Frameworks/UnityFramework.framework"];

    NSBundle* bundle = [NSBundle bundleWithPath: bundlePath];
    if ([bundle isLoaded] == false) [bundle load];

    UnityFramework* ufw = [bundle.principalClass getInstance];
    if (![ufw appController])
    {
        // unity is not initialized
        [ufw setExecuteHeader: &_mh_execute_header];
    }
    return ufw;
}

@interface UnityHelper ()
@property (nonatomic, strong) UIViewController *uuu;
@end

@implementation UnityHelper


#pragma mark - Unity

- (BOOL)unityIsInitialized
{
    return [self ufw] && [[self ufw] appController];
}

- (void)initUnity:(int)argc argV:(char **)argv launchOptions:(NSDictionary *)launchOptions
{
    /* 判断Unity 是否已经初始化 */
    if ([self unityIsInitialized]) {
        
        [UIView transitionWithView:[UIApplication sharedApplication].keyWindow duration:0.5f options:UIViewAnimationOptionTransitionFlipFromBottom animations:^{
            
            BOOL oldState = [UIView areAnimationsEnabled];
            
            [UIApplication sharedApplication].keyWindow.rootViewController = self.uuu;
            [[UIApplication sharedApplication].keyWindow makeKeyAndVisible];
            
            [UIView setAnimationsEnabled:oldState];
            
            [self receiveUnityMsg:@""];
            
        } completion:nil];
        return;
    }
    
    /* 初始化Unity */
    self.ufw = UnityFrameworkLoad();
    [self.ufw setDataBundleId:"com.unity3d.framework"];
    [self.ufw registerFrameworkListener:self];
    [NSClassFromString(@"FrameworkLibAPI") registerAPIforNativeCalls:self];
    [self.ufw runEmbeddedWithArgc:argc argv:argv appLaunchOpts:launchOptions];
    
}



- (void)receiveUnityMsg:(NSString *)color {
    
    NSString * apiVersion = self.dic[@"apiVersion"];
    NSString * authorizationAppStudent = self.dic[@"authorizationAppStudent"];
    NSString * carTypePath = self.dic[@"carTypePath"];
    NSString * examQuestionPath = self.dic[@"examQuestionPath"];
    NSString * VideoListPath = self.dic[@"VideoListPath"];
    NSString * GetVideoPath = self.dic[@"GetVideoPath"];
    NSString * version = self.dic[@"version"];
    NSString * from = self.dic[@"from"];
    NSString * videoId = self.dic[@"videoId"];
    
    
    NSString *jsonString = [NSString stringWithFormat:@"{\"apiVersion\":\"%@\",\"authorizationAppStudent\":\"%@\",\"carTypePath\":\"%@\",\"examQuestionPath\":\"%@\",\"VideoListPath\":\"%@\",\"GetVideoPath\":\"%@\",\"version\":\"%@\",\"from\":\"%@\",\"videoId\":\"%@\"}",apiVersion,authorizationAppStudent,carTypePath,examQuestionPath,VideoListPath,GetVideoPath,version,from,videoId];
    

    
    const char* jsonMsg = [jsonString UTF8String];
    [[self ufw] sendMessageToGOWithName: "Bridge" functionName: "Receive" message: jsonMsg];
    
}


- (void)showHostMainWindow:(NSString*)color {
    
    self.uuu =  [UIApplication sharedApplication].keyWindow.rootViewController;
    
    [self CloseUnity];
    
}




- (void)onShowVip:(NSString *)color {
    
}


- (void)CloseUnity {
    
    AppDelegate * app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    
    
    [UIView transitionWithView:[UIApplication sharedApplication].keyWindow duration:0.5f options:UIViewAnimationOptionTransitionFlipFromBottom animations:^{
        BOOL oldState = [UIView areAnimationsEnabled];
        [UIApplication sharedApplication].keyWindow.rootViewController = self.flutterViewController;
        [[UIApplication sharedApplication].keyWindow makeKeyAndVisible];
        [UIView setAnimationsEnabled:oldState];
    } completion:^(BOOL finished) {
        app.shouldAutorotate = NO; //强制竖屏
        [UIViewController attemptRotationToDeviceOrientation];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            app.shouldAutorotate = YES; //非强制竖屏
        });
    }];
    
}





@end
