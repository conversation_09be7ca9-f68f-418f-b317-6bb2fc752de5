//
//  UnityHelper.h
//  Runner
//
//  Created by 渴望 on 2023/9/9.
//

#import <Foundation/Foundation.h>
#include <UnityFramework/UnityFramework.h>
#include <UnityFramework/NativeCallProxy.h>
#import <Flutter/Flutter.h>


NS_ASSUME_NONNULL_BEGIN

@interface UnityHelper : NSObject <UnityFrameworkListener ,NativeCallsProtocol> //NativeCallsProtocol

@property  UnityFramework *ufw;
- (void)initUnity:(int)argc argV:(char **)argv launchOptions:(NSDictionary *)launchOptions;

//@property (nonatomic, strong) AppConfig * appconfig;
@property (nonatomic, strong) FlutterViewController * flutterViewController;
@property (nonatomic, strong) FlutterMethodChannel * platformChannel;
@property (nonatomic, strong) NSDictionary *dic;

@end


NS_ASSUME_NONNULL_END
