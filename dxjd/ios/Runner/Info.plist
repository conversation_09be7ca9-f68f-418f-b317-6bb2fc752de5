<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_CN</string>
		<string>zh</string>
	</array>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx3628d3e48d1e93a4</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dxjkpro</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>douyin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>aws4j9ssoiw1wemj</string>
			</array>
		</dict>
	</array>
	<key>DouyinAppID</key>
	<string>aws4j9ssoiw1wemj</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>alipays</string>
		<string>weixinULAPI</string>
		<string>weixin</string>
		<string>wechat</string>
		<string>weixinURLParamsAPI</string>
		<string>douyinopensdk</string>
		<string>douyinliteopensdk</string>
		<string>douyinsharesdk</string>
		<string>snssdk1128</string>
		<string>snssdk1128</string>
		<string>wechat</string>
		<string>weixinULAPI</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>beizi.biz</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>sdk-api.beizi.biz</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
			</dict>
			<key>sdk-event.beizi.biz</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
			</dict>
		</dict>
	</dict>
	<key>NSUserNotificationsUsageDescription</key>
	<string>大象驾到Pro需要访问系统推送来接收到大象驾到Pro的新消息提醒</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>238da6jt44.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>22mmun2rn5.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>f7s53z58qe.skadnetwork</string>
		</dict>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>aps-environment</key>
	<string>development</string>
</dict>
</plist>
