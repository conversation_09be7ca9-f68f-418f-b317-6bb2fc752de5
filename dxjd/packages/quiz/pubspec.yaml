name: quiz
description: A new Flutter package project.
version: 0.0.1
publish_to: 'none'

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # State management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  component_library:
    path: ../component_library
  api:
    path: ../api
  user_repository:
    path: ../user_repository
  login:
    path: ../features/login
  tools:
    path: ../tools
  timing:
    path: ../timing
  dxjd:
    path: ../../
  home_repository:
    path: ../home_repository
  home:
    path: ../features/home
  # mPass小程序容器
  mop:
    git:
      url: https://github.com/Kayouyou/mop-flutter-sdk.git
  # 加密
  encrypt: ^5.0.1
  # 事务总线
  event_bus: ^2.0.0
  common_utils: ^2.1.0
  # Toast库
  fluttertoast: ^8.0.9
  stop_watch_timer: ^1.3.1
  flutter_easyloading: ^3.0.3
  cached_network_image: ^3.2.0
  url_launcher: ^6.3.0
  # 下拉刷新
  flutter_easyrefresh: ^2.2.1
  # webview
  webview_flutter: ^3.0.0

  # 加密数据库
  sqflite_sqlcipher: ^2.2.1
  get: ^4.7.2
  # 播放器
  video_player: ^2.5.1
  audioplayers: ^1.1.1
  scrollable_positioned_list: ^0.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

# To add assets to your package, add an assets section, like this:
# assets:
#   - images/a_dot_burr.jpeg
#   - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
