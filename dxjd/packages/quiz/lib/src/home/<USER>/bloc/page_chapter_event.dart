import 'package:equatable/equatable.dart';

/// PageChapter相关事件
abstract class PageChapterEvent extends Equatable {
  const PageChapterEvent();

  @override
  List<Object?> get props => [];
}

/// 初始化事件
class PageChapterInitialized extends PageChapterEvent {
  const PageChapterInitialized();
}

/// 切换Tab事件
class TabChanged extends PageChapterEvent {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

/// 刷新UI事件
class RefreshUIRequested extends PageChapterEvent {
  const RefreshUIRequested();
}

/// 做题结果事件
class ExerciseResultSubmitted extends PageChapterEvent {
  final bool isCorrect;
  final int position;
  final String selection;

  const ExerciseResultSubmitted({
    required this.isCorrect,
    required this.position,
    required this.selection,
  });

  @override
  List<Object?> get props => [isCorrect, position, selection];
}

/// 页面切换事件
class PageChanged extends PageChapterEvent {
  final int pageIndex;

  const PageChanged(this.pageIndex);

  @override
  List<Object?> get props => [pageIndex];
}

/// 听题播放事件
class ListenPlayingChanged extends PageChapterEvent {
  final int index;

  const ListenPlayingChanged(this.index);

  @override
  List<Object?> get props => [index];
}

/// 听题播放状态事件
class ListenPlayStateChanged extends PageChapterEvent {
  final int index;

  const ListenPlayStateChanged(this.index);

  @override
  List<Object?> get props => [index];
}

/// 视频数据加载事件
class VideoDataLoaded extends PageChapterEvent {
  const VideoDataLoaded();
}

/// 跳转视频事件
class JumpToVideoRequested extends PageChapterEvent {
  const JumpToVideoRequested();
}

/// 删除题目事件
class DeleteQuestionRequested extends PageChapterEvent {
  const DeleteQuestionRequested();
}

/// 收藏题目事件
class CollectQuestionRequested extends PageChapterEvent {
  const CollectQuestionRequested();
}

/// 纠错事件
class RectifyRequested extends PageChapterEvent {
  const RectifyRequested();
}

/// 提交试卷事件
class SubmitPaperRequested extends PageChapterEvent {
  const SubmitPaperRequested();
}

/// 返回事件
class BackRequested extends PageChapterEvent {
  const BackRequested();
}

/// 应用生命周期变化事件
class AppLifecycleChanged extends PageChapterEvent {
  final String state;

  const AppLifecycleChanged(this.state);

  @override
  List<Object?> get props => [state];
}

/// 登录状态变化事件
class LoginStatusChanged extends PageChapterEvent {
  const LoginStatusChanged();
}

/// VIP状态更新事件
class VipStatusUpdated extends PageChapterEvent {
  const VipStatusUpdated();
}

/// 字体大小变化事件
class FontSizeChanged extends PageChapterEvent {
  const FontSizeChanged();
}

/// 听题显示状态变化事件
class ListenShowStateChanged extends PageChapterEvent {
  const ListenShowStateChanged();
}

/// 题目索引变化事件
class QuizIndexChanged extends PageChapterEvent {
  final int index;

  const QuizIndexChanged(this.index);

  @override
  List<Object?> get props => [index];
}

/// 刷新题目事件
class RefreshQuizRequested extends PageChapterEvent {
  final String data;

  const RefreshQuizRequested(this.data);

  @override
  List<Object?> get props => [data];
}
