import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:tools/tools.dart';

import '../../../../quiz.dart';
import '../../../bean/exercise.dart';
import '../../../bean/ladder_cache.dart';
import '../exercises.dart';
import '../listen.dart';
import '../browse.dart';
import 'page_chapter_event.dart';
import 'page_chapter_state.dart';

class PageChapterBloc extends Bloc<PageChapterEvent, PageChapterState> {
  final int? subject;
  final String? type;
  final int? sortId;
  final List<dynamic>? ids;
  final int? fixid;
  final int? mainId;
  final int? childId;
  final int? luid;
  final String? recordTitle;
  final bool? isGoWatchVideo;

  StreamSubscription? _ibus;

  PageChapterBloc({
    required this.subject,
    required this.type,
    this.sortId = 0,
    this.ids,
    this.fixid = 0,
    this.mainId = 0,
    this.childId = 0,
    this.luid = 0,
    this.recordTitle = '',
    this.isGoWatchVideo = false,
  }) : super(const PageChapterInitial()) {
    on<PageChapterInitialized>(_onInitialized);
    on<TabChanged>(_onTabChanged);
    on<RefreshUIRequested>(_onRefreshUIRequested);
    on<ExerciseResultSubmitted>(_onExerciseResultSubmitted);
    on<PageChanged>(_onPageChanged);
    on<ListenPlayingChanged>(_onListenPlayingChanged);
    on<ListenPlayStateChanged>(_onListenPlayStateChanged);
    on<VideoDataLoaded>(_onVideoDataLoaded);
    on<JumpToVideoRequested>(_onJumpToVideoRequested);
    on<DeleteQuestionRequested>(_onDeleteQuestionRequested);
    on<CollectQuestionRequested>(_onCollectQuestionRequested);
    on<RectifyRequested>(_onRectifyRequested);
    on<SubmitPaperRequested>(_onSubmitPaperRequested);
    on<BackRequested>(_onBackRequested);
    on<AppLifecycleChanged>(_onAppLifecycleChanged);
    on<LoginStatusChanged>(_onLoginStatusChanged);
    on<VipStatusUpdated>(_onVipStatusUpdated);
    on<FontSizeChanged>(_onFontSizeChanged);
    on<ListenShowStateChanged>(_onListenShowStateChanged);
    on<QuizIndexChanged>(_onQuizIndexChanged);
    on<RefreshQuizRequested>(_onRefreshQuizRequested);

    _initEventBus();
  }

  void _initEventBus() {
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_LOGIN ||
          event.type == JkKey.EVENT_UPDATE_VIP) {
        add(const RefreshUIRequested());
      } else if (event.type == JkKey.EVENT_TO_QUIZVIDEO) {
        add(const JumpToVideoRequested());
      } else if (event.type == JkKey.EVENT_LISTEN_SHOW) {
        add(const ListenShowStateChanged());
      } else if (JkKey.EVENT_QUIZ_INDEX == event.type) {
        int index = event.object;
        add(QuizIndexChanged(index));
      } else if (event.type == JkKey.EVENT_REFRESH_FONTSIZE ||
          event.type == JkKey.EVENT_UPDATE_VIP) {
        add(const RefreshUIRequested());
      } else if (event.type == JkKey.EVENT_REFRESH_QUIZ) {
        String data = event.object;
        add(RefreshQuizRequested(data));
      }
    });
  }

  Future<void> _onInitialized(
    PageChapterInitialized event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(const PageChapterLoading());

    try {
      IQuiz.get().subject = subject!;
      IQuiz.get().type = type!;

      await _initExercises();
      await _setQuizVideo();
      await _setCorecctWrong();

      if (JkKey.KEY_FIXED == IQuiz.get().type) {
        IQuiz.get().init((int value) {
          if (IQuiz.get().current <= IQuiz.get().totaltime) {
            if (state.adding) {
              IQuiz.get().current += 1000;
            }
          } else {
            add(const RefreshUIRequested());
          }
        });
        IQuiz.get().errorList = [];
      }

      bool guide = await Storage.getBool(Storage.guide, true);

      final exercises = _createExercises();
      final listen = _createListen();
      final browse = _createBrowse();

      int initialTab = isGoWatchVideo == true ? 3 : 0;
      Widget initialTabBody = exercises;

      emit(PageChapterLoaded(
        currentTabIndex: initialTab,
        tabBody: initialTabBody,
        isGuideVisible: guide,
        isHidden: true,
        isOver: false,
        isJiXu: 0,
        needJump: false,
        ignore: false,
        isActive: true,
        adding: true,
        videoMoArr: state.videoMoArr,
        exercises: exercises,
        listen: listen,
        browse: browse,
      ));

      if (isGoWatchVideo == true) {
        add(const TabChanged(3));
      }
    } catch (e) {
      emit(PageChapterError('初始化失败: $e'));
    }
  }

  Future<void> _onTabChanged(
    TabChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    final currentState = state;
    Widget? newTabBody;
    bool newIgnore = currentState.ignore;

    if (event.tabIndex == 0) {
      newTabBody = currentState.exercises;
      Listen.pause();
    } else if (event.tabIndex == 1) {
      if (JkKey.KEY_FIXED == IQuiz.get().type && !currentState.ignore) {
        // 需要显示确认对话框，这里暂时设置为listen，实际应该在UI层处理对话框
        newTabBody = currentState.listen;
      } else {
        newTabBody = currentState.listen;
      }
    } else if (event.tabIndex == 2) {
      if (JkKey.KEY_FIXED == IQuiz.get().type && !currentState.ignore) {
        // 需要显示确认对话框，这里暂时设置为browse，实际应该在UI层处理对话框
        newTabBody = currentState.browse;
        Listen.pause();
      } else {
        newTabBody = currentState.browse;
        Listen.pause();
      }
    } else {
      // 视频模式，跳转到视频播放
      add(const JumpToVideoRequested());
      return;
    }

    emit(currentState.copyWith(
      currentTabIndex: event.tabIndex,
      tabBody: newTabBody,
      ignore: newIgnore,
    ));
  }

  Future<void> _onRefreshUIRequested(
    RefreshUIRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onExerciseResultSubmitted(
    ExerciseResultSubmitted event,
    Emitter<PageChapterState> emit,
  ) async {
    QuizMo? mo = IQuiz.get().getQuiz(event.position);
    if (mo == null) return;

    mo.selection = event.selection;
    await _setResult(mo, event.isCorrect);
    await _setCorecctWrong();

    QuizUtils.get().saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);

    if (JkKey.KEY_FIXED != IQuiz.get().type) {
      QuizUtils.get().saveExercise(mo, IQuiz.get().subject);
      if (JkKey.KEY_LADDER == IQuiz.get().type && mo.selection.isNotEmpty) {
        LadderCacheMo ladder = LadderCacheMo(
          mo.sub_Id,
          mo.sort_Id,
          IQuiz.get().subject,
          mo.selection,
          mo.examstate,
          mo.doTime,
          ITools.get().getUid('0'),
          mainId!,
          childId!,
          luid!,
          0,
        );
        QuizUtils.get().saveLadder(ladder);
      }
    }

    emit(state.copyWith());
  }

  Future<void> _onPageChanged(
    PageChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    _setProgressNum();
    emit(state.copyWith());
  }

  Future<void> _onListenPlayingChanged(
    ListenPlayingChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    _setProgressNum();
    emit(state.copyWith());
  }

  Future<void> _onListenPlayStateChanged(
    ListenPlayStateChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    bool newHidden = event.index != IQuiz.get().currentpage;
    emit(state.copyWith(isHidden: newHidden));
  }

  Future<void> _onVideoDataLoaded(
    VideoDataLoaded event,
    Emitter<PageChapterState> emit,
  ) async {
    await _setQuizVideo();
    emit(state.copyWith(videoMoArr: state.videoMoArr));
  }

  Future<void> _onJumpToVideoRequested(
    JumpToVideoRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    await _jumpToVideo();
  }

  // 继续添加其他事件处理方法...
  Future<void> _onDeleteQuestionRequested(
    DeleteQuestionRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现删除逻辑
  }

  Future<void> _onCollectQuestionRequested(
    CollectQuestionRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现收藏逻辑
  }

  Future<void> _onRectifyRequested(
    RectifyRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现纠错逻辑
  }

  Future<void> _onSubmitPaperRequested(
    SubmitPaperRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现提交试卷逻辑
  }

  Future<void> _onBackRequested(
    BackRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现返回逻辑
  }

  Future<void> _onAppLifecycleChanged(
    AppLifecycleChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    // 实现应用生命周期变化逻辑
  }

  Future<void> _onLoginStatusChanged(
    LoginStatusChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onVipStatusUpdated(
    VipStatusUpdated event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onFontSizeChanged(
    FontSizeChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onListenShowStateChanged(
    ListenShowStateChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onQuizIndexChanged(
    QuizIndexChanged event,
    Emitter<PageChapterState> emit,
  ) async {
    emit(state.copyWith());
  }

  Future<void> _onRefreshQuizRequested(
    RefreshQuizRequested event,
    Emitter<PageChapterState> emit,
  ) async {
    if (event.data == '1') {
      emit(state.copyWith());
    }
  }

  // 私有辅助方法
  Future<void> _initExercises() async {
    // 在创建新的Listen实例前，先释放旧的
    if (state.listen != null) {
      Listen.release();
    }
  }

  Exercises _createExercises() {
    return Exercises(
      key: UniqueKey(),
      onResult: (bool flag, int position, String selection) {
        add(ExerciseResultSubmitted(
          isCorrect: flag,
          position: position,
          selection: selection,
        ));
      },
      onPage: (int index) {
        add(PageChanged(index));
      },
      subject: subject!,
    );
  }

  Listen _createListen() {
    return Listen(
      key: UniqueKey(),
      onPlaying: (int index) {
        add(ListenPlayingChanged(index));
      },
      onPaly: (int index) {
        add(ListenPlayStateChanged(index));
      },
    );
  }

  Browse _createBrowse() {
    return Browse(
      key: UniqueKey(),
      onPage: (int index) {
        add(PageChanged(index));
      },
      subject: subject!,
    );
  }

  Future<void> _setQuizVideo() async {
    List<int> subIds = [];
    for (QuizMo mm in IQuiz.get().quizList) {
      subIds.add(mm.sub_Id);
    }

    List quizData = await HttpDao.get().videoSubjectList(subIds);
    List<QuizVideoMo> moArr =
        quizData.map((e) => QuizVideoMo.fromJson(e)).toList();

    List<QuizVideoMo> videoMoArr = [];
    for (QuizMo mm in IQuiz.get().quizList) {
      for (QuizVideoMo nn in moArr) {
        if (mm.sub_Id == nn.subId) {
          mm.video_id = nn.id;
          mm.video_aliyunVid = nn.aliyunVid;
          mm.video_cover = nn.cover;
          mm.video_favoriteCover = nn.favoriteCover;
          videoMoArr.add(nn);
        }
      }
    }
  }

  Future<void> _jumpToVideo() async {
    try {
      if (subject == 1) {
        BuryingPointUtils.instance.addPoint(
            buryingPointList: BuryingPointList(
                eventType: 1, entranceType: 14, action: 1, browseDuration: 0));
      } else if (subject == 4) {
        BuryingPointUtils.instance.addPoint(
            buryingPointList: BuryingPointList(
                eventType: 1, entranceType: 35, action: 1, browseDuration: 0));
      }

      if (!MainController.isLoginIntercept()) {
        return;
      }

      if (IQuiz.get()
          .quizList[IQuiz.get().currentpage]
          .video_aliyunVid
          .isEmpty) {
        EasyLoading.showToast('本题视频正在上架中');
        return;
      }

      int pageIdx = 0;
      for (var i = 0; i < state.videoMoArr.length; i++) {
        var v = state.videoMoArr[i];
        QuizMo q = IQuiz.get().quizList[IQuiz.get().currentpage];
        if (v.subId == q.sub_Id) {
          pageIdx = i;
        }
      }

      Tools.get().jump(ShortVideo(
        dataList: state.videoMoArr,
        pageIdx: pageIdx,
        subject: subject!,
        firstDoError: false,
        callback: (page) {
          add(const RefreshUIRequested());

          int changePage = IQuiz.get().currentpage;
          for (var i = 0; i < IQuiz.get().quizList.length; i++) {
            QuizMo mm = IQuiz.get().quizList[i];
            var nn = state.videoMoArr[page];
            if (mm.sub_Id == nn.subId) {
              changePage = i;
            }
          }
          IQuiz.get().currentpage = changePage;
          IEventBus.get().post(IEvent(JkKey.EVENT_QUIZ_INDEX, changePage));
          if (state.currentTabIndex == 1) {
            Listen.resume();
          }
          _setProgressNum();
        },
      ));
    } catch (e) {
      Logs.e('jumpVideoShort：error = $e');
    }
  }

  Future<void> _setResult(QuizMo mo, bool flag) async {
    mo.examstate = flag ? 1 : 2;
    mo.doTime = DateUtil.getNowDateStr();
    if (!ITools.get().isLogin &&
        (JkKey.KEY_CAPTER == IQuiz.get().type ||
            JkKey.KEY_NONE == IQuiz.get().type)) {
      mo.chapterstate = flag ? 1 : 2;
    } else if (JkKey.KEY_NEWLY == IQuiz.get().type ||
        JkKey.KEY_LADDER == IQuiz.get().type) {
      mo.flag = flag ? 1 : 2;
    }

    if (flag) {
      if (JkKey.KEY_ERROR == IQuiz.get().type) {
        List<String> errorToRight = await Storage.getStringList(
            '${JkKey.KEY_ERROR_TO_RIGHT}${ITools.get().uid}$subject');
        String subId = mo.sub_Id.toString();
        if (!errorToRight.contains(subId)) {
          try {
            List<String> errorToRightNew = List.from(errorToRight);
            errorToRightNew.add(subId);
            Storage.setStringList(
                '${JkKey.KEY_ERROR_TO_RIGHT}${ITools.get().uid}$subject',
                errorToRightNew);
          } catch (e) {
            Logs.e('做对题 setResult errorToRight error: $e');
          }
        }
      }
    } else {
      mo.iserror = 1;
      if (JkKey.KEY_NONE != IQuiz.get().type &&
          JkKey.KEY_CAPTER != IQuiz.get().type &&
          JkKey.KEY_ERROR != IQuiz.get().type &&
          JkKey.KEY_COLLECT != IQuiz.get().type) {
        IQuiz.get().errorList.add(mo);
      }
    }
  }

  Future<void> _setCorecctWrong() async {
    IQuiz.get().correct = 0;
    IQuiz.get().wrong = 0;
    IQuiz.get().right = 0;
    IQuiz.get().error = 0;

    for (QuizMo mo in IQuiz.get().quizList) {
      if (!ITools.get().isLogin &&
          (JkKey.KEY_CAPTER == IQuiz.get().type ||
              JkKey.KEY_NONE == IQuiz.get().type)) {
        switch (mo.chapterstate) {
          case 1:
            IQuiz.get().correct++;
            break;
          case 2:
            IQuiz.get().wrong++;
            break;
        }
      } else {
        switch (mo.examstate) {
          case 1:
            IQuiz.get().correct++;
            break;
          case 2:
            IQuiz.get().wrong++;
            break;
        }
        if (JkKey.KEY_NEWLY == IQuiz.get().type ||
            JkKey.KEY_LADDER == IQuiz.get().type) {
          switch (mo.flag) {
            case 1:
              IQuiz.get().right++;
              break;
            case 2:
              IQuiz.get().error++;
              break;
          }
        }
      }
    }

    if (JkKey.KEY_NEWLY == IQuiz.get().type ||
        JkKey.KEY_LADDER == IQuiz.get().type) {
      IQuiz.get().total = IQuiz.get().right + IQuiz.get().error;
    } else {
      IQuiz.get().total = IQuiz.get().correct + IQuiz.get().wrong;
    }

    IQuiz.get().score = IQuiz.get().quizList.length == 0
        ? 0
        : IQuiz.get().correct * 100 ~/ IQuiz.get().quizList.length;
  }

  void _setProgressNum() {
    if (IQuiz.get().quizList.length == 0) {
      IQuiz.get().currentpage = 0;
      return;
    }
    if (IQuiz.get().currentpage + 1 > IQuiz.get().quizList.length) {
      IQuiz.get().currentpage = IQuiz.get().quizList.length - 1;
    }
    if (IQuiz.get().currentpage - 1 < 0) {
      IQuiz.get().currentpage = 0;
    }
    IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_QUIZ, '1'));
  }

  @override
  Future<void> close() {
    IEventBus.get().unregister(_ibus);
    IQuiz.get().disposeTiming();
    Listen.release();
    return super.close();
  }
}
