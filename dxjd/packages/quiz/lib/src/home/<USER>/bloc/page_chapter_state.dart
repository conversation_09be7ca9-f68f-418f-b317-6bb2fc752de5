import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../bean/quiz.dart';
import '../../../bean/quizVideo.dart';
import '../exercises.dart';
import '../listen.dart';
import '../browse.dart';

/// PageChapter状态
class PageChapterState extends Equatable {
  final int currentTabIndex;
  final Widget? tabBody;
  final bool isGuideVisible;
  final bool isHidden;
  final bool isOver;
  final int isJiXu;
  final bool needJump;
  final bool ignore;
  final bool isActive;
  final bool adding;
  final List<QuizVideoMo> videoMoArr;
  final Exercises? exercises;
  final Listen? listen;
  final Browse? browse;
  final bool isLoading;
  final String? errorMessage;

  const PageChapterState({
    this.currentTabIndex = 0,
    this.tabBody,
    this.isGuideVisible = true,
    this.isHidden = true,
    this.isOver = false,
    this.isJiXu = 0,
    this.needJump = false,
    this.ignore = false,
    this.isActive = true,
    this.adding = true,
    this.videoMoArr = const [],
    this.exercises,
    this.listen,
    this.browse,
    this.isLoading = false,
    this.errorMessage,
  });

  PageChapterState copyWith({
    int? currentTabIndex,
    Widget? tabBody,
    bool? isGuideVisible,
    bool? isHidden,
    bool? isOver,
    int? isJiXu,
    bool? needJump,
    bool? ignore,
    bool? isActive,
    bool? adding,
    List<QuizVideoMo>? videoMoArr,
    Exercises? exercises,
    Listen? listen,
    Browse? browse,
    bool? isLoading,
    String? errorMessage,
  }) {
    return PageChapterState(
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      tabBody: tabBody ?? this.tabBody,
      isGuideVisible: isGuideVisible ?? this.isGuideVisible,
      isHidden: isHidden ?? this.isHidden,
      isOver: isOver ?? this.isOver,
      isJiXu: isJiXu ?? this.isJiXu,
      needJump: needJump ?? this.needJump,
      ignore: ignore ?? this.ignore,
      isActive: isActive ?? this.isActive,
      adding: adding ?? this.adding,
      videoMoArr: videoMoArr ?? this.videoMoArr,
      exercises: exercises ?? this.exercises,
      listen: listen ?? this.listen,
      browse: browse ?? this.browse,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        currentTabIndex,
        tabBody,
        isGuideVisible,
        isHidden,
        isOver,
        isJiXu,
        needJump,
        ignore,
        isActive,
        adding,
        videoMoArr,
        exercises,
        listen,
        browse,
        isLoading,
        errorMessage,
      ];
}

/// 初始状态
class PageChapterInitial extends PageChapterState {
  const PageChapterInitial();
}

/// 加载中状态
class PageChapterLoading extends PageChapterState {
  const PageChapterLoading() : super(isLoading: true);
}

/// 加载完成状态
class PageChapterLoaded extends PageChapterState {
  const PageChapterLoaded({
    required int currentTabIndex,
    required Widget tabBody,
    required bool isGuideVisible,
    required bool isHidden,
    required bool isOver,
    required int isJiXu,
    required bool needJump,
    required bool ignore,
    required bool isActive,
    required bool adding,
    required List<QuizVideoMo> videoMoArr,
    required Exercises? exercises,
    required Listen? listen,
    required Browse? browse,
  }) : super(
          currentTabIndex: currentTabIndex,
          tabBody: tabBody,
          isGuideVisible: isGuideVisible,
          isHidden: isHidden,
          isOver: isOver,
          isJiXu: isJiXu,
          needJump: needJump,
          ignore: ignore,
          isActive: isActive,
          adding: adding,
          videoMoArr: videoMoArr,
          exercises: exercises,
          listen: listen,
          browse: browse,
        );
}

/// 错误状态
class PageChapterError extends PageChapterState {
  const PageChapterError(String errorMessage) : super(errorMessage: errorMessage);
}
