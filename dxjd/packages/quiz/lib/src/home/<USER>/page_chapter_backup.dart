import 'dart:async';

import 'package:common_utils/common_utils.dart';
import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:quiz/src/bean/quizVideo.dart';
import 'package:quiz/src/home/<USER>/video_short.dart';
import 'package:tools/tools.dart';

import '../../../quiz.dart';
import '../../bean/exercise.dart';
import '../../bean/ladder_cache.dart';
import '../../bean/quiz.dart';
import '../../data/i_quiz.dart';
import '../../data/quiz_utils.dart';
import '../../utils/jump.dart';
import 'browse.dart';
import 'exercises.dart';
import 'listen.dart';

StreamSubscription? _ibus;

class PageChapter extends StatefulWidget {
  PageChapter(
      {super.key,
      this.subject,
      this.type,
      this.sortId = 0,
      this.ids,
      this.fixid = 0,
      this.mainId = 0,
      this.childId = 0,
      this.isGoWatchVideo = false,
      this.luid = 0,
      this.recordTitle = ''});

  final int? subject;
  final String? type;
  final int? sortId;
  final List<dynamic>? ids;
  final int? fixid;
  final int? mainId;
  final int? childId;
  final int? luid;
  final String? recordTitle;

  bool? isGoWatchVideo;
  @override
  _PageChapterState createState() => _PageChapterState();
}

class _PageChapterState extends State<PageChapter>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  List _tabs = ['做题', '听题', '背题', '视频'];
  int teber = 0;
  Widget tabBody = Container(
    color: Colors.white,
  );

  Exercises? exercises;
  Listen? listen;
  Browse? browse;
  bool guide = true;
  bool hide = true;

  bool isOver = false; //考试是否结束
  int isJiXu = 0;
  bool needJump = false;
  bool ignore = false;

  /***************************
   * 标题倒计时
   ************************************/
  bool isActive = true;
  bool adding = true;

  //排序后的题目视频
  List<QuizVideoMo> videoMoArr = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // 注册监听器
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_LOGIN ||
          event.type == JkKey.EVENT_UPDATE_VIP) {
        refreshUI();
      } else if (event.type == JkKey.EVENT_TO_QUIZVIDEO) {
        jumpToVideo();
      }
    });
    insData();
  }

  insData() async {
    IQuiz.get().subject = widget.subject!;
    IQuiz.get().type = widget.type!;
    ignore = false;
    await initExercises();
    setTeber(0);
    setCorecctWrong(); //设置对的个数 错的个数
    if (JkKey.KEY_FIXED == IQuiz.get().type) {
      IQuiz.get().init((int value) {
        if (IQuiz.get().current <= IQuiz.get().totaltime) {
          if (adding) {
            IQuiz.get().current += 1000;
          }
        } else {
          isOver = true;
          if (!ignore) {
            justShow(JkKey.TIMEOVER);
          }
        }
      });
      IQuiz.get().errorList = []; //置空错题数组
    }
    Storage.getBool(Storage.guide, true).then((value) async {
      guide = value;
      refreshUI();
    });

    await setQuizVideo();
    if (widget.isGoWatchVideo ?? false) {
      setTeber(3);
    }
  }

  //查询题目视频信息
  Future<void> setQuizVideo() async {
    List<int> subIds = [];
    for (QuizMo mm in IQuiz.get().quizList) {
      subIds.add(mm.sub_Id);
    }
    print('kwcc ${subIds.length}');
    List quizData = await HttpDao.get().videoSubjectList(subIds);
    List<QuizVideoMo> moArr = (quizData as List<dynamic>)
        .map((e) => QuizVideoMo.fromJson(e))
        .toList();
    print('kwcc  ${moArr.length}');
    for (QuizMo mm in IQuiz.get().quizList) {
      for (QuizVideoMo nn in moArr) {
        if (mm.sub_Id == nn.subId) {
          mm.video_id = nn.id;
          mm.video_aliyunVid = nn.aliyunVid;
          mm.video_cover = nn.cover;
          mm.video_favoriteCover = nn.favoriteCover;
          videoMoArr.add(nn);
        }
      }
    }
    refreshUI();
  }

  //跳转视频播放
  void jumpToVideo() {
    try {
      if (widget.subject == 1) {
        BuryingPointUtils.instance.addPoint(
            buryingPointList: BuryingPointList(
                eventType: 1, entranceType: 14, action: 1, browseDuration: 0));
      } else if (widget.subject == 4) {
        BuryingPointUtils.instance.addPoint(
            buryingPointList: BuryingPointList(
                eventType: 1, entranceType: 35, action: 1, browseDuration: 0));
      }
      //判断是否登录
      if (!MainController.isLoginIntercept()) {
        return;
      }
      if (IQuiz.get()
          .quizList[IQuiz.get().currentpage]
          .video_aliyunVid
          .isEmpty) {
        EasyLoading.showToast('本题视频正在上架中');
        return;
      }

      int pageIdx = 0;
      for (var i = 0; i < videoMoArr.length; i++) {
        QuizVideoMo v = videoMoArr[i];
        QuizMo q = IQuiz.get().quizList[IQuiz.get().currentpage];
        if (v.subId == q.sub_Id) {
          pageIdx = i;
        }
      }

      Tools.get().jump(ShortVideo(
        dataList: videoMoArr,
        pageIdx: pageIdx,
        subject: widget.subject!,
        firstDoError: false,
        callback: (page) {
          refreshUI();

          int changePage = IQuiz.get().currentpage;
          for (var i = 0; i < IQuiz.get().quizList.length; i++) {
            QuizMo mm = IQuiz.get().quizList[i];
            QuizVideoMo nn = videoMoArr[page];
            if (mm.sub_Id == nn.subId) {
              changePage = i;
            }
          }
          IQuiz.get().currentpage = changePage;
          IEventBus.get().post(IEvent(JkKey.EVENT_QUIZ_INDEX, changePage));
          if (teber == 1) {
            Listen.resume();
          }
          setProgressNum();
        },
      ));
    } catch (e) {
      Logs.e('jumpVideoShort：error = ${e}');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // 移除监听器
    IEventBus.get().unregister(_ibus);
    IQuiz.get().disposeTiming();
    Listen.release();

    if (JkKey.KEY_CAPTER == IQuiz.get().type) {
      if (ITools.get().isLogin) {
        Storage.setInt(
            '${IQuiz.get().subject}page${JkKey.KEY_CAPTER}${widget.sortId!}',
            IQuiz.get().currentpage);
      } else {
        Storage.setInt(
            '${IQuiz.get().subject}page_visitor${JkKey.KEY_CAPTER}${widget.sortId!}',
            IQuiz.get().currentpage);
      }
      IEventBus.get().post(IEvent(JkKey.EVENT_UPDATE_DBQUIZ, ''));
    } else if (JkKey.KEY_NONE == IQuiz.get().type) {
      if (ITools.get().isLogin) {
        Storage.setInt(
            '${IQuiz.get().subject}page${JkKey.KEY_NONE}${widget.sortId!}',
            IQuiz.get().currentpage);
      } else {
        Storage.setInt(
            '${IQuiz.get().subject}page_visitor${JkKey.KEY_NONE}${widget.sortId!}',
            IQuiz.get().currentpage);
      }
      IEventBus.get().post(IEvent(JkKey.EVENT_UPDATE_DBQUIZ, ''));
    } else if (JkKey.KEY_KNOWLEDGE == IQuiz.get().type) {
      IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_KNOWLEDGE_RATE, ''));
    }

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      // 闲置状态 比如接打电话等，处于这种状态的应用程序应该假设它们可能在任何时候暂停。
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        if (JkKey.KEY_FIXED == IQuiz.get().type) {
          if (!isActive) {
            //app 从后台唤醒，进入前台
            isActive = true;
            if (IQuiz.get().current < IQuiz.get().totaltime) {
              adding = true;
            }
          } else {}
        }
        if (teber == 1) {
          Listen.resume();
        }
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        if (JkKey.KEY_FIXED == IQuiz.get().type) {
          //app 进入后台
          adding = false;
          isActive = false;
          Logs.e("模拟 进入后台${isActive}");
        }
        Listen.pause();
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void refreshUI() {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  Future initExercises() async {
    // 在创建新的Listen实例前，先释放旧的
    if (listen != null) {
      Listen.release();
    }

    exercises = Exercises(
      key: UniqueKey(),
      onResult: (bool flag, int position, String selection) {
        QuizMo? mo = IQuiz.get().getQuiz(position);
        if (mo == null) return;
        mo.selection = selection;
        //做题结果设置
        setResult(mo, flag);
        setCorecctWrong(); //设置对的个数 错的个数

        QuizUtils.get().saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);
        if (JkKey.KEY_FIXED != IQuiz.get().type) {
          QuizUtils.get().saveExercise(mo, IQuiz.get().subject);
          if (JkKey.KEY_LADDER == IQuiz.get().type && mo.selection.isNotEmpty) {
            LadderCacheMo ladder = new LadderCacheMo(
                mo.sub_Id,
                mo.sort_Id,
                IQuiz.get().subject,
                mo.selection,
                mo.examstate,
                mo.doTime,
                ITools.get().getUid('0'),
                widget.mainId!,
                widget.childId!,
                widget.luid!,
                0);
            QuizUtils.get().saveLadder(ladder);
          }
        }
        refreshUI();
      },
      onPage: (int index) {
        setProgressNum();
      },
      subject: widget.subject!,
    );
    listen = Listen(
        key: UniqueKey(),
        onPlaying: (int index) {
          setProgressNum();
        },
        onPaly: (int index) {
          hide = index != IQuiz.get().currentpage;
          refreshUI();
        });
    browse = Browse(
      key: UniqueKey(),
      onPage: (int index) {
        setProgressNum();
      },
      subject: widget.subject!,
    );
  }

  @override
  Widget build(BuildContext context) {
    Logs.e("@@@@@@ teber = ${teber}, hide = ${hide}");
    return UIUtils.statusBarDarkWidget(
      Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        body: WillPopScope(
          onWillPop: () {
            if (IQuiz.get().isShow) {
              IQuiz.get().isShow = false;
              IEventBus.get().post(IEvent(JkKey.EVENT_LISTEN_SHOW, ''));
              refreshUI();
            } else {
              doBack();
            }
            return Future.value(false);
          },
          child: Stack(
            children: <Widget>[
              Column(
                children: <Widget>[
                  Container(
                    height: UIUtils.dp(48) + UIUtils.statusHeight,
                    child: Container(
                      margin: EdgeInsets.only(top: UIUtils.statusHeight),
                      width: double.infinity,
                      height: UIUtils.dp(48),
                      child: Stack(
                        children: <Widget>[
                          Positioned(
                            left: 0,
                            top: 0,
                            bottom: 0,
                            child: JkInkWell(
                              child: Container(
                                padding: EdgeInsets.only(
                                    left: UIUtils.dp(12),
                                    right: UIUtils.dp(12)),
                                height: UIUtils.dp(48),
                                alignment: AlignmentDirectional.center,
                                child: Image.asset(
                                  'assets/home_img/nav_icon_back.png',
                                  width: UIUtils.dp(18),
                                  height: UIUtils.dp(18),
                                  fit: BoxFit.contain,
                                ),
                              ),
                              onTap: () {
                                print('isShow = ${IQuiz.get().isShow}');
                                if (IQuiz.get().isShow) {
                                  IQuiz.get().isShow = false;
                                  IEventBus.get().post(
                                      IEvent(JkKey.EVENT_LISTEN_SHOW, ''));
                                  refreshUI();
                                } else {
                                  doBack();
                                }
                              },
                            ),
                          ),
                          Container(
                            alignment: AlignmentDirectional.center,
                            child: Container(
                              width: UIUtils.dp(204),
                              alignment: AlignmentDirectional.center,
                              child: TTabBar(
                                  tabs: _tabs,
                                  height: UIUtils.dp(32),
                                  bg: Style.style_teb_line,
                                  labelStyle: Style.style_white_16_w500,
                                  unselectedLabelStyle:
                                      Style.style_text2_14_w500,
                                  indicator: Style.style_teb_indicator_line,
                                  current: teber,
                                  onTap: (int index) {
                                    setTeber(index);
                                  }),
                            ),
                          ),
                          Positioned(
                            top: UIUtils.dp(2),
                            right: UIUtils.dp(90),
                            child: Container(
                              width: 22,
                              height: 13,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4)),
                              ),
                              alignment: Alignment.center,
                              child: const Text(
                                'NEW',
                                style: TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          if (teber != 1 || !IQuiz.get().isShow)
                            Positioned(
                              right: 0,
                              top: 0,
                              bottom: 0,
                              child: JkInkWell(
                                child: Container(
                                  padding: EdgeInsets.only(
                                      left: UIUtils.dp(16),
                                      right: UIUtils.dp(16)),
                                  height: UIUtils.dp(48),
                                  alignment: AlignmentDirectional.center,
                                  child: Text(
                                    rightText(),
                                    style: Style.style_text1_14_w500,
                                  ),
                                ),
                                onTap: () {
                                  if (teber == 1) {
                                    IQuiz.get().isShow = true;
                                    IEventBus.get().post(
                                        IEvent(JkKey.EVENT_LISTEN_SHOW, ''));
                                    refreshUI();
                                  } else {
                                    DialogHelper.showDialogFontSize(context);
                                  }
                                },
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: tabBody,
                  ),
                  JkInkWell(
                    child: QuizTebWidget(
                        key: UniqueKey(),
                        teber: teber,
                        hide: hide,
                        delete: showDelete(),
                        collect: showCollect(),
                        submit: showSubmit(),
                        tovip: showTovip(),
                        onTap: (String type) {
                          if (type == 'delete') {
                            doDelete();
                          } else if (type == 'collect') {
                            doCollect();
                          } else if (type == 'rectify') {
                            doRectify();
                          } else if (type == 'submit') {
                            justShow(JkKey.SUBMIT); //点击交卷
                          } else if (type == 'tovip') {
                            if (widget.subject == 1) {
                              BuryingPointUtils.instance.addPoint(
                                  buryingPointList: BuryingPointList(
                                      eventType: 1,
                                      entranceType: 8,
                                      action: 1,
                                      browseDuration: 0));
                            } else {
                              BuryingPointUtils.instance.addPoint(
                                  buryingPointList: BuryingPointList(
                                      eventType: 1,
                                      entranceType: 25,
                                      action: 1,
                                      browseDuration: 0));
                            }
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 66, type: 'jingxuan600ti');
                          }
                        }),
                    onTap: () {
                      DialogHelper.showDialogNumList(
                          context,
                          IQuiz.get().quizList,
                          IQuiz.get().currentpage,
                          IQuiz.get().correct,
                          IQuiz.get().wrong, (String type, int position) {
                        if (type == 'submit') {
                          if (!ignore) {
                            justShow(JkKey.SUBMIT); //点击交卷
                          }
                        } else if (type == 'tovip') {
                          JumpUtils.get().jumpBuyVip();
                        } else {
                          IQuiz.get().currentpage = position;
                          IEventBus.get()
                              .post(IEvent(JkKey.EVENT_QUIZ_INDEX, position));
                          if (teber == 1) {
                            Listen.resume();
                          }
                          setProgressNum();
                        }
                      }, submit: showSubmit(), tovip: showTovip());
                    },
                  ),
                  Container(
                    height: MediaQuery.of(context).padding.bottom,
                    color: Style.bg,
                  ),
                ],
              ),
              if (guide)
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: JkInkWell(
                    child: Image.asset(
                      'assets/exercise/ic_quiz_guide.png',
                      width: UIUtils.width,
                      height: UIUtils.height,
                      fit: BoxFit.fill,
                    ),
                    onTap: () {
                      guide = false;
                      Storage.setBool(Storage.guide, false);
                      refreshUI();
                    },
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void setTeber(int index) {
    this.teber = index;
    if (index == 0) {
      tabBody = exercises!;
      Listen.pause();
    } else if (index == 1) {
      if (JkKey.KEY_FIXED == IQuiz.get().type && !ignore) {
        DialogHelper.showDialogTips(
            context, 0, '温馨提示', '切换听题，本次做题成绩将作废，是否确定前往？', '继续做题', '确定',
            (String? type) async {
          if (type == 'confirm') {
            ignore = true;
            tabBody = listen!;
            // Listen.resume(IQuiz.get().currentpage);
          }
        });
      } else {
        tabBody = listen!;
        // Listen.resume(IQuiz.get().currentpage);
      }
    } else if (index == 2) {
      if (JkKey.KEY_FIXED == IQuiz.get().type && !ignore) {
        DialogHelper.showDialogTips(
            context, 0, '温馨提示', '切换背题，本次做题成绩将作废，是否确定前往？', '继续做题', '确定',
            (String? type) async {
          if (type == 'confirm') {
            ignore = true;
            tabBody = browse!;
            Listen.pause();
          }
        });
      } else {
        tabBody = browse!;
        Listen.pause();
      }
    } else {
      int idx = 0;
      if (tabBody == exercises!) {
        idx = 0;
      } else if (tabBody == listen!) {
        idx = 1;
      } else if (tabBody == browse!) {
        idx = 2;
      }
      setTeber(idx);
      jumpToVideo();
    }
    refreshUI();
  }

  //做题结果设置
  Future<void> setResult(QuizMo mo, bool flag) async {
    mo.examstate = flag ? 1 : 2;
    mo.doTime = DateUtil.getNowDateStr();
    if (!ITools.get().isLogin &&
        (JkKey.KEY_CAPTER == IQuiz.get().type ||
            JkKey.KEY_NONE == IQuiz.get().type)) {
      mo.chapterstate = flag ? 1 : 2;
    } else if (JkKey.KEY_NEWLY == IQuiz.get().type ||
        JkKey.KEY_LADDER == IQuiz.get().type) {
      mo.flag = flag ? 1 : 2;
    }
    if (flag) {
      //做对题
      if (JkKey.KEY_ERROR == IQuiz.get().type) {
        List<String> errorToRight = await Storage.getStringList(
            '${JkKey.KEY_ERROR_TO_RIGHT}${ITools.get().uid}${widget.subject}');
        String subId = mo.sub_Id.toString();
        if (!errorToRight.contains(subId)) {
          try {
            List<String> errorToRight_new = List.from(errorToRight);
            errorToRight_new.add(subId);
            Storage.setStringList(
                '${JkKey.KEY_ERROR_TO_RIGHT}${ITools.get().uid}${widget.subject}',
                errorToRight_new);
            Logs.e('🐯做对题 ${errorToRight_new.length}');
          } catch (e) {
            Logs.e('🐯做对题 setResult errorToRight error: $e');
          }
        }
      }
    } else {
      //做错的情况
      mo.iserror = 1;
      if (JkKey.KEY_NONE != IQuiz.get().type &&
          JkKey.KEY_CAPTER != IQuiz.get().type &&
          JkKey.KEY_ERROR != IQuiz.get().type &&
          JkKey.KEY_COLLECT != IQuiz.get().type) {
        //做错的情况
        //添加错题
        IQuiz.get().errorList.add(mo);
      }
      Logs.e('错题：errorList.length = ${IQuiz.get().errorList.length}');
    }
  }

  //做题对错数设置
  void setCorecctWrong() {
    IQuiz.get().correct = 0;
    IQuiz.get().wrong = 0;
    IQuiz.get().right = 0;
    IQuiz.get().error = 0;
    for (QuizMo mo in IQuiz.get().quizList) {
      if (!ITools.get().isLogin &&
          (JkKey.KEY_CAPTER == IQuiz.get().type ||
              JkKey.KEY_NONE == IQuiz.get().type)) {
        switch (mo.chapterstate) {
          case 1:
            IQuiz.get().correct++;
            break;
          case 2:
            IQuiz.get().wrong++;
            break;
        }
      } else {
        switch (mo.examstate) {
          case 1:
            IQuiz.get().correct++;
            break;
          case 2:
            IQuiz.get().wrong++;
            break;
        }
        if (JkKey.KEY_NEWLY == IQuiz.get().type ||
            JkKey.KEY_LADDER == IQuiz.get().type) {
          switch (mo.flag) {
            case 1:
              IQuiz.get().right++;
              break;
            case 2:
              IQuiz.get().error++;
              break;
          }
        }
      }
    }
    if (JkKey.KEY_NEWLY == IQuiz.get().type ||
        JkKey.KEY_LADDER == IQuiz.get().type) {
      IQuiz.get().total = IQuiz.get().right + IQuiz.get().error; //统计总共做了多少题
    } else {
      IQuiz.get().total = IQuiz.get().correct + IQuiz.get().wrong; //统计总共做了多少题
    }

    IQuiz.get().score = IQuiz.get().quizList.length == 0
        ? 0
        : (IQuiz.get().correct * 100 / IQuiz.get().quizList.length).toInt();

    if (JkKey.KEY_FIXED == IQuiz.get().type && !ignore) {
      if (IQuiz.get().total == IQuiz.get().quizList.length) {
        justShow(JkKey.SUBMIT); //点击交卷
      } else if (!isOver && IQuiz.get().wrong >= 11) {
        //此处固定试题为每道1分
        justShow(JkKey.OVERSCORE); //超过最大错题数
      }
    }
  }

  //设置页码
  void setProgressNum() {
    if (IQuiz.get().quizList.length == 0) {
      IQuiz.get().currentpage = 0;
      return;
    }
    if (IQuiz.get().currentpage + 1 > IQuiz.get().quizList.length) {
      IQuiz.get().currentpage = IQuiz.get().quizList.length - 1;
    }
    if (IQuiz.get().currentpage - 1 < 0) {
      IQuiz.get().currentpage = 0;
    }
    refreshUI();
    IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_QUIZ, '1'));
  }

  bool showDelete() {
    if (JkKey.KEY_ERROR == IQuiz.get().type ||
        JkKey.KEY_COLLECT == IQuiz.get().type) {
      return true;
    }
    return false;
  }

  bool showCollect() {
    if (JkKey.KEY_COLLECT == IQuiz.get().type) {
      return false;
    }
    return true;
  }

  bool showRectify() {
    return true;
  }

  bool showSubmit() {
    if (JkKey.KEY_FIXED == IQuiz.get().type && !ignore) {
      return true;
    }
    return false;
  }

  bool showTovip() {
    if ((JkKey.KEY_KNOWLEDGE == IQuiz.get().type ||
            JkKey.KEY_FIXED == IQuiz.get().type ||
            JkKey.KEY_ERROR == IQuiz.get().type) ||
        JkKey.KEY_COLLECT == IQuiz.get().type ||
        ITools.get().isVip(IQuiz.get().subject)) {
      return false;
    }
    return true;
  }

  int collect() {
    if (teber == 1) {
      if (hide) {
        return 0;
      }
    }
    if (IQuiz.get().getQuiz(IQuiz.get().currentpage) != null) {
      return IQuiz.get().getQuiz(IQuiz.get().currentpage)!.collect;
    }
    return 0;
  }

  String rightText() {
    if (teber == 1) {
      return '文字';
    }
    return '设置';
  }

  Future doDelete() async {
    QuizMo? mo = IQuiz.get().getQuiz(IQuiz.get().currentpage);
    if (mo == null) return;
    EasyLoading.show();
    try {
      List<int> subIds = <int>[];
      subIds.add(mo.sub_Id);
      if (JkKey.KEY_ERROR == IQuiz.get().type) {
        final result = await HttpDao.get().deleteError(subIds: subIds);
        if (result) {
          mo.iserror = 0;
          await QuizUtils.get()
              .saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);
          //删除错题
          await IQuiz.get().removePosition(IQuiz.get().currentpage, () {
            initExercises();
            setTeber(teber);
            refreshUI();
          });
          UIUtils.showToastSafe(msg: '删除成功', gravity: 'center');
          IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_ERROR, ''));
        } else {
          UIUtils.showToastSafe(msg: '删除失败', gravity: 'center');
        }
      }
      if (JkKey.KEY_COLLECT == IQuiz.get().type) {
        final result = await HttpDao.get().cancelCollect(subIds: subIds);
        if (result) {
          mo.collect = 0;
          await QuizUtils.get()
              .saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);
          //删除收藏
          await IQuiz.get().removePosition(IQuiz.get().currentpage, () {
            initExercises();
            setTeber(teber);
          });
          UIUtils.showToastSafe(msg: '删除成功', gravity: 'center');
          ITrain.get().quesRefresh(true);
          IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_COLLECT, ''));
        } else {
          UIUtils.showToastSafe(msg: '删除失败', gravity: 'center');
        }
      }
    } catch (e) {
    } finally {
      EasyLoading.dismiss();
    }

    if (IQuiz.get().quizList.length == 0) {
      doBack();
    } else {
      setProgressNum();
      setCorecctWrong();
      Listen.checkRemove(IQuiz.get().currentpage);
    }
  }

  Future doCollect() async {
    QuizMo? mo = IQuiz.get().getQuiz(IQuiz.get().currentpage);
    if (mo == null) return;
    if (ITools.get().isLogin) {
      if (mo.collect == 1) {
        List<int> subIds = <int>[];
        subIds.add(mo.sub_Id);
        final result = await HttpDao.get().cancelCollect(subIds: subIds);
        if (result) {
          mo.collect = 0;
          await QuizUtils.get()
              .saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);
          UIUtils.showToastSafe(msg: '已取消收藏！', gravity: 'center');
          ITrain.get().quesRefresh(true);
          IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_COLLECT, ''));
        } else {
          UIUtils.showToastSafe(msg: '取消收藏失败', gravity: 'center');
        }
      } else {
        List<Map<String, dynamic>> favoriteList = <Map<String, dynamic>>[];
        Map<String, dynamic> map = Map<String, dynamic>();
        map['subId'] = mo.sub_Id;
        map['sortId'] = mo.sort_Id;
        favoriteList.add(map);
        final result =
            await HttpDao.get().saveCollect(favoriteList: favoriteList);
        if (result) {
          mo.collect = 1;
          await QuizUtils.get()
              .saveQuiz(mo, IQuiz.get().subject, ITools.get().isLogin);
          UIUtils.showToastSafe(msg: '收藏成功！', gravity: 'center');
          ITrain.get().quesRefresh(true);
          IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_COLLECT, ''));
        } else {
          UIUtils.showToastSafe(msg: '收藏失败', gravity: 'center');
        }
      }
    } else {
      if (mo.collect == 1) {
        mo.collect = 0;
        UIUtils.showToastSafe(msg: '已取消收藏！', gravity: 'center');
      } else {
        mo.collect = 1;
        UIUtils.showToastSafe(msg: '收藏成功！', gravity: 'center');
      }
      await QuizUtils.get().saveQuiz(mo, IQuiz.get().subject, false);
    }
    IEventBus.get().post(IEvent(JkKey.EVENT_REFRESH_QUIZ, ''));
  }

  Future doRectify() async {
    QuizMo? mo = IQuiz.get().getQuiz(IQuiz.get().currentpage);
    if (mo != null) {
      JumpUtils.get().jumpRectify(mo.sub_Id);
    }
  }

  //设置返回键提示
  void doBack() {
    bool back = onBack();
    if (!back) {
      finish();
    }
  }

  void finish() {
    Listen.release();
    DialogTips.dismiss();
    DialogBigPicture.dismiss();
    FontSizeWidget.dismiss();
    NumListWidget.dismiss();
    DialogSubmitPaper.dismiss();
    Navigator.of(context).pop();
  }

  void justShow(int flag) {
    if ((flag == JkKey.OVERSCORE || flag == JkKey.TIMEOVER) && isJiXu == 1) {
      return;
    }
    int count =
        (IQuiz.get().quizList == null) ? 0 : IQuiz.get().quizList.length;

    DialogHelper.showDialogSubmitPaper(
        context,
        ignore,
        flag,
        true,
        IQuiz.get().intToTime(),
        count - IQuiz.get().total,
        IQuiz.get().wrong,
        IQuiz.get().score, (String? type) {
      if (type == 'tovip') {
        DialogHelper.showDialogThreeTips(
            context,
            '温馨提示',
            '前往VIP课程需要结束本次考试，是否提交考试成绩？',
            '取消',
            '直接前往',
            '交卷',
            <int>[1, 2, 1], (String? type) {
          if (type == 'confirm') {
            if (!ignore) {
              IQuiz.get().setExamRecord(flag, IQuiz.get().intToTimeSS(), 2,
                  widget.fixid!, 0, widget.recordTitle!);
            }
            JumpUtils.get().jumpPromote(IQuiz.get().subject);
          } else if (type == 'middle') {
            JumpUtils.get().jumpPromote(IQuiz.get().subject);
          }
        });
      } else if (type == 'confirm') {
        if (flag == JkKey.OVERSCORE) {
          isOver = true;
          needJump = true;
        } else {
          needJump = true;
        }
        uploadExamText(flag);
      } else if (type == 'cancel') {
        if (flag == JkKey.OVERSCORE) {
          isJiXu = 1;
        } else if (flag == JkKey.TIMEOVER) {
          isJiXu = 1;
          needJump = false;
        }
      }
    });
  }

  void uploadExamText(int flag) {
    if (!ignore) {
      IQuiz.get().setExamRecord(flag, IQuiz.get().intToTimeSS(), 2,
          widget.fixid!, 0, widget.recordTitle!);
    }
    doJumpActivity();
  }

  void doJumpActivity() async {
    if (needJump) {
      IQuiz.get().disposeTiming();

      EasyLoading.show();
      try {
        if (JkKey.KEY_LADDER == IQuiz.get().type) {
          int testNum = 0;
          List<LadderCacheMo> list = <LadderCacheMo>[];
          List<Map<String, dynamic>> recordList = <Map<String, dynamic>>[];
          for (int i = 0; i < IQuiz.get().quizList.length; i++) {
            QuizMo mo = IQuiz.get().quizList[i];
            int id = DateUtil.getNowDateMs();
            int subId = mo.sub_Id;
            int sortId = mo.sort_Id;
            int examType = IQuiz.get().subject;
            String answer = mo.selection; //答案
            int result = mo.examstate;
            String doTime = mo.doTime; //yyyy-MM-dd HH:mm:ss
            String uid = ITools.get().getUid('0');
            int mainId = widget.mainId!;
            int childId = widget.mainId!;
            int luid = widget.luid!;
            if (mo.selection.isNotEmpty) {
              testNum++;
              list.add(LadderCacheMo(subId, sortId, examType, answer, result,
                  doTime, uid, mainId, childId, luid, 1));
            }

            recordList.add(ExerciseMo(
                    id, subId, sortId, examType, answer, result, doTime, uid)
                .toJson());
          }

          List<Map<String, dynamic>> saveList = <Map<String, dynamic>>[];
          int examType = HttpDao.get()
              .getLadderExamType(IQuiz.get().subject, ITools.get().trainType());
          Map<String, dynamic> map = Map<String, dynamic>();
          map['examType'] = examType;
          map['mainId'] = widget.mainId!;
          map['childId'] = widget.childId!;
          map['testNum'] = testNum;
          map['uid'] = widget.luid!;
          map['recordList'] =
              ITools.get().isVip(IQuiz.get().subject) ? recordList : [];
          saveList.add(map);

          final result = await HttpDao.get().saveLadder(list: saveList);
          if (result) {
            QuizUtils.get().saveLadderList(list);
            IEventBus.get().post(IEvent(JkKey.EVENT_UPDATE_LADDER, ''));
          }
        }
      } catch (e) {
      } finally {
        EasyLoading.dismiss();
      }
      finish();
      await IQuiz.get().jumpMockResult();
    }
  }

  bool onBack() {
    Logs.e('type = ${IQuiz.get().type}');
    if (JkKey.KEY_NEWLY == IQuiz.get().type ||
        JkKey.KEY_MIXED == IQuiz.get().type ||
        JkKey.KEY_LADDER == IQuiz.get().type) {
      Logs.e('total = ${IQuiz.get().total}');
      if (IQuiz.get().total == 0) {
        return false;
      } else {
        showbackAlert();
        return true;
      }
    } else if (JkKey.KEY_FIXED == IQuiz.get().type) {
      if (IQuiz.get().total == 0 || ignore) {
        IQuiz.get().disposeTiming();
        return false;
      } else {
        justShow(JkKey.BACKCLICK);
        return true;
      }
    } else {
      IQuiz.get().setRecord();
      return false;
    }
  }

  //提示
  void showbackAlert() async {
    if (JkKey.KEY_LADDER == IQuiz.get().type) {
      needJump = true;
      IQuiz.get().setRecord();
      doJumpActivity();
    } else if (JkKey.KEY_NEWLY == IQuiz.get().type) {
      DialogHelper.showDialogTips(
          context,
          0,
          '温馨提示',
          '您本次做了${IQuiz.get().total}道题，做对${IQuiz.get().total - IQuiz.get().errorList.length}道题，做错${IQuiz.get().errorList.length}道题',
          '继续做题',
          '返回首页', (String? type) async {
        if (type == 'confirm') {
          IQuiz.get().setRecord();
          finish();
        } else if (type == 'cancel') {}
      });
    } else {
      DialogHelper.showDialogTips(
          context,
          0,
          '温馨提示',
          '您本次做了${IQuiz.get().total}道题，做对${IQuiz.get().total - IQuiz.get().errorList.length}道题，做错${IQuiz.get().errorList.length}道题',
          '查看错题',
          '返回首页', (String? type) async {
        if (type == 'confirm') {
          IQuiz.get().setRecord();
          finish();
        } else if (type == 'cancel') {
          if (IQuiz.get().errorList != null &&
              IQuiz.get().errorList.length > 0) {
            List<QuizMo> list = <QuizMo>[];
            list.addAll(IQuiz.get().errorList);
            JumpUtils.get().jumpBrowseExercise(
                IQuiz.get().type, IQuiz.get().subject,
                list: list);
          } else {
            UIUtils.showToastSafe(msg: '您本次练习错题数为0', gravity: 'center');
          }
        }
      });
    }
  }
}
