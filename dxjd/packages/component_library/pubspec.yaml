name: component_library
description: "A new Flutter project."
version: 0.0.1
publish_to: 'none'

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_easyloading: ^3.0.5
  flutter_localizations:
    sdk: flutter
  flutter_screenutil: ^5.9.0
  permission_handler: ^10.4.5
  device_info_plus: ^10.0.1
  package_info_plus: ^7.0.0
  connectivity_plus: ^6.0.4
  flutter_image_compress: ^2.2.0
  amap_flutter_map: ^3.0.0
  photo_view: ^0.14.0
  cached_network_image: ^3.2.0
  fluwx: ^4.4.3 # WeChat
  uuid: ^3.0.6
  get: ^4.7.2
  encrypt:
  dxjd:
    path: ../..
  timing:
    path: ../timing
  # mPass小程序容器
  mop:
    git:
      url: https://github.com/Kayouyou/mop-flutter-sdk.git
  amap_flutter_location: ^3.0.0
  tools:
    path: ../tools
  beizi_ad_sdk:
    path: ../../plugins/beizi_ad_sdk
  home_repository:
    path: ../home_repository
  home:
    path: ../features/home
  user_repository:
    path: ../user_repository
  timing_repository:
    path: ../timing_repository
  quiz:
    path: ../quiz
  api:
    path: ../api
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
