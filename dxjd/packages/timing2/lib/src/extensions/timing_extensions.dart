import 'package:flutter/material.dart';
import 'package:tools/tools.dart';

import '../network/api_client.dart';
import '../storage/hive_storage_impl.dart';

import '../core/timing_interface.dart';
import '../core/timing_manager.dart';
import '../events/event_bus.dart';
import '../events/timing_events.dart';
import '../models/timing_record.dart';
import '../preconditions/precondition_interface.dart';
import '../preconditions/standard_preconditions.dart';
import '../ui/dialogs/timing_dialogs.dart';

/// 计时服务扩展
extension TimingServiceExtension on ITimingService {
  /// 添加标准前置条件
  void addStandardPreconditions(BuildContext context) {
    // 登录检查
    addPrecondition(LoginPrecondition());

    // 计时开关检查
    addPrecondition(TimingSwitchPrecondition());

    // 相机权限检查
    addPrecondition(CameraPermissionPrecondition(context: context));

    // 驾校绑定检查
    addPrecondition(SchoolBindingPrecondition());

    // 备案状态检查
    addPrecondition(RecordStatusPrecondition());

    // 系统时间检查
    addPrecondition(SystemTimePrecondition());

    // 网络连接检查
    addPrecondition(NetworkConnectionPrecondition());

  }

  /// 显示开始计时弹窗
  Future<bool> showStartTimingDialog(BuildContext context, int subject) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              TimingDialogs.startTimingDialog(context, subject),
        ) ??
        false;
  }

  /// 显示结束计时弹窗
  Future<bool> showEndTimingDialog(BuildContext context, int subject) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => TimingDialogs.endTimingDialog(context, subject),
        ) ??
        false;
  }

  /// 显示过程拍照弹窗
  Future<bool> showProcessPhotoDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => TimingDialogs.processPhotoDialog(context),
        ) ??
        false;
  }

  /// 显示未完成计时弹窗
  Future<bool> showUnfinishedTimingDialog(
      BuildContext context, TimingRecord record) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              TimingDialogs.unfinishedTimingDialog(context, record),
        ) ??
        false;
  }

  /// 监听计时事件
  void listenToEvents(void Function(TimingEvent event) onEvent) {
    TimingEventBus.instance.eventStream.listen(onEvent);
  }

  /// 监听特定类型的计时事件
  void listenToEventType<T extends TimingEvent>(
      void Function(T event) onEvent) {
    TimingEventBus.instance.on<T>(onEvent);
  }
}

/// 计时管理器扩展
extension TimingManagerExtension on TimingManager {
  /// 快速初始化
  static Future<TimingManager> quickInit({
    required BuildContext context,
  }) async {
    // 初始化计时管理器
    final manager = await TimingManager.initializeManager(
      storage: await initStorage(),
      api: initApi(),
      context: context,
    );

    // 添加标准前置条件
    manager.addStandardPreconditions(context);

    return manager;
  }

  /// 初始化存储
  static Future<HiveTimingStorage> initStorage() async {
    final storage = HiveTimingStorage();
    await storage.initialize();
    return storage;
  }

  /// 初始化API
  static ApiClient initApi() {
    // 注意：这里需要根据实际情况调整
    // 假设 HttpDao.get() 已经实现了 Api 接口
    return ApiClient(api: HttpDao.get() as dynamic);
  }
}

/// 自定义前置条件扩展
extension CustomPreconditionExtension on CustomPrecondition {
  /// 创建自定义前置条件
  static CustomPrecondition create({
    required String id,
    required String name,
    required String description,
    required Future<bool> Function() checkFunction,
    String? failureMessage,
  }) {
    return CustomPrecondition(
      id: id,
      name: name,
      description: description,
      checkFunction: () async {
        try {
          final result = await checkFunction();
          if (result) {
            return PreconditionResult.success();
          } else {
            return PreconditionResult.failure(failureMessage ?? '前置条件检查失败');
          }
        } catch (e) {
          return PreconditionResult.failure('前置条件检查异常: $e');
        }
      },
    );
  }
}
