name: timing2
description: 优化后的计时模块，提供更好的封装和可扩展性
version: 0.0.1
publish_to: none

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  tools:
    path: ../tools

  # 数据存储
  hive: ^2.0.6
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1

  # 工具类
  common_utils: ^2.1.0
  flutter_easyloading: ^3.0.3
  rxdart: ^0.27.1

  # 计时器
  stop_watch_timer: ^1.3.1

  # 相机和图片处理
  camera: ^0.10.0+1
  camerawesome: ^0.3.3
  image_picker: ^1.1.1
  crop_your_image: ^1.0.2
  path_provider: ^2.0.9
  extended_image: ^6.1.0
  image_editor: ^1.3.0
  flutter_native_image: ^0.0.6
  flutter_image_compress: ^2.2.0

  # 微信相关
  fluwx: ^4.4.3

  # UI相关
  flutter_screenutil: ^5.9.0

  # 项目内部依赖
  api:
    path: ../api
  component_library:
    path: ../component_library

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.1.7
  permission_handler: ^10.0.0

dependency_overrides:
  timing: 1.0.1
