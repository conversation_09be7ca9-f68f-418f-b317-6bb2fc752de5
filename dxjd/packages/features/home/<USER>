name: home
description: "A new Flutter project."
version: 0.0.1
homepage:

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_carousel_widget: ^2.2.0
  common_utils: ^2.0.0
  easy_refresh: ^3.4.0
  flutter_screenutil: ^5.9.0
  scrollview_observer: ^1.20.0
  url_launcher: ^6.3.0
  pausable_timer: ^3.1.0+3
  app_links: ^6.4.0

  # 汉字转拼音
  lpinyin: ^2.0.0
  azlistview: ^2.0.0
#  github_language_colors:
#    git:
#      url: https://github.com/Sky24n/github_language_colors.git
  amap_flutter_location: ^3.0.0
  get: ^4.7.2
  flukit: ^3.0.1
  percent_indicator: ^4.2.3
  amap_flutter_map: ^3.0.0
  login:
    path: ../../features/login
  mine:
    path: ../../features/mine
  component_library:
    path: ../../component_library
  home_repository:
    path: ../../home_repository
  user_repository:
    path: ../../user_repository
  push_repository:
    path: ../../push_repository
  domain_models:
    path: ../../domain_models
  timing_repository:
    path: ../../timing_repository
  quiz:
    path: ../../quiz
  timing:
    path: ../../timing
  tools:
    path: ../../tools
  dxjd:
    path: ../../..
  exercise_bloc:
    path: ../../exercise_bloc
  routemaster: ^1.0.1
  easy_rich_text: '^2.0.0'
  flutter_easyloading: ^3.0.5
  flutter_staggered_grid_view: ^0.7.0
  permission_handler:
  video_player: ^2.8.6
  # mPass小程序容器
  mop:
    git:
      url: https://github.com/Kayouyou/mop-flutter-sdk.git
  chewie:
    path: ../../../plugins/chewie-1.8.3
  # 视频缓存插件
  video_cache:
    path: ../../../plugins/video_cache
  flutter_barrage_craft: ^1.0.1

#  flutter_aliplayer: ^6.7.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true