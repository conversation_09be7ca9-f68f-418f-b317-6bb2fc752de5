import 'dart:async';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:home_repository/home_repository.dart';
import 'package:api/api.dart';
import 'package:push_repository/push_repository.dart';
import 'package:quiz/quiz.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';

class SectionOneController extends GetxController {
  List img = [
    "img_zixun",
    "img_zixun_er",
  ];

  List zhiboimg = [
    "img_zhibo_er",
    "img_zhibo",
    "img_zhibo_san",
  ];
  GetTheCertificateDataDm? certificateDataDm;
  Map subjectOneTimeTable = {};
  ArticleInfoDataDm? subjectOneArticleInfo;
  VipSubModel? vipModel;
  HotLiveList? hotLiveList;
  BringVIewExaminationModel? bringVIewExaminationModel; //带你看考场数据
  bool isDispose = false;
  int page = 0;
  CarouselController carouselController = CarouselController();
  int total = 0; //题目个数

  bool isNotificationOpen = true;
  //是否显示通知权限tips
  bool isTipsOpen = false;
  final ScrollController scrollController = ScrollController();
  Timer? _timer;
  final List<Color> colors = [Colors.black, Colors.red];
  int _currentIndex = 0;
  ScrollController refreshScrollController = ScrollController();
  //完成的题目数
  int completed = 0;
  //我的成绩
  Map myAchievementData = {};
  //banner是否开启自动滚动
  bool bannerIsStartScroll = true;
  //banner 是否在可见区域
  bool bannerIsExitView = true;
  StreamSubscription? _ibus;
  //模考平均分
  int examAverage = 0;
  //banner列表
  List<BannerDataDm>? bannerList;
  late HomeRepository homeRepository;
  late PushRepository pushRepository;
  late UserRepository userRepository;
  late TimingRepository timingRepository;
  @override
  void onInit() {
    homeRepository = Get.find<MainController>().homeRepository;
    pushRepository = Get.find<MainController>().pushRepository;
    userRepository = Get.find<MainController>().userRepository;
    timingRepository = Get.find<MainController>().timingRepository;
    refreshScrollController.addListener(() {
      if(refreshScrollController.offset >= 100.h){
        if(bannerIsStartScroll){
          carouselController.stopAutoPlay();
          bannerIsStartScroll = false;
          bannerIsExitView = false;
          update();
        }
      }else{
        if(!bannerIsStartScroll){
          carouselController.startAutoPlay();
          bannerIsStartScroll = true;
          bannerIsExitView = true;
          update();
        }
      }
    });
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_REFRESH_MY_RECORD) {
        if (IHome.get().quiz1 != null) {
          total = IHome.get().quiz1!.total;
          completed = IHome.get().quiz1!.done;
          update();
        } else if (event.type == JkKey.EVENT_HOURS_UPDATE) {
          refreshUI();
        }
        if (IHome.get().exam1 != null) {
          examAverage = IHome.get().exam1!.average;
          update();
        }
      }
    });
    if (IHome.get().quiz1 != null) {
      total = IHome.get().quiz1!.total;
      completed = IHome.get().quiz1!.done;
    }
    if (IHome.get().exam1 != null) {
      examAverage = IHome.get().exam1!.average;
    }
    super.onInit();
  }

  //获取带你看考场视频数据
  Future<void> _getSpringViewExaminationData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    try {
      String cityCode = (division ?? 440100).toString();
      bringVIewExaminationModel = await homeRepository.getBringViewExamination(
        index: 1,
        rows: 200,
        city: int.parse(cityCode),
      );
    } catch (e) {
      debugPrint("=============$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          bringVIewExaminationModel = null;
        }
      }
    } finally {
      update();
    }
  }

  void _getNotificationPermission() async {
    final isNotificationEnabled = await pushRepository.isNotificationEnabled();
    int outTimes = await Storage.getInt('notification_open_time_tips', 0);
    final nowTime = DateTime.now().millisecondsSinceEpoch;
    if (nowTime - outTimes > 1000 * 60 * 60 * 24) {
      isTipsOpen = true;
    } else {
      isTipsOpen = false;
    }
    isNotificationOpen = isNotificationEnabled;
    update();
  }

  //获取热门直播列表
  _getLiveList() async {
    try {
      hotLiveList = await homeRepository.getHotLiveList(true);
      update();
    } catch (e) {}
  }

  //获取课程列表
  _getCourseList() async {
    try {
      final result = await homeRepository.getTheCertificateData(
          category: ['xw-shopping-dxjd-xxhk'],
          categorySub: ['xw-sp-dxjd-xxhk-zxxx']);
      certificateDataDm = result;
      update();
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show('获取数据失败，请稍后再试');
      }
    }
  }

  Future<bool> getExamProductIsBuy(String id) async {
    if (userRepository.vipProductMap.vip1 &&
        userRepository.vipProductMap.vip2 &&
        userRepository.vipProductMap.vip3 &&
        userRepository.vipProductMap.vip4) {
      return true;
    }
    try {
      final result = await homeRepository.getTheCertificateStatusData(id);
      if (result["Status"] == 1) {
        return true;
      }
      return false;
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show('获取数据失败，请稍后再试');
      }
      return false;
    }
  }

  goViewExaminationPage(
      String? id, BringViewListElement? item, int tIndex) async {
    if (!MainController.isLoginIntercept()) {
      return;
    }
    if (await getExamProductIsBuy(id ?? "")) {
      Get.toNamed(AppRoutes.bringViewExamination, arguments: {
        'videoId': item?.examCatalogInfo?[tIndex].videoId,
        'videoUrlImage': item?.examCatalogInfo?[tIndex].cover,
        'examId': item?.id
      });
    } else {
      String cityCode = "";
      String cityName = "";
      ExaminationController examinationController =
          Get.find<ExaminationController>(
              tag: ExaminationPageState.key.toString());
      UserAccount? userInfo =
          await examinationController.userRepository.userAccountDM;
      if (examinationController.selectCityMap.isEmpty) {
        cityCode =
            userInfo?.reqisterDivision.toString().substring(0, 4) ?? "440100";
        cityName = cityMap[cityCode.toString()] ?? "广州市";
        cityCode = "${cityCode}00";
      } else {
        cityCode = examinationController.selectCityMap['division'];
        cityName = examinationController.selectCityMap['name'];
      }
      JumpSmallProgramUtils.jump(
          '/pages/shop/goods/site/site?id=${item?.id}&city=$cityCode&cityname=$cityName',
          'fc2259710004813765');
    }
  }

//  获取Vip统计人数
  _getVipCount() async {
    try {
      vipModel = await homeRepository.getVipHelpNum();
    } catch (e) {}
  }

  Future<void> _getSubjectOneArticleInfo() async {
    try {
      final result = await homeRepository.getArticleInfoDm();
      if (result != null) {
        subjectOneArticleInfo = result;
        update();
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show("网络错误，请稍后重试");
      }
    }
  }

  //我的成绩单数据查询
  Future<void> _getMyAchievementData() async {
    try {
      final result = await homeRepository.getMyAchievementData();
      if (result != null) {
        myAchievementData = result;
        update();
      }
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show("网络错误，请稍后重试");
      }
    }
  }

  ///获取科一banner
  _getSubjectOneBanner() async {
    final String token = await userRepository.getUserToken() ?? "";
    if (token.isEmpty) {
      try {
        var bannerList = await homeRepository.getHomeNoTokenBanner();
        this.bannerList = bannerList;
        // update();
      } catch (e) {}
    } else {
      List<int> vipSubject = [];
      if (userRepository.getSubVip(1)) {
        vipSubject.add(1);
      }
      try {
        var bannerList = await homeRepository.getHomeBanner(
            district: int.parse(homeRepository.cityInfo.cityCode ?? "440100"),
            subject: [1],
            vipSubject: vipSubject);
        this.bannerList = bannerList;
        // update();
      } catch (e) {
        print("error:$e");
      }
    }
  }

  Future<bool> getProductIsBuy(
      CertificateDetailModel? certificateDetailData) async {
    try {
      final result = await homeRepository.getTheCertificateStatusData(
          certificateDetailData?.products[0].id ?? "");
      if (result["Status"] == 1) {
        return true;
      }
      return false;
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show('获取数据失败，请稍后再试');
      }
      return false;
    }
  }

  //  获取数据
  getData() async {
    _getSubjectOneBanner();
    _getSubjectOneArticleInfo();
    _getMyAchievementData();
    _getLiveList();
    _getSpringViewExaminationData(
      division: stringToIntConverter(homeRepository.cityInfo.cityCode),
      city: homeRepository.cityInfo.cityName,
    );
    _getCourseList();
    _getVipCount();
    //获取通知权限是否开启
    // 是否是第一次启动app
    bool isFirstEnterApp =
        await PreferencesService().getBool('fist_run') ?? true;
    if (!isFirstEnterApp) {
      _getNotificationPermission();
    }
  }

  void refreshUI() async {
    try {
      update();
    } catch (e) {}
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _currentIndex = (_currentIndex + 1) % colors.length;
      update();
      // 滚动到下一个颜色
      if (scrollController.hasClients) {
        scrollController.animateTo(
          _currentIndex * 43.h, // 每个颜色块的高度是40
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void onReady() {
    getData();
    _startTimer();
    super.onReady();
  }

  @override
  void onClose() {
    isDispose = true;
    _timer?.cancel();
    scrollController.dispose();
    refreshScrollController.dispose();
    IEventBus.get().unregister(_ibus);
    super.onClose();
  }
}
