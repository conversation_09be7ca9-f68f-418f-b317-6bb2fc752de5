import 'dart:async';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:home_repository/home_repository.dart';
import 'package:push_repository/push_repository.dart';
import 'package:quiz/quiz.dart';
import 'package:api/api.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';

import '../../../home.dart';

class SectionFourController extends GetxController {
  List img = [
    "img_zixun",
    "img_zixun_er",
  ];

  List zhiboimg = [
    "img_zhibo_er",
    "img_zhibo",
    "img_zhibo_san",
  ];

  Map subjectFourTimeTable = {};
  ArticleInfoDataDm? subjectFourArticleInfo; //资讯文章
  VipSubModel? vipModel;
  // Map subjectFourTimeTable={};

  int page = 0;
  HotLiveList? hotLiveList;
  int total = 0; //题目个数
  //完成的题目数
  int completed = 0;

  StreamSubscription? _ibus;
  //科目四banner
  List<BannerDataDm>? bannerList;

  final ScrollController scrollController = ScrollController();
  Timer? _timer;
  final List<Color> colors = [Colors.black, Colors.red];
  int currentIndex = 0;
  ScrollController refreshScrollController = ScrollController();
  Map myAchievementData = {};
  BringVIewExaminationModel? bringVIewExaminationModel; //带你看考场数据
  //模考平均分
  int examAverage = 0;
  late HomeRepository homeRepository;
  late PushRepository pushRepository;
  late UserRepository userRepository;
  CarouselController carouselController = CarouselController();
  //banner是否开启自动滚动
  bool bannerIsStartScroll = true;
  //banner 是否在可见区域
  bool bannerIsExitView = true;
  //获取带你看考场视频数据
  Future<void> _getSpringViewExaminationData(
      {division,
      String? city,
      bool isCallBack = false,
      isSelect = false}) async {
    try {
      String cityCode = (division ?? 440100).toString();
      bringVIewExaminationModel = await homeRepository.getBringViewExamination(
        index: 1,
        rows: 200,
        city: int.parse(cityCode),
      );
    } catch (e) {
      debugPrint("=============$e");
      if (e is DomainException) {
        if (e.code == 9 && e.message == "没有相关数据") {
          bringVIewExaminationModel = null;
        }
      }
    } finally {
      update();
    }
  }

  onRefresh() {
    _getSubjectFourBanner();
    _getMyAchievementData();
    _getSubjectFourArticleInfo();
    _getSpringViewExaminationData(
      division: stringToIntConverter(homeRepository.cityInfo.cityCode),
      city: homeRepository.cityInfo.cityName,
    );
    _getLiveList();
  }

  //我的成绩单数据查询
  Future<void> _getMyAchievementData() async {
    try {
      final result = await homeRepository.getMyAchievementData();
      if (result != null) {
        myAchievementData = result;
        update();
      }
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show("网络错误，请稍后重试");
      }
    }
  }

  ///获取科目四banner
  _getSubjectFourBanner() async {
    final String token = await userRepository.getUserToken() ?? "";
    if (token.isEmpty) {
      try {
        var bannerList = await homeRepository.getHomeNoTokenBanner();
        this.bannerList = bannerList;
        // update();
      } catch (e) {}
    } else {
      List<int> vipSubject = [];
      if (userRepository.getSubVip(4)) {
        vipSubject.add(4);
      }
      try {
        var bannerList = await homeRepository.getHomeBanner(
            district: int.parse(homeRepository.cityInfo.cityCode ?? "440100"),
            subject: [4],
            vipSubject: vipSubject);
        this.bannerList = bannerList;
        // update();
      } catch (e) {
        print("error:$e");
      }
    }
  }

//  获取Vip统计人数
  _getVipCount() async {
    try {
      vipModel = await homeRepository.getVipHelpNum();
    } catch (e) {}
  }

  Future<void> _getSubjectFourArticleInfo() async {
    try {
      final result = await homeRepository.getArticleInfoDm();
      if (result != null) {
        subjectFourArticleInfo = result;
        update();
      }
    } catch (e) {
      if (e is DomainException) {
        Toast.show(e.message);
      } else {
        Toast.show("网络错误，请稍后重试");
      }
    }
  }

  //获取热门直播列表
  _getLiveList() async {
    try {
      hotLiveList = await homeRepository.getHotLiveList(false);
      update();
    } catch (e) {}
  }

  //科四学时历程信息
  // _getSubjectFourTimeTable() async {
  //   try {
  //     final result =await homeRepository.getSubjectOneTimeTable(subject: 4);
  //     if(result!=null) {
  //       setState(() {
  //         subjectFourTimeTable = result;
  //       });
  //     }
  //   }catch(e){
  //     if(e is DomainException){
  //       Toast.show(e.message);
  //     }else{
  //       Toast.show("网络错误，请稍后重试");
  //     }
  //   }
  // }
  Future<bool> getExamProductIsBuy(String id) async {
    if (userRepository.vipProductMap.vip1 &&
        userRepository.vipProductMap.vip2 &&
        userRepository.vipProductMap.vip3 &&
        userRepository.vipProductMap.vip4) {
      return true;
    }
    try {
      final result = await homeRepository.getTheCertificateStatusData(id);
      if (result["Status"] == 1) {
        return true;
      }
      return false;
    } catch (e) {
      if (e is DomainException) {
        // Toast.show(e.message);
      } else {
        Toast.show('获取数据失败，请稍后再试');
      }
      return false;
    }
  }

  goViewExaminationPage(
      String? id, BringViewListElement? item, int tIndex) async {
    if (!MainController.isLoginIntercept()) {
      return;
    }
    if (await getExamProductIsBuy(id ?? "")) {
      Get.toNamed(AppRoutes.bringViewExamination, arguments: {
        'videoId': item?.examCatalogInfo?[tIndex].videoId,
        'videoUrlImage': item?.examCatalogInfo?[tIndex].cover,
        'examId': item?.id
      });
    } else {
      String cityCode = "";
      String cityName = "";
      ExaminationController examinationController =
          Get.find<ExaminationController>(
              tag: ExaminationPageState.key.toString());
      UserAccount? userInfo =
          await examinationController.userRepository.userAccountDM;
      if (examinationController.selectCityMap.isEmpty) {
        cityCode =
            userInfo?.reqisterDivision.toString().substring(0, 4) ?? "440100";
        cityName = cityMap[cityCode.toString()] ?? "广州市";
        cityCode = "${cityCode}00";
      } else {
        cityCode = examinationController.selectCityMap['division'];
        cityName = examinationController.selectCityMap['name'];
      }
      JumpSmallProgramUtils.jump(
          '/pages/shop/goods/site/site?id=${item?.id}&city=$cityCode&cityname=$cityName',
          'fc2259710004813765');
    }
  }

  void refreshUI() async {
    try {
      update();
    } catch (e) {}
  }

  @override
  void onInit() {
    homeRepository = Get.find<MainController>().homeRepository;
    pushRepository = Get.find<MainController>().pushRepository;
    userRepository = Get.find<MainController>().userRepository;
    refreshScrollController.addListener(() {
      if(refreshScrollController.offset >= 100.h){
        if(bannerIsStartScroll){
          carouselController.stopAutoPlay();
          bannerIsStartScroll = false;
          bannerIsExitView = false;
          update();
        }
      }else{
        if(!bannerIsStartScroll){
          carouselController.startAutoPlay();
          bannerIsStartScroll = true;
          bannerIsExitView = true;
          update();
        }
      }
    });
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_REFRESH_MY_RECORD) {
        if (IHome.get().quiz4 != null) {
          total = IHome.get().quiz4!.total;
          completed = IHome.get().quiz4!.done;
          update();
        }
      } else if (event.type == JkKey.EVENT_HOURS_UPDATE) {
        refreshUI();
      }
      if (IHome.get().exam4 != null) {
        examAverage = IHome.get().exam4!.average;
      }
    });
    if (IHome.get().quiz4 != null) {
      total = IHome.get().quiz4!.total;
      completed = IHome.get().quiz4!.done;
    }
    if (IHome.get().exam4 != null) {
      examAverage = IHome.get().exam4!.average;
    }
    super.onInit();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      currentIndex = (currentIndex + 1) % colors.length;
      update();
      // 滚动到下一个颜色
      if (scrollController.hasClients) {
        scrollController.animateTo(
          currentIndex * 40.h, // 每个颜色块的高度是40
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void onReady() {
    onRefresh();
    _getVipCount();
    _startTimer();
    super.onReady();
  }

  @override
  void onClose() {
    IEventBus.get().unregister(_ibus);
    _timer?.cancel();
    refreshScrollController.dispose();
    scrollController.dispose();
    super.onClose();
  }
}
