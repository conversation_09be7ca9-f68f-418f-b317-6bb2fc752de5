import 'package:component_library/component_library.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:login/login.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:mine/mine.dart';
import 'package:user_repository/user_repository.dart';

import '../../../home.dart';

class TopicSelectPage extends StatefulWidget {
  const TopicSelectPage({super.key});

  @override
  State<TopicSelectPage> createState() => _TopicSelectPageState();
}

class _TopicSelectPageState extends State<TopicSelectPage> {
  List iconList = [
    "baoming_icon_xaioche",
    "baoming_icon_motuo",
    "baoming_icon_huoche",
    "baoming_icon_keche",
    "baoming_icon_guache",
  ];
  List titleList = [
    "小车",
    "摩托车",
    "货车",
    "客车",
    "挂车",
  ];

  List<Map<String, String>> titleListMap = [
    {"小车": 'C1'},
    {"摩托车": 'D'},
    {"货车": 'A2'},
    {"客车": 'A1'},
    {"挂车": 'C6'},
  ];
  List<String> tipList = [
    "C1/C2/C3",
    "D/E/F",
    "A2/B2",
    "A1/A3/B1",
    "C6",
  ];

  var newIndex = 0;
  bool isSingUp = false;
  var _selectCity = '请选择城市，不同城市题库不一样';
  var saveCity, saveCityDivision;
  late UserRepository userRepository ;
  UserAccount? userAccount;
  var _carType = 'C1';
  bool isUserSelect = false;
  Map _selectCityMap = {};

  late MainController _mainController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _mainController = Get.find<MainController>();

    userRepository = _mainController.userRepository;

    userRepository.getUserAccountInfo().then((value) {
      _selectCity=cityMap[
      value?.reqisterDivision.toString().substring(0, 4)];
      saveCityDivision = value?.reqisterDivision;
      setState(() {});
    });

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: assImg2(img: 'topic_select_title', w: 110.0.w, h: 15.0.h),
          centerTitle: true,
          leading: IconButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: assImg2(img: 'topic_select_black_back', w: 24.w))),
      body: Stack(
        children: [
          assImg2(img: 'topic_select_bg', fit: BoxFit.fill),
          Column(
            children: [
              Container(
                height: 92.h,
                padding: EdgeInsets.only(left: 20.w),
                margin: EdgeInsets.only(top: 103.h, right: 11.w, left: 11.w),
                decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(10.r)),
                child: Stack(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        app16spA1Text("学车城市",
                            fontFamily: 'PingFangSC-Semibold'),
                        GestureDetector(
                            onTap: () {
                              Get.toNamed(AppRoutes.cityListSelectPage)
                                  ?.then((value) {
                                if (value != null) {
                                  setState(() {
                                    isUserSelect = true;
                                    _selectCityMap = value;
                                    if (_selectCityMap['division'].isNotEmpty) {
                                      _selectCity = _selectCityMap['name'];
                                      saveCityDivision = int.parse(_selectCityMap['division']) ;
                                      // cityToRegisterDivisionMap[_selectCityMap['division']]=_selectCityMap['name'];
                                    }
                                  });
                                }
                              });
                            },
                            child: SizedBox(
                              width: double.infinity,
                              // height: 40.h,
                              child: Row(
                                children: [
                                  assImg2(
                                          img: "topic_select_location",
                                          w: 24.w,
                                          h: 24.h)
                                      .paddingOnly(right: 11.w),
                                  // Expanded(flex: 1, child: app16spA1Text(isUserSelect?_selectCity:saveCity??_selectCity)),
                                  Expanded(
                                      flex: 1,
                                      child: app14sp268Text(_selectCity,
                                          color: Color(0xFFAAAAAA),
                                          fontFamily: 'PingFangSC-Medium')),
                                  // assImg(
                                  //     img: "icon_city_more", w: 20.w, h: 20.h),
                                ],
                              ),
                            ))
                      ],
                    ),
                    Positioned(
                      right: 0.w,
                      top: 0.h,
                      child: assImg2(
                          img: 'topic_select_tikutips', w: 82.w, h: 29.h),
                    ),
                  ],
                ),
              ),
              white8rContainer(context,
                  padding: EdgeInsets.only(
                      top: 16.h, left: 20.w, right: 20.w, bottom: 11.h),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      app16spA1Text("请选择要学习的车型",
                              fontFamily: 'PingFangSC-Medium')
                          .paddingOnly(bottom: 16.h),
                      // StaggeredGrid.custom(delegate: delegate)
                      //     MasonryGridView.count(crossAxisCount: 3, itemBuilder: (_,index){
                      //       return Tile(index: index);
                      //     },)
                      StaggeredGrid.count(
                        crossAxisCount: 3,
                        mainAxisSpacing: 10.h,
                        crossAxisSpacing: 12.w,
                        children:
                            // titleList.asMap().entries.map((e) => Tile( index: e.key)).toList(),
                            [
                          StaggeredGridTile.count(
                            crossAxisCellCount: 2,
                            mainAxisCellCount: 1,
                            child: Tile(index: 0),
                          ),
                          StaggeredGridTile.count(
                            crossAxisCellCount: 1,
                            mainAxisCellCount: 1,
                            child: Tile(index: 1),
                          ),
                          StaggeredGridTile.count(
                            crossAxisCellCount: 1,
                            mainAxisCellCount: 1,
                            child: Tile(index: 2),
                          ),
                          StaggeredGridTile.count(
                            crossAxisCellCount: 1,
                            mainAxisCellCount: 1,
                            child: Tile(index: 3),
                          ),
                          StaggeredGridTile.count(
                            crossAxisCellCount: 1,
                            mainAxisCellCount: 1,
                            child: Tile(index: 4),
                          ),
                        ],
                      ),
                    ],
                  )),
              Spacer(),
              Container(
                height: 94.h,
                width: double.infinity,
                color: Colors.white,
                child: Center(
                  child: GestureDetector(
                    onTap: () async {
                      await PreferencesService().setString('useTopicType', _carType);
                      await PreferencesService().setInt('useDivision', saveCityDivision);
                      if (Get.isRegistered<ExaminationController>(tag: ExaminationPageState.key.toString())) {
                        ExaminationController examinationController =
                        Get.find<ExaminationController>(tag: ExaminationPageState.key.toString());
                        examinationController.changeTopicType();
                        examinationController.changeCityType();
                      }
                      if(Get.isRegistered<MineController>()){
                        MineController mineController = Get.find<MineController>();
                        mineController.changeTopicType();
                      }
                      await userRepository.queryUserInfo();
                      Navigator.of(context).pop();
                      // Toast.show("${_selectCity} ${_carType}");
                    },
                    child: Container(
                      height: 45.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                          color: Color(0xFF2E9BF2),
                          borderRadius: BorderRadius.circular(23.r)),
                      child: Center(
                              child: app16spAC5000Text('开始学习',
                                  color: Colors.white,
                                  fontFamily: 'PingFangSC-Semibold')),
                    ).paddingSymmetric(horizontal: 24.w),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  var selectBox = BoxDecoration(
      color: AppColors.F0F7FF,
      border: Border.all(width: 1.h, color: AppColors.other_login_text),
      image: DecorationImage(
          image: assetImg(img: "baoming_icon_xuanzhong"), fit: BoxFit.fill),
      borderRadius: BorderRadius.circular(8.r));

  Widget Tile({required int index}) {
    return GestureDetector(
        onTap: () {
          setState(() {
            newIndex = index;
          });
          _carType = titleListMap[index][titleList[index]]!;
          // Toast.show("点击了${titleList[index]}");
          // Toast.show("选择${_carType}");
        },
        child: Container(
          decoration: newIndex == index
              ? selectBox
              : BoxDecoration(
                  color: AppColors.FCFDFF,
                  border: Border.all(width: 0.1.h),
                  borderRadius: BorderRadius.circular(8.r)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              assImg(img: iconList[index], h: 28.h, w: 48.w)
                  .paddingOnly(bottom: 8.h),
              app14spA1Text(titleList[index],
                      fontFamily: 'PingFangSC-Medium',
                      color:
                          newIndex == index ? AppColors.other_login_text : null)
                  .paddingOnly(bottom: 4.h),
              app12spAAAText(tipList[index],
                  fontFamily: 'PingFangSC-Regular',
                  color: newIndex == index ? AppColors.other_login_text : null)
            ],
          ),
        ));
  }
}
