name: mine
description: "A new Flutter project."
version: 0.0.1
homepage:

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  login:
    path: ../../features/login
  flutter_screenutil: ^5.9.0
  get: ^4.7.2
  flutter_switch: ^0.3.2

  image_picker: ^1.1.1

  # mPass小程序容器
  mop:
    git:
      url: https://github.com/Kayouyou/mop-flutter-sdk.git
  # 权限
  permission_handler: ^10.4.5
  flutter_easyloading:
  webview_flutter:
  component_library:
    path: ../../component_library
  api:
    path: ../../api
  user_repository:
    path: ../../user_repository
  home_repository:
    path: ../../home_repository
  domain_models:
    path: ../../domain_models
  quiz:
    path: ../../quiz
  timing:
    path: ../../timing
  timing_repository:
    path: ../../timing_repository
  oss_repository:
    path: ../../oss_repository
  tools:
    path: ../../tools
  home:
    path: ../../features/home
  dxjd:
    path: ../../..
  key_value_storage:
    path: ../../key_value_storage
  # sd卡
  path_provider: ^2.0.9
  fluwx: ^4.4.3 # WeChat
  qr_flutter: ^4.1.0
  url_launcher: ^6.3.0


  easy_refresh: ^3.4.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  rxdart: ^0.27.1
  cached_network_image:

fluwx:
  app_id: 'wx3628d3e48d1e93a4'
  debug_logging: true # Logging in debug mode.
  android:
  #    interrupt_wx_request: true # Defaults to true.
  #    flutter_activity: 'MainActivity' # Defaults to app's launcher
  ios:
    universal_link: https://dxjk.daxiangjd.com/app/

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
