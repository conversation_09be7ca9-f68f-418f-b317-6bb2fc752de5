import 'package:flutter/material.dart';

import 'pages/home_page.dart';
import 'pages/simple_exercise_page.dart';
import 'pages/simple_mock_exam_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Exercise BLoC Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const HomePage(),
        '/exercise': (context) => const SimpleExercisePage(),
        '/mock_exam': (context) => const SimpleMockExamPage(),
      },
    );
  }
}
