import 'package:flutter/material.dart';
import 'dart:async';

/// 简化版练习页面 - 展示重构后的做题功能
class SimpleExercisePage extends StatefulWidget {
  const SimpleExercisePage({super.key});

  @override
  State<SimpleExercisePage> createState() => _SimpleExercisePageState();
}

class _SimpleExercisePageState extends State<SimpleExercisePage> {
  int _currentQuestionIndex = 0;
  int _correctCount = 0;
  int _wrongCount = 0;
  String _selectedAnswer = '';
  bool _showAnswer = false;
  int _currentMode = 0; // 0:做题, 1:听题, 2:背题, 3:视频

  final List<Map<String, dynamic>> _questions = [
    {
      'id': 1,
      'question': '机动车驾驶人初次申请机动车驾驶证和增加准驾车型后的实习期是多长时间？',
      'options': ['6个月', '12个月', '3个月', '2年'],
      'correct': 'B',
      'explanation': '根据《机动车驾驶证申领和使用规定》，机动车驾驶人初次申请机动车驾驶证和增加准驾车型后的实习期是12个月。',
      'userAnswer': '',
    },
    {
      'id': 2,
      'question':
          '驾驶机动车在高速公路上行驶，遇有雾、雨、雪、沙尘、冰雹等低能见度气象条件时，能见度在50米以下时，以下做法正确的是什么？',
      'options': ['加速驶离高速公路', '在应急车道上停车等待', '可以继续行驶，但需降低车速', '尽快驶离高速公路'],
      'correct': 'D',
      'explanation': '能见度在50米以下时，应当尽快驶离高速公路。根据《道路交通安全法实施条例》相关规定，此时不得继续行驶。',
      'userAnswer': '',
    },
    {
      'id': 3,
      'question': '这个标志是何含义？',
      'options': ['注意行人', '人行横道', '注意儿童', '学校区域'],
      'correct': 'A',
      'explanation': '此标志为注意行人标志，用于提醒驾驶人注意前方可能有行人通过。',
      'userAnswer': '',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // 自定义AppBar
            _buildCustomAppBar(),

            // 模式切换TabBar
            _buildModeTabBar(),

            // 进度信息
            _buildProgressInfo(),

            // 题目内容
            Expanded(
              child: _buildQuestionContent(),
            ),

            // 底部控制按钮
            _buildBottomControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      height: 48.0,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 返回按钮
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back_ios, size: 20),
              color: const Color(0xFF333333),
            ),
          ),

          // 标题
          const Center(
            child: Text(
              '顺序练习演示',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),

          // 功能按钮
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _showFontSizeDialog(),
                  icon: const Icon(Icons.text_fields, size: 20),
                  color: const Color(0xFF666666),
                ),
                IconButton(
                  onPressed: () => _showQuestionListDialog(),
                  icon: const Icon(Icons.list, size: 20),
                  color: const Color(0xFF666666),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeTabBar() {
    const modes = ['做题', '听题', '背题', '视频'];

    return Container(
      margin: const EdgeInsets.all(16),
      child: Container(
        width: 204.0,
        height: 32.0,
        decoration: BoxDecoration(
          color: const Color(0x991992EF),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: modes.asMap().entries.map((entry) {
            final index = entry.key;
            final title = entry.value;
            final isSelected = index == _currentMode;

            return Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _currentMode = index),
                child: Container(
                  height: 32.0,
                  decoration: isSelected
                      ? const BoxDecoration(
                          color: Color(0xFF1992EF),
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        )
                      : null,
                  alignment: Alignment.center,
                  child: Text(
                    title,
                    style: isSelected
                        ? const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          )
                        : const TextStyle(
                            color: Color(0xFF666666),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildProgressInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F7FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildProgressItem(
              '题目', '${_currentQuestionIndex + 1}/${_questions.length}'),
          _buildProgressItem('正确', '$_correctCount'),
          _buildProgressItem('错误', '$_wrongCount'),
          _buildProgressItem('进度',
              '${((_currentQuestionIndex + 1) / _questions.length * 100).toInt()}%'),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1992EF),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionContent() {
    if (_currentMode == 1) {
      return _buildListenMode();
    } else if (_currentMode == 2) {
      return _buildBrowseMode();
    } else if (_currentMode == 3) {
      return _buildVideoMode();
    } else {
      return _buildExerciseMode();
    }
  }

  Widget _buildExerciseMode() {
    final question = _questions[_currentQuestionIndex];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE0E0E0)),
            ),
            child: Text(
              question['question'],
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
                height: 1.5,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 选项
          ...question['options'].asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final optionKey =
                String.fromCharCode(65 + index as int); // A, B, C, D
            final isSelected = _selectedAnswer == optionKey;
            final isCorrect = question['correct'] == optionKey;

            Color backgroundColor = Colors.white;
            Color borderColor = const Color(0xFFE0E0E0);
            Color textColor = const Color(0xFF333333);

            if (_showAnswer) {
              if (isCorrect) {
                backgroundColor = const Color(0xFFE8F5E8);
                borderColor = const Color(0xFF4CAF50);
                textColor = const Color(0xFF2E7D32);
              } else if (isSelected && !isCorrect) {
                backgroundColor = const Color(0xFFFFEBEE);
                borderColor = const Color(0xFFF44336);
                textColor = const Color(0xFFC62828);
              }
            } else if (isSelected) {
              backgroundColor = const Color(0xFFE3F2FD);
              borderColor = const Color(0xFF1992EF);
              textColor = const Color(0xFF1976D2);
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: GestureDetector(
                onTap: _showAnswer ? null : () => _selectAnswer(optionKey),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: borderColor),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected || (_showAnswer && isCorrect)
                              ? borderColor
                              : Colors.transparent,
                          border: Border.all(color: borderColor),
                        ),
                        child: Center(
                          child: Text(
                            optionKey,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isSelected || (_showAnswer && isCorrect)
                                  ? Colors.white
                                  : borderColor,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 14,
                            color: textColor,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),

          // 答案解析
          if (_showAnswer) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F7FA),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE0E0E0)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _selectedAnswer == question['correct']
                            ? Icons.check_circle
                            : Icons.cancel,
                        color: _selectedAnswer == question['correct']
                            ? const Color(0xFF4CAF50)
                            : const Color(0xFFF44336),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _selectedAnswer == question['correct']
                            ? '回答正确'
                            : '回答错误',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _selectedAnswer == question['correct']
                              ? const Color(0xFF4CAF50)
                              : const Color(0xFFF44336),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '正确答案：${question['correct']}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    question['explanation'],
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildListenMode() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.headphones, size: 64, color: Color(0xFF1992EF)),
          SizedBox(height: 16),
          Text(
            '听题模式',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '音频播放功能演示',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBrowseMode() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book, size: 64, color: Color(0xFF1992EF)),
          SizedBox(height: 16),
          Text(
            '背题模式',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '题目浏览功能演示',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoMode() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.play_circle_filled, size: 64, color: Color(0xFF1992EF)),
          SizedBox(height: 16),
          Text(
            '视频模式',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '视频播放功能演示',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F7FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE0E0E0)),
        ),
      ),
      child: Row(
        children: [
          // 上一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF666666),
                side: const BorderSide(color: Color(0xFFE0E0E0)),
              ),
              child: const Text('上一题'),
            ),
          ),

          const SizedBox(width: 16),

          // 下一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: _showAnswer ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1992EF),
                foregroundColor: Colors.white,
              ),
              child: Text(
                  _currentQuestionIndex < _questions.length - 1 ? '下一题' : '完成'),
            ),
          ),
        ],
      ),
    );
  }

  void _selectAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      _showAnswer = true;
      _questions[_currentQuestionIndex]['userAnswer'] = answer;

      if (answer == _questions[_currentQuestionIndex]['correct']) {
        _correctCount++;
      } else {
        _wrongCount++;
      }
    });
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _selectedAnswer = _questions[_currentQuestionIndex]['userAnswer'] ?? '';
        _showAnswer = _selectedAnswer.isNotEmpty;
      });
    }
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _selectedAnswer = _questions[_currentQuestionIndex]['userAnswer'] ?? '';
        _showAnswer = _selectedAnswer.isNotEmpty;
      });
    } else {
      _showCompletionDialog();
    }
  }

  void _showCompletionDialog() {
    final accuracy = _correctCount / _questions.length * 100;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('练习完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, size: 64, color: Color(0xFF4CAF50)),
            const SizedBox(height: 16),
            Text(
              '正确率: ${accuracy.toInt()}%',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text('正确: $_correctCount 题'),
            Text('错误: $_wrongCount 题'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('字体大小'),
        content: const Text('字体大小调节功能'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showQuestionListDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('题目列表'),
        content: const Text('题目导航功能'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
