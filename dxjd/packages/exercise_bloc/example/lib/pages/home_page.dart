import 'package:flutter/material.dart';

/// 主页 - 展示重构功能的入口
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exercise BLoC 重构功能演示'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF5F7FA), Color(0xFFE8EDF5)],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 标题区域
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.auto_awesome,
                        size: 48,
                        color: Color(0xFF1992EF),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Exercise BLoC 重构版本',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '基于BLoC架构的全新做题系统\n支持顺序练习、模拟考试、视频播放等功能',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // 功能按钮区域
                Expanded(
                  child: Column(
                    children: [
                      // 顺序练习按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.quiz,
                        title: '顺序练习',
                        subtitle: '完整的做题流程演示',
                        color: const Color(0xFF1992EF),
                        onTap: () => Navigator.pushNamed(context, '/exercise'),
                      ),

                      const SizedBox(height: 16),

                      // 模拟考试按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.timer,
                        title: '模拟考试',
                        subtitle: '计时考试模式演示',
                        color: const Color(0xFFFF6B35),
                        onTap: () => Navigator.pushNamed(context, '/mock_exam'),
                      ),

                      const SizedBox(height: 16),

                      // 功能对比按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.compare_arrows,
                        title: '功能对比',
                        subtitle: '新旧版本功能对比',
                        color: const Color(0xFF9C27B0),
                        onTap: () => _showComparisonDialog(context),
                      ),

                      const Spacer(),

                      // 版本信息
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.info_outline, 
                                     size: 20, 
                                     color: Colors.grey[600]),
                                const SizedBox(width: 8),
                                Text(
                                  '版本信息',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                _buildInfoItem('版本', 'v1.0.0'),
                                _buildInfoItem('架构', 'BLoC'),
                                _buildInfoItem('状态', '已完成'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1992EF),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _showComparisonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('功能对比'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('✅ 新版本优势:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• BLoC状态管理，更清晰的架构'),
              Text('• 类型安全的状态流'),
              Text('• 更好的性能表现'),
              Text('• 模块化设计，易于维护'),
              Text('• 完整的错误处理机制'),
              SizedBox(height: 16),
              Text('🔄 兼容性:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• UI布局100%兼容原版'),
              Text('• 交互逻辑完全一致'),
              Text('• 数据格式向下兼容'),
              Text('• 支持平滑迁移'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
