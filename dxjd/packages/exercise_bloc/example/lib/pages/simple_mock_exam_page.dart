import 'dart:async';
import 'package:flutter/material.dart';

/// 简化版模拟考试页面 - 展示计时考试功能
class SimpleMockExamPage extends StatefulWidget {
  const SimpleMockExamPage({super.key});

  @override
  State<SimpleMockExamPage> createState() => _SimpleMockExamPageState();
}

class _SimpleMockExamPageState extends State<SimpleMockExamPage> {
  Timer? _timer;
  int _remainingSeconds = 45 * 60; // 45分钟
  bool _isExamStarted = false;
  bool _isExamCompleted = false;
  int _currentQuestionIndex = 0;
  int _correctCount = 0;
  int _wrongCount = 0;

  final List<Map<String, dynamic>> _questions = [
    {
      'id': 1,
      'question': '机动车驾驶人初次申请机动车驾驶证和增加准驾车型后的实习期是多长时间？',
      'options': ['6个月', '12个月', '3个月', '2年'],
      'correct': 'B',
      'userAnswer': '',
    },
    {
      'id': 2,
      'question':
          '驾驶机动车在高速公路上行驶，遇有雾、雨、雪、沙尘、冰雹等低能见度气象条件时，能见度在50米以下时，以下做法正确的是什么？',
      'options': ['加速驶离高速公路', '在应急车道上停车等待', '可以继续行驶，但需降低车速', '尽快驶离高速公路'],
      'correct': 'D',
      'userAnswer': '',
    },
    {
      'id': 3,
      'question': '这个标志是何含义？',
      'options': ['注意行人', '人行横道', '注意儿童', '学校区域'],
      'correct': 'A',
      'userAnswer': '',
    },
    {
      'id': 4,
      'question': '在这种急弯道路上行车应交替使用远近光灯。',
      'options': ['正确', '错误'],
      'correct': 'A',
      'userAnswer': '',
    },
    {
      'id': 5,
      'question': '驾驶机动车在道路上向左变更车道时，应当提前开启左转向灯，在确认安全后变更车道。',
      'options': ['正确', '错误'],
      'correct': 'A',
      'userAnswer': '',
    },
  ];

  @override
  void initState() {
    super.initState();
    _startExam();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startExam() {
    setState(() {
      _isExamStarted = true;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _completeExam();
      }
    });
  }

  void _completeExam() {
    _timer?.cancel();
    setState(() {
      _isExamCompleted = true;
    });
    _showExamResultDialog();
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // 考试头部信息
            _buildExamHeader(),

            // 题目进度条
            _buildProgressBar(),

            // 考试内容
            Expanded(
              child: _buildQuestionContent(),
            ),

            // 考试控制按钮
            _buildExamControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildExamHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1992EF),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => _showExitConfirmDialog(),
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              const Expanded(
                child: Text(
                  '模拟考试',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _showExamInfoDialog(),
                icon: const Icon(Icons.info_outline, color: Colors.white),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 考试信息卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildExamInfoItem(
                  icon: Icons.timer,
                  label: '剩余时间',
                  value: _formatTime(_remainingSeconds),
                  valueColor: _remainingSeconds < 300
                      ? Colors.red
                      : const Color(0xFF1992EF),
                ),
                _buildExamInfoItem(
                  icon: Icons.quiz,
                  label: '考试科目',
                  value: '科目一',
                  valueColor: const Color(0xFF1992EF),
                ),
                _buildExamInfoItem(
                  icon: Icons.assignment,
                  label: '题目总数',
                  value: '${_questions.length}题',
                  valueColor: const Color(0xFF1992EF),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color valueColor,
  }) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF666666), size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: valueColor,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    final progress = (_currentQuestionIndex + 1) / _questions.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '第 ${_currentQuestionIndex + 1} 题',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${_currentQuestionIndex + 1}/${_questions.length}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: const Color(0xFFE0E0E0),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF1992EF)),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionContent() {
    final question = _questions[_currentQuestionIndex];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE0E0E0)),
            ),
            child: Text(
              question['question'],
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
                height: 1.5,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 选项
          ...question['options'].asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final optionKey =
                String.fromCharCode(65 + index as int); // A, B, C, D
            final isSelected = question['userAnswer'] == optionKey;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: GestureDetector(
                onTap: () => _selectAnswer(optionKey),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFFE3F2FD) : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF1992EF)
                          : const Color(0xFFE0E0E0),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected
                              ? const Color(0xFF1992EF)
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF1992EF)
                                : const Color(0xFFE0E0E0),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            optionKey,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isSelected
                                  ? Colors.white
                                  : const Color(0xFF666666),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelected
                                ? const Color(0xFF1976D2)
                                : const Color(0xFF333333),
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildExamControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F7FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE0E0E0)),
        ),
      ),
      child: Row(
        children: [
          // 上一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: _currentQuestionIndex > 0 ? _previousQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF666666),
                side: const BorderSide(color: Color(0xFFE0E0E0)),
              ),
              child: const Text('上一题'),
            ),
          ),

          const SizedBox(width: 16),

          // 下一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: _currentQuestionIndex < _questions.length - 1
                  ? _nextQuestion
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1992EF),
                foregroundColor: Colors.white,
              ),
              child: const Text('下一题'),
            ),
          ),

          const SizedBox(width: 16),

          // 交卷按钮
          ElevatedButton(
            onPressed: () => _showSubmitConfirmDialog(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6B35),
              foregroundColor: Colors.white,
            ),
            child: const Text('交卷'),
          ),
        ],
      ),
    );
  }

  void _selectAnswer(String answer) {
    setState(() {
      _questions[_currentQuestionIndex]['userAnswer'] = answer;
    });
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    }
  }

  void _showExitConfirmDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('考试正在进行中，确定要退出吗？退出后考试记录将不会保存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('继续考试'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('确认退出'),
          ),
        ],
      ),
    );
  }

  void _showSubmitConfirmDialog() {
    final answeredCount =
        _questions.where((q) => q['userAnswer'].isNotEmpty).length;
    final unansweredCount = _questions.length - answeredCount;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认交卷'),
        content: Text(
          unansweredCount > 0
              ? '还有 $unansweredCount 道题未作答，确定要交卷吗？'
              : '所有题目已完成，确定要交卷吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('继续答题'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _completeExam();
            },
            child: const Text('确认交卷'),
          ),
        ],
      ),
    );
  }

  void _showExamInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('考试说明'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('考试规则:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 考试时间：45分钟'),
              Text('• 题目数量：5题（演示版）'),
              Text('• 及格分数：90分'),
              Text('• 每题20分'),
              SizedBox(height: 16),
              Text('注意事项:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 请在规定时间内完成所有题目'),
              Text('• 可以随时查看已答题目'),
              Text('• 交卷后无法修改答案'),
              Text('• 时间到自动交卷'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  void _showExamResultDialog() {
    // 计算考试结果
    _correctCount = 0;
    _wrongCount = 0;

    for (final question in _questions) {
      if (question['userAnswer'].isNotEmpty) {
        if (question['userAnswer'] == question['correct']) {
          _correctCount++;
        } else {
          _wrongCount++;
        }
      }
    }

    final score = (_correctCount / _questions.length * 100).toInt();
    final isPassed = score >= 90;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          isPassed ? '考试通过' : '考试未通过',
          style: TextStyle(
            color: isPassed ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isPassed ? Icons.check_circle : Icons.cancel,
              size: 64,
              color: isPassed ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '考试分数: $score 分',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text('正确: $_correctCount 题'),
            Text('错误: $_wrongCount 题'),
            const SizedBox(height: 8),
            Text(
              isPassed ? '恭喜通过考试！' : '继续努力，下次一定能通过！',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }
}
