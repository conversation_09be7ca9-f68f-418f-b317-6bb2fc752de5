# Exercise BLoC

基于 BLoC 架构的现代化练习模块，用于驾考应用程序。

## 🎯 概述

本包是对原始练习模块的完全重写，采用了现代化的 Flutter 架构模式：

- **BLoC Pattern**: 业务逻辑与 UI 的清晰分离
- **Repository Pattern**: 抽象化数据访问层
- **Dependency Injection**: 可测试且易维护的代码
- **Clean Architecture**: 关注点清晰分离

## 🏗️ 架构

```
lib/
├── src/
│   ├── presentation/          # UI 层
│   │   ├── pages/            # 页面组件
│   │   ├── widgets/          # 可复用 UI 组件
│   │   └── bloc/             # BLoC 类
│   ├── domain/               # 业务逻辑层
│   │   ├── entities/         # 业务实体
│   │   ├── repositories/     # 仓库接口
│   │   └── usecases/         # 业务用例
│   └── data/                 # 数据层
│       ├── models/           # 数据模型
│       ├── repositories/     # 仓库实现
│       └── datasources/      # 数据源 (API, DB, 等)
└── exercise_bloc.dart        # 公共 API
```

## 🚀 功能特性

### 练习模式
- **做题模式**: 交互式答题，提供即时反馈
- **听题模式**: 基于音频的学习，支持播放控制
- **背题模式**: 题目浏览和复习
- **视频模式**: 基于视频的学习内容

### 核心功能
- ✅ 支持单选题、多选题和判断题
- ✅ 音频播放，支持后台运行
- ✅ 进度跟踪和统计
- ✅ 错题收集和复习
- ✅ 书签功能
- ✅ 离线支持
- ✅ 实时同步

## 🎨 UI 一致性

本包与原始实现保持 **100% UI 一致性**：
- 相同的视觉设计和样式
- 相同的用户交互
- 保留动画和过渡效果
- 兼容现有主题

## 📱 使用方法

```dart
// 初始化练习模块
await ExerciseModule.initialize();

// 导航到练习页面
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ExercisePage(
      subject: 1,
      type: 'chapter',
      sortId: 1,
    ),
  ),
);
```

## 🧪 测试

本包包含全面的测试：
- BLoC 和仓库的单元测试
- UI 组件的小部件测试
- 完整流程的集成测试

运行测试：
```bash
flutter test
```

## 🔧 开发

### 代码生成
```bash
flutter packages pub run build_runner build
```

### 依赖项
本包依赖以下本地包：
- `component_library`: 共享 UI 组件
- `api`: 网络层
- `tools`: 工具函数
- `domain_models`: 共享数据模型

## 📄 许可证

本包是驾考应用程序的一部分，遵循相同的许可条款。
