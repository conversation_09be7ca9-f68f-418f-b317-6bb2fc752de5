name: exercise_bloc
description: Modern exercise module with BLoC architecture for driving exam app
version: 0.0.1
publish_to: 'none'

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # BLoC state management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Dependency injection
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # Audio player
  audioplayers: ^1.1.1

  # Video player
  video_player: ^2.8.1
  
  # Network and caching
  cached_network_image: ^3.3.0
  dio: ^5.3.2
  
  # Database
  sqflite: ^2.3.0
  
  # Utilities
  json_annotation: ^4.8.1
  
  # Local packages
  component_library:
    path: ../component_library
  api:
    path: ../api
  tools:
    path: ../tools
  domain_models:
    path: ../domain_models
  quiz:
    path: ../quiz

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  
  # Code generation
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  injectable_generator: ^2.4.1
  
  # Testing
  bloc_test: ^9.1.4
  mocktail: ^1.0.0

dependency_overrides:
  # 解决 timing 包冲突
  timing: ^1.0.0
  # 解决 uuid 版本冲突
  uuid: 3.0.6
  
flutter:
  # No assets needed for now
