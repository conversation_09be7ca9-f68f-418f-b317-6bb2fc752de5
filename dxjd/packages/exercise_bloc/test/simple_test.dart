import 'package:flutter_test/flutter_test.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';

void main() {
  group('Simple Tests', () {
    test('QuestionEntity creation', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
        ],
        correctAnswer: 'a',
      );

      expect(question.id, equals(1));
      expect(question.title, equals('Test Question'));
      expect(question.type, equals(QuestionType.single));
      expect(question.options.length, equals(2));
      expect(question.correctAnswer, equals('a'));
    });

    test('QuestionOption creation', () {
      const option = QuestionOption(key: 'a', text: 'Option A');

      expect(option.key, equals('a'));
      expect(option.text, equals('Option A'));
    });

    test('QuestionType enum', () {
      expect(QuestionType.single.toString(), contains('single'));
      expect(QuestionType.multiple.toString(), contains('multiple'));
      expect(QuestionType.judge.toString(), contains('judge'));
    });

    test('QuestionEntity equality', () {
      const question1 = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
      );

      const question2 = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
      );

      expect(question1, equals(question2));
      expect(question1.hashCode, equals(question2.hashCode));
    });

    test('QuestionEntity copyWith', () {
      const original = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Original Title',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
      );

      final modified = original.copyWith(
        title: 'Modified Title',
        isAnswered: true,
      );

      expect(modified.id, equals(1));
      expect(modified.title, equals('Modified Title'));
      expect(modified.isAnswered, equals(true));
      expect(original.title, equals('Original Title'));
      expect(original.isAnswered, equals(false));
    });

    test('QuestionEntity with audio URL', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Audio Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        audioUrl: 'audio/question1.mp3',
      );

      expect(question.audioUrl, equals('audio/question1.mp3'));
    });

    test('QuestionEntity with image path', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Image Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        imagePath: 'images/question1.jpg',
      );

      expect(question.imagePath, equals('images/question1.jpg'));
    });

    test('QuestionEntity with video path', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Video Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        videoPath: 'videos/question1.mp4',
      );

      expect(question.videoPath, equals('videos/question1.mp4'));
    });

    test('QuestionEntity with explanation and skills', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Question with explanation',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        explanation: 'This is the explanation',
        skills: 'Required skills',
      );

      expect(question.explanation, equals('This is the explanation'));
      expect(question.skills, equals('Required skills'));
    });

    test('QuestionEntity multiple choice', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Multiple Choice Question',
        type: QuestionType.multiple,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
          QuestionOption(key: 'c', text: 'Option C'),
        ],
        correctAnswer: 'ab',
      );

      expect(question.type, equals(QuestionType.multiple));
      expect(question.correctAnswer, equals('ab'));
      expect(question.options.length, equals(3));
    });

    test('QuestionEntity judge type', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Judge Question',
        type: QuestionType.judge,
        options: [
          QuestionOption(key: 'a', text: '正确'),
          QuestionOption(key: 'b', text: '错误'),
        ],
        correctAnswer: 'a',
      );

      expect(question.type, equals(QuestionType.judge));
      expect(question.options.length, equals(2));
    });

    test('QuestionEntity with user answer', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Answered Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        userAnswer: 'a',
        isAnswered: true,
      );

      expect(question.userAnswer, equals('a'));
      expect(question.isAnswered, equals(true));
    });

    test('QuestionEntity with play count and playing state', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Audio Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
        audioUrl: 'audio/question1.mp3',
        playCount: 3,
        isPlaying: true,
      );

      expect(question.playCount, equals(3));
      expect(question.isPlaying, equals(true));
    });

    test('QuestionEntity toString', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
        ],
        correctAnswer: 'a',
      );

      final stringRepresentation = question.toString();
      expect(stringRepresentation, contains('QuestionEntity'));
      expect(stringRepresentation, contains('1')); // ID is first parameter
      expect(stringRepresentation, contains('Test Question'));
    });
  });
}
