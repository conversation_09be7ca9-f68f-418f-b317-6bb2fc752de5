import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/presentation/bloc/listen/listen_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/listen/listen_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/listen/listen_state.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';
import 'package:exercise_bloc/src/domain/usecases/load_questions.dart';
import 'package:exercise_bloc/src/domain/repositories/audio_repository.dart';

// Mock classes
class MockLoadQuestions extends Mock implements LoadQuestions {}
class MockAudioRepository extends Mock implements AudioRepository {}

void main() {
  group('ListenBloc', () {
    late ListenBloc listenBloc;
    late MockLoadQuestions mockLoadQuestions;
    late MockAudioRepository mockAudioRepository;

    setUp(() {
      mockLoadQuestions = MockLoadQuestions();
      mockAudioRepository = MockAudioRepository();

      listenBloc = ListenBloc(
        loadQuestions: mockLoadQuestions,
        audioRepository: mockAudioRepository,
      );
    });

    tearDown(() {
      listenBloc.close();
    });

    test('initial state is ListenInitial', () {
      expect(listenBloc.state, equals(const ListenInitial()));
    });

    group('LoadQuestions', () {
      final mockQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Test Question 1',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
          audioPath: 'audio1.mp3',
        ),
        const QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Test Question 2',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'b',
          audioPath: 'audio2.mp3',
        ),
      ];

      blocTest<ListenBloc, ListenState>(
        'emits [ListenLoading, ListenLoaded] when LoadQuestions is added successfully',
        build: () {
          when(() => mockLoadQuestions(any()))
              .thenAnswer((_) async => mockQuestions);
          return listenBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )),
        expect: () => [
          const ListenLoading(),
          isA<ListenLoaded>()
              .having((state) => state.questions.length, 'questions length', 2)
              .having((state) => state.currentIndex, 'current index', 0),
        ],
        verify: (_) {
          verify(() => mockLoadQuestions(any())).called(1);
        },
      );

      blocTest<ListenBloc, ListenState>(
        'emits [ListenLoading, ListenError] when LoadQuestions fails',
        build: () {
          when(() => mockLoadQuestions(any()))
              .thenThrow(Exception('Network error'));
          return listenBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )),
        expect: () => [
          const ListenLoading(),
          isA<ListenError>().having((state) => state.message, 'error message',
              contains('Failed to load questions')),
        ],
      );
    });

    group('Audio Control', () {
      final mockQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Test Question',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
          audioPath: 'audio1.mp3',
        ),
      ];

      const initialState = ListenLoaded(
        questions: mockQuestions,
        currentIndex: 0,
      );

      blocTest<ListenBloc, ListenState>(
        'plays audio when PlayQuestionAudio is added',
        build: () {
          when(() => mockAudioRepository.play(any()))
              .thenAnswer((_) async {});
          return listenBloc;
        },
        seed: () => initialState,
        act: (bloc) => bloc.add(const PlayQuestionAudio(questionIndex: 0)),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.questions[0].isPlaying, 'is playing', true)
              .having((state) => state.questions[0].playCount, 'play count', 1),
        ],
        verify: (_) {
          verify(() => mockAudioRepository.play('audio1.mp3')).called(1);
        },
      );

      blocTest<ListenBloc, ListenState>(
        'pauses audio when PauseAudio is added',
        build: () {
          when(() => mockAudioRepository.pause())
              .thenAnswer((_) async {});
          return listenBloc;
        },
        seed: () => ListenLoaded(
          questions: [mockQuestions[0].copyWith(isPlaying: true)],
          currentIndex: 0,
        ),
        act: (bloc) => bloc.add(const PauseAudio()),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.questions[0].isPlaying, 'is playing', false),
        ],
        verify: (_) {
          verify(() => mockAudioRepository.pause()).called(1);
        },
      );

      blocTest<ListenBloc, ListenState>(
        'stops audio when StopAudio is added',
        build: () {
          when(() => mockAudioRepository.stop())
              .thenAnswer((_) async {});
          return listenBloc;
        },
        seed: () => ListenLoaded(
          questions: [mockQuestions[0].copyWith(isPlaying: true)],
          currentIndex: 0,
        ),
        act: (bloc) => bloc.add(const StopAudio()),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.questions[0].isPlaying, 'is playing', false),
        ],
        verify: (_) {
          verify(() => mockAudioRepository.stop()).called(1);
        },
      );
    });

    group('Navigation', () {
      const mockQuestions = [
        QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Question 1',
          type: QuestionType.single,
          options: [QuestionOption(key: 'a', text: 'Option A')],
          correctAnswer: 'a',
          audioPath: 'audio1.mp3',
        ),
        QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Question 2',
          type: QuestionType.single,
          options: [QuestionOption(key: 'a', text: 'Option A')],
          correctAnswer: 'a',
          audioPath: 'audio2.mp3',
        ),
      ];

      blocTest<ListenBloc, ListenState>(
        'navigates to next question',
        build: () => listenBloc,
        seed: () => const ListenLoaded(
          questions: mockQuestions,
          currentIndex: 0,
        ),
        act: (bloc) => bloc.add(const NextQuestion()),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.currentIndex, 'current index', 1),
        ],
      );

      blocTest<ListenBloc, ListenState>(
        'navigates to previous question',
        build: () => listenBloc,
        seed: () => const ListenLoaded(
          questions: mockQuestions,
          currentIndex: 1,
        ),
        act: (bloc) => bloc.add(const PreviousQuestion()),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.currentIndex, 'current index', 0),
        ],
      );

      blocTest<ListenBloc, ListenState>(
        'jumps to specific question',
        build: () => listenBloc,
        seed: () => const ListenLoaded(
          questions: mockQuestions,
          currentIndex: 0,
        ),
        act: (bloc) => bloc.add(const JumpToQuestion(1)),
        expect: () => [
          isA<ListenLoaded>()
              .having((state) => state.currentIndex, 'current index', 1),
        ],
      );
    });
  });
}
