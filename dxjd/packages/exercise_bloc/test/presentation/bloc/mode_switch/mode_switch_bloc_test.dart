import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_state.dart';

void main() {
  group('ModeSwitchBloc', () {
    late ModeSwitchBloc modeSwitchBloc;

    setUp(() {
      modeSwitchBloc = ModeSwitchBloc();
    });

    tearDown(() {
      modeSwitchBloc.close();
    });

    test('initial state is ModeSwitchInitial with exercise mode', () {
      expect(
        modeSwitchBloc.state,
        equals(const ModeSwitchInitial(currentMode: ExerciseMode.exercise)),
      );
    });

    group('Mode Switching', () {
      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'switches to listen mode',
        build: () => modeSwitchBloc,
        act: (bloc) => bloc.add(const SwitchToListen()),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.listen),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'switches to browse mode',
        build: () => modeSwitchBloc,
        act: (bloc) => bloc.add(const SwitchToBrowse()),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.browse,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.browse),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'switches to video mode',
        build: () => modeSwitchBloc,
        act: (bloc) => bloc.add(const SwitchToVideo()),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.video,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.video),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'switches back to exercise mode',
        build: () => modeSwitchBloc,
        seed: () => const ModeSwitchSuccess(currentMode: ExerciseMode.listen),
        act: (bloc) => bloc.add(const SwitchToExercise()),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.listen,
            targetMode: ExerciseMode.exercise,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.exercise),
        ],
      );
    });

    group('Mode Switch Confirmation', () {
      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'requires confirmation when switching with unsaved progress',
        build: () => modeSwitchBloc,
        act: (bloc) => bloc.add(const SwitchToListen(requiresConfirmation: true)),
        expect: () => [
          const ModeSwitchConfirmationRequired(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
            reason: 'You have unsaved progress. Are you sure you want to switch modes?',
          ),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'confirms mode switch after user confirmation',
        build: () => modeSwitchBloc,
        seed: () => const ModeSwitchConfirmationRequired(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
          reason: 'You have unsaved progress. Are you sure you want to switch modes?',
        ),
        act: (bloc) => bloc.add(const ConfirmModeSwitch(targetMode: ExerciseMode.listen)),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.listen),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'cancels mode switch when user cancels',
        build: () => modeSwitchBloc,
        seed: () => const ModeSwitchConfirmationRequired(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
          reason: 'You have unsaved progress. Are you sure you want to switch modes?',
        ),
        act: (bloc) => bloc.add(const CancelModeSwitch()),
        expect: () => [
          const ModeSwitchSuccess(currentMode: ExerciseMode.exercise),
        ],
      );
    });

    group('Error Handling', () {
      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'handles mode switch failure',
        build: () => modeSwitchBloc,
        act: (bloc) => bloc.add(const SwitchToListen(shouldFail: true)),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
          ),
          const ModeSwitchFailure(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
            error: 'Failed to switch to listen mode',
          ),
        ],
      );

      blocTest<ModeSwitchBloc, ModeSwitchState>(
        'retries mode switch after failure',
        build: () => modeSwitchBloc,
        seed: () => const ModeSwitchFailure(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
          error: 'Failed to switch to listen mode',
        ),
        act: (bloc) => bloc.add(const SwitchToListen()),
        expect: () => [
          const ModeSwitchInProgress(
            currentMode: ExerciseMode.exercise,
            targetMode: ExerciseMode.listen,
          ),
          const ModeSwitchSuccess(currentMode: ExerciseMode.listen),
        ],
      );
    });

    group('State Properties', () {
      test('ModeSwitchSuccess has correct properties', () {
        const state = ModeSwitchSuccess(currentMode: ExerciseMode.listen);
        
        expect(state.currentMode, equals(ExerciseMode.listen));
        expect(state.canSwitchModes, isTrue);
        expect(state.isSwitching, isFalse);
        expect(state.isConfirmationRequired, isFalse);
      });

      test('ModeSwitchInProgress has correct properties', () {
        const state = ModeSwitchInProgress(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
        );
        
        expect(state.currentMode, equals(ExerciseMode.exercise));
        expect(state.targetMode, equals(ExerciseMode.listen));
        expect(state.canSwitchModes, isFalse);
        expect(state.isSwitching, isTrue);
        expect(state.isConfirmationRequired, isFalse);
      });

      test('ModeSwitchConfirmationRequired has correct properties', () {
        const state = ModeSwitchConfirmationRequired(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
          reason: 'Test reason',
        );
        
        expect(state.currentMode, equals(ExerciseMode.exercise));
        expect(state.targetMode, equals(ExerciseMode.listen));
        expect(state.reason, equals('Test reason'));
        expect(state.canSwitchModes, isFalse);
        expect(state.isSwitching, isFalse);
        expect(state.isConfirmationRequired, isTrue);
      });

      test('ModeSwitchFailure has correct properties', () {
        const state = ModeSwitchFailure(
          currentMode: ExerciseMode.exercise,
          targetMode: ExerciseMode.listen,
          error: 'Test error',
        );
        
        expect(state.currentMode, equals(ExerciseMode.exercise));
        expect(state.targetMode, equals(ExerciseMode.listen));
        expect(state.error, equals('Test error'));
        expect(state.canSwitchModes, isTrue);
        expect(state.isSwitching, isFalse);
        expect(state.isConfirmationRequired, isFalse);
      });
    });

    group('ExerciseMode Extension', () {
      test('fromIndex returns correct mode', () {
        expect(ExerciseModeExtension.fromIndex(0), equals(ExerciseMode.exercise));
        expect(ExerciseModeExtension.fromIndex(1), equals(ExerciseMode.listen));
        expect(ExerciseModeExtension.fromIndex(2), equals(ExerciseMode.browse));
        expect(ExerciseModeExtension.fromIndex(3), equals(ExerciseMode.video));
      });

      test('fromIndex throws for invalid index', () {
        expect(() => ExerciseModeExtension.fromIndex(-1), throwsArgumentError);
        expect(() => ExerciseModeExtension.fromIndex(4), throwsArgumentError);
      });

      test('mode index returns correct value', () {
        expect(ExerciseMode.exercise.index, equals(0));
        expect(ExerciseMode.listen.index, equals(1));
        expect(ExerciseMode.browse.index, equals(2));
        expect(ExerciseMode.video.index, equals(3));
      });
    });
  });
}
