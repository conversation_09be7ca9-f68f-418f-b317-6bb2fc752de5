import 'package:flutter_test/flutter_test.dart';
import 'package:exercise_bloc/src/data/repositories/audio_repository_impl.dart';
import 'package:exercise_bloc/src/data/datasources/audio_datasource.dart';
import 'package:exercise_bloc/src/domain/repositories/audio_repository.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Data Layer Tests', () {
    test('AudioRepository can be instantiated', () {
      // Arrange
      final audioDataSource = AudioDataSourceImpl();

      // Act
      final repository = AudioRepositoryImpl(audioDataSource: audioDataSource);

      // Assert
      expect(repository, isA<AudioRepository>());
    });

    test('AudioDataSource implements correct interface', () {
      // Arrange & Act
      final dataSource = AudioDataSourceImpl();

      // Assert
      expect(dataSource, isA<AudioDataSource>());
    });

    test('AudioPlayerState enum has correct values', () {
      // Test enum values
      expect(AudioPlayerState.stopped.toString(), contains('stopped'));
      expect(AudioPlayerState.playing.toString(), contains('playing'));
      expect(AudioPlayerState.paused.toString(), contains('paused'));
      expect(AudioPlayerState.completed.toString(), contains('completed'));
      expect(AudioPlayerState.error.toString(), contains('error'));
    });

    test('AudioInterruption enum has correct values', () {
      // Test enum values
      expect(AudioInterruption.began.toString(), contains('began'));
      expect(AudioInterruption.ended.toString(), contains('ended'));
    });

    test('AudioRepository methods are callable', () async {
      // Arrange
      final audioDataSource = AudioDataSourceImpl();
      final repository = AudioRepositoryImpl(audioDataSource: audioDataSource);

      // Act & Assert - Test that methods exist and are callable
      expect(() => repository.isPlaying, returnsNormally);
      expect(() => repository.isPaused, returnsNormally);
      expect(() => repository.isStopped, returnsNormally);
      expect(() => repository.getCurrentPosition(), returnsNormally);
      expect(() => repository.getDuration(), returnsNormally);

      // Test streams
      expect(repository.positionStream, isA<Stream<Duration>>());
      expect(repository.durationStream, isA<Stream<Duration>>());
      expect(repository.playerStateStream, isA<Stream<AudioPlayerState>>());
      expect(repository.onPlayerComplete, isA<Stream<void>>());
      expect(repository.onAudioInterruption, isA<Stream<AudioInterruption>>());
    });

    test('AudioDataSource has correct default values', () async {
      // Arrange
      final dataSource = AudioDataSourceImpl();

      // Act & Assert
      expect(await dataSource.isPlaying, isFalse);
      expect(await dataSource.isPaused, isFalse);
      expect(await dataSource.currentPosition, equals(Duration.zero));
      expect(await dataSource.totalDuration, equals(Duration.zero));
      expect(await dataSource.currentVolume, equals(1.0));
      expect(await dataSource.currentPlaybackSpeed, equals(1.0));
    });

    test('AudioRepository error handling works', () async {
      // Arrange
      final audioDataSource = AudioDataSourceImpl();
      final repository = AudioRepositoryImpl(audioDataSource: audioDataSource);

      // Act & Assert - Test error handling for invalid values
      expect(
        () => repository.setVolume(-0.1),
        throwsA(isA<Exception>()),
      );

      expect(
        () => repository.setVolume(1.1),
        throwsA(isA<Exception>()),
      );

      expect(
        () => repository.setPlaybackRate(0.0),
        throwsA(isA<Exception>()),
      );

      expect(
        () => repository.setPlaybackRate(-1.0),
        throwsA(isA<Exception>()),
      );
    });

    test('AudioRepository graceful error handling for getters', () async {
      // Arrange
      final audioDataSource = AudioDataSourceImpl();
      final repository = AudioRepositoryImpl(audioDataSource: audioDataSource);

      // Act & Assert - Test that getters return safe defaults on error
      final isPlaying = repository.isPlaying;
      final isPaused = repository.isPaused;
      final isStopped = repository.isStopped;
      final position = await repository.getCurrentPosition();
      final duration = await repository.getDuration();

      expect(isPlaying, isA<bool>());
      expect(isPaused, isA<bool>());
      expect(isStopped, isA<bool>());
      expect(position, isA<Duration>());
      expect(duration, isA<Duration>());
    });

    test('AudioDataSource can be disposed', () {
      // Arrange
      final dataSource = AudioDataSourceImpl();

      // Act & Assert - Should not throw during creation
      expect(dataSource, isA<AudioDataSource>());
      // Note: dispose() test skipped due to platform dependency
    });

    test('AudioRepository can be disposed', () {
      // Arrange
      final audioDataSource = AudioDataSourceImpl();
      final repository = AudioRepositoryImpl(audioDataSource: audioDataSource);

      // Act & Assert - Should not throw during creation
      expect(repository, isA<AudioRepository>());
      // Note: dispose() test skipped due to platform dependency
    });
  });
}
