import 'package:flutter_test/flutter_test.dart';

/// Test configuration and utilities for exercise_bloc package
class TestConfig {
  TestConfig._();

  /// Default timeout for async tests
  static const Duration defaultTimeout = Duration(seconds: 10);

  /// Timeout for integration tests
  static const Duration integrationTimeout = Duration(seconds: 30);

  /// Setup common test configuration
  static void setupTests() {
    // Set default timeout for all tests
    testWidgets.timeout = Timeout(defaultTimeout);
    test.timeout = Timeout(defaultTimeout);
  }

  /// Setup integration test configuration
  static void setupIntegrationTests() {
    testWidgets.timeout = Timeout(integrationTimeout);
    test.timeout = Timeout(integrationTimeout);
  }
}

/// Test utilities and helpers
class TestUtils {
  TestUtils._();

  /// Wait for a specific duration (useful for testing async operations)
  static Future<void> wait([Duration duration = const Duration(milliseconds: 100)]) {
    return Future.delayed(duration);
  }

  /// Pump and settle for widget tests
  static Future<void> pumpAndSettle(WidgetTester tester, [Duration duration = const Duration(milliseconds: 100)]) async {
    await tester.pump(duration);
    await tester.pumpAndSettle();
  }

  /// Create a test description with category prefix
  static String testDescription(String category, String description) {
    return '[$category] $description';
  }
}

/// Mock data factory for tests
class MockDataFactory {
  MockDataFactory._();

  /// Create mock question entities for testing
  static List<Map<String, dynamic>> createMockQuestionData({
    int count = 3,
    String type = 'single',
    int subject = 1,
    int sortId = 1,
  }) {
    return List.generate(count, (index) {
      final id = index + 1;
      return {
        'id': id,
        'sortId': sortId,
        'title': 'Test Question $id',
        'type': type,
        'options': _createMockOptions(type),
        'correctAnswer': _getCorrectAnswer(type),
        'explanation': 'Test explanation for question $id',
        'skills': 'Test skills for question $id',
        'subject': subject,
      };
    });
  }

  static List<Map<String, String>> _createMockOptions(String type) {
    switch (type) {
      case 'single':
        return [
          {'key': 'a', 'text': 'Option A'},
          {'key': 'b', 'text': 'Option B'},
          {'key': 'c', 'text': 'Option C'},
          {'key': 'd', 'text': 'Option D'},
        ];
      case 'multiple':
        return [
          {'key': 'a', 'text': 'Option A'},
          {'key': 'b', 'text': 'Option B'},
          {'key': 'c', 'text': 'Option C'},
          {'key': 'd', 'text': 'Option D'},
        ];
      case 'judge':
        return [
          {'key': 'a', 'text': '正确'},
          {'key': 'b', 'text': '错误'},
        ];
      default:
        return [
          {'key': 'a', 'text': 'Option A'},
          {'key': 'b', 'text': 'Option B'},
        ];
    }
  }

  static String _getCorrectAnswer(String type) {
    switch (type) {
      case 'single':
        return 'a';
      case 'multiple':
        return 'ab';
      case 'judge':
        return 'a';
      default:
        return 'a';
    }
  }

  /// Create mock progress data
  static Map<String, dynamic> createMockProgressData({
    int subject = 1,
    String type = 'chapter',
    int currentIndex = 0,
    int totalQuestions = 10,
    int correctCount = 0,
    int wrongCount = 0,
  }) {
    return {
      'subject': subject,
      'type': type,
      'currentIndex': currentIndex,
      'totalQuestions': totalQuestions,
      'correctCount': correctCount,
      'wrongCount': wrongCount,
      'answeredQuestions': <int, String>{},
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock audio data
  static Map<String, dynamic> createMockAudioData({
    String path = 'test_audio.mp3',
    Duration duration = const Duration(minutes: 2),
    bool isPlaying = false,
    Duration position = Duration.zero,
  }) {
    return {
      'path': path,
      'duration': duration.inMilliseconds,
      'isPlaying': isPlaying,
      'position': position.inMilliseconds,
    };
  }
}

/// Test matchers for custom assertions
class TestMatchers {
  TestMatchers._();

  /// Matcher for checking if a list contains specific items
  static Matcher containsItems<T>(List<T> items) {
    return predicate<List<T>>((list) {
      return items.every((item) => list.contains(item));
    }, 'contains all items: $items');
  }

  /// Matcher for checking if a duration is approximately equal
  static Matcher approximatelyEquals(Duration expected, {Duration tolerance = const Duration(milliseconds: 100)}) {
    return predicate<Duration>((actual) {
      final difference = (actual - expected).abs();
      return difference <= tolerance;
    }, 'approximately equals $expected (±$tolerance)');
  }

  /// Matcher for checking if a string contains keywords
  static Matcher containsKeywords(List<String> keywords) {
    return predicate<String>((text) {
      final lowerText = text.toLowerCase();
      return keywords.every((keyword) => lowerText.contains(keyword.toLowerCase()));
    }, 'contains keywords: $keywords');
  }
}

/// Test groups for organizing tests
class TestGroups {
  TestGroups._();

  static const String unit = 'Unit Tests';
  static const String integration = 'Integration Tests';
  static const String widget = 'Widget Tests';
  static const String bloc = 'BLoC Tests';
  static const String repository = 'Repository Tests';
  static const String usecase = 'UseCase Tests';
  static const String entity = 'Entity Tests';
  static const String model = 'Model Tests';
}

/// Test tags for filtering tests
class TestTags {
  TestTags._();

  static const String fast = 'fast';
  static const String slow = 'slow';
  static const String integration = 'integration';
  static const String unit = 'unit';
  static const String widget = 'widget';
  static const String bloc = 'bloc';
  static const String repository = 'repository';
  static const String usecase = 'usecase';
  static const String audio = 'audio';
  static const String network = 'network';
}

/// Test environment setup
class TestEnvironment {
  TestEnvironment._();

  /// Setup test environment
  static void setup() {
    TestConfig.setupTests();
    
    // Add any global test setup here
    setUpAll(() {
      // Global setup for all tests
    });

    tearDownAll(() {
      // Global cleanup for all tests
    });
  }

  /// Setup integration test environment
  static void setupIntegration() {
    TestConfig.setupIntegrationTests();
    
    setUpAll(() {
      // Integration test specific setup
    });

    tearDownAll(() {
      // Integration test specific cleanup
    });
  }
}
