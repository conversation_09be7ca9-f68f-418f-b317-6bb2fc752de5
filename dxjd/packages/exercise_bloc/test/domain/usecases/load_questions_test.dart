import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/domain/usecases/load_questions.dart';
import 'package:exercise_bloc/src/domain/repositories/question_repository.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';

// Mock classes
class MockQuestionRepository extends Mo<PERSON> implements QuestionRepository {}

void main() {
  group('LoadQuestions', () {
    late LoadQuestions useCase;
    late MockQuestionRepository mockRepository;

    setUp(() {
      mockRepository = MockQuestionRepository();
      useCase = LoadQuestions(mockRepository);
    });

    group('call', () {
      final mockQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Test Question 1',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
        ),
        const QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Test Question 2',
          type: QuestionType.multiple,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
            QuestionOption(key: 'c', text: 'Option C'),
          ],
          correctAnswer: 'ab',
        ),
      ];

      test('returns questions when repository call succeeds', () async {
        // Arrange
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        when(() => mockRepository.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
              mainId: any(named: 'mainId'),
              childId: any(named: 'childId'),
              luid: any(named: 'luid'),
              ids: any(named: 'ids'),
            )).thenAnswer((_) async => mockQuestions);

        // Act
        final result = await useCase(params);

        // Assert
        expect(result, equals(mockQuestions));
        verify(() => mockRepository.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
              mainId: null,
              childId: null,
              luid: null,
              ids: null,
            )).called(1);
      });

      test('returns questions with all parameters', () async {
        // Arrange
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
          mainId: 10,
          childId: 20,
          luid: 30,
          ids: [1, 2, 3],
        );

        when(() => mockRepository.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
              mainId: any(named: 'mainId'),
              childId: any(named: 'childId'),
              luid: any(named: 'luid'),
              ids: any(named: 'ids'),
            )).thenAnswer((_) async => mockQuestions);

        // Act
        final result = await useCase(params);

        // Assert
        expect(result, equals(mockQuestions));
        verify(() => mockRepository.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
              mainId: 10,
              childId: 20,
              luid: 30,
              ids: [1, 2, 3],
            )).called(1);
      });

      test('throws exception when repository call fails', () async {
        // Arrange
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        when(() => mockRepository.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
              mainId: any(named: 'mainId'),
              childId: any(named: 'childId'),
              luid: any(named: 'luid'),
              ids: any(named: 'ids'),
            )).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase(params),
          throwsA(isA<Exception>()),
        );
      });

      test('returns empty list when no questions found', () async {
        // Arrange
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        when(() => mockRepository.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
              mainId: any(named: 'mainId'),
              childId: any(named: 'childId'),
              luid: any(named: 'luid'),
              ids: any(named: 'ids'),
            )).thenAnswer((_) async => []);

        // Act
        final result = await useCase(params);

        // Assert
        expect(result, isEmpty);
        verify(() => mockRepository.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
              mainId: null,
              childId: null,
              luid: null,
              ids: null,
            )).called(1);
      });
    });

    group('LoadQuestionsParams', () {
      test('creates params with required fields', () {
        // Act
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(params.subject, equals(1));
        expect(params.type, equals('chapter'));
        expect(params.sortId, equals(1));
        expect(params.mainId, isNull);
        expect(params.childId, isNull);
        expect(params.luid, isNull);
        expect(params.ids, isNull);
      });

      test('creates params with all fields', () {
        // Act
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
          mainId: 10,
          childId: 20,
          luid: 30,
          ids: [1, 2, 3],
        );

        // Assert
        expect(params.subject, equals(1));
        expect(params.type, equals('chapter'));
        expect(params.sortId, equals(1));
        expect(params.mainId, equals(10));
        expect(params.childId, equals(20));
        expect(params.luid, equals(30));
        expect(params.ids, equals([1, 2, 3]));
      });

      test('supports equality comparison', () {
        // Arrange
        const params1 = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        const params2 = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        const params3 = LoadQuestionsParams(
          subject: 2,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(params1, equals(params2));
        expect(params1, isNot(equals(params3)));
      });

      test('supports hashCode', () {
        // Arrange
        const params1 = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        const params2 = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(params1.hashCode, equals(params2.hashCode));
      });

      test('supports toString', () {
        // Arrange
        const params = LoadQuestionsParams(
          subject: 1,
          type: 'chapter',
          sortId: 1,
          mainId: 10,
        );

        // Act
        final result = params.toString();

        // Assert
        expect(result, contains('LoadQuestionsParams'));
        expect(result, contains('subject: 1'));
        expect(result, contains('type: chapter'));
        expect(result, contains('sortId: 1'));
        expect(result, contains('mainId: 10'));
      });
    });
  });
}
