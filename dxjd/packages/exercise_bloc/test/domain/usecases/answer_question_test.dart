import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/domain/usecases/answer_question.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';
import 'package:exercise_bloc/src/domain/repositories/question_repository.dart';

class MockQuestionRepository extends Mo<PERSON> implements QuestionRepository {}

void main() {
  group('AnswerQuestion', () {
    late AnswerQuestion usecase;
    late MockQuestionRepository mockRepository;

    setUp(() {
      mockRepository = MockQuestionRepository();
      usecase = AnswerQuestion(mockRepository);
    });

    group('Single Choice Questions', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test single choice question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
          QuestionOption(key: 'c', text: 'Option C'),
        ],
        correctAnswer: 'a',
        explanation: 'A is the correct answer',
        skills: 'Test skills',
      );

      test('should return correct result when answer is correct', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'a',
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, true);
        expect(result.correctAnswer, 'a');
        expect(result.explanation, 'A is the correct answer');
        expect(result.skills, 'Test skills');

        verify(() => mockRepository.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        )).called(1);

        verify(() => mockRepository.updateQuestionStats(
          questionId: 1,
          answerCount: 1,
          correctCount: 1,
          errorCount: 0,
        )).called(1);
      });

      test('should return incorrect result when answer is wrong', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'b',
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, false);
        expect(result.correctAnswer, 'a');

        verify(() => mockRepository.saveAnswer(
          questionId: 1,
          answer: 'b',
          isCorrect: false,
          subject: 1,
        )).called(1);

        verify(() => mockRepository.updateQuestionStats(
          questionId: 1,
          answerCount: 1,
          correctCount: 0,
          errorCount: 1,
        )).called(1);
      });

      test('should handle case insensitive answers', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'A', // Uppercase
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, true);
      });
    });

    group('Multiple Choice Questions', () {
      const question = QuestionEntity(
        id: 2,
        sortId: 1,
        title: 'Test multiple choice question',
        type: QuestionType.multiple,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
          QuestionOption(key: 'c', text: 'Option C'),
          QuestionOption(key: 'd', text: 'Option D'),
        ],
        correctAnswer: 'ab',
      );

      test('should return correct result when all correct options are selected', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'ab',
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, true);
        expect(result.correctAnswer, 'ab');
      });

      test('should handle different order of multiple choice answers', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'ba', // Different order
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, true);
      });

      test('should return incorrect result when missing options', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'a', // Missing 'b'
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, false);
      });
    });

    group('Judge Questions', () {
      const question = QuestionEntity(
        id: 3,
        sortId: 1,
        title: 'Test judge question',
        type: QuestionType.judge,
        options: [
          QuestionOption(key: 'true', text: '正确'),
          QuestionOption(key: 'false', text: '错误'),
        ],
        correctAnswer: 'true',
      );

      test('should return correct result for true answer', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'true',
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, true);
        expect(result.correctAnswer, 'true');
      });

      test('should return incorrect result for false answer', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenAnswer((_) async {});

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'false',
          subject: 1,
        );

        // Act
        final result = await usecase(params);

        // Assert
        expect(result.isCorrect, false);
      });
    });

    group('Error Handling', () {
      const question = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test question',
        type: QuestionType.single,
        options: [QuestionOption(key: 'a', text: 'Option A')],
        correctAnswer: 'a',
      );

      test('should throw exception when saveAnswer fails', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenThrow(Exception('Database error'));

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'a',
          subject: 1,
        );

        // Act & Assert
        expect(() => usecase(params), throwsException);
      });

      test('should throw exception when updateQuestionStats fails', () async {
        // Arrange
        when(() => mockRepository.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRepository.updateQuestionStats(
          questionId: any(named: 'questionId'),
          answerCount: any(named: 'answerCount'),
          correctCount: any(named: 'correctCount'),
          errorCount: any(named: 'errorCount'),
        )).thenThrow(Exception('Update error'));

        const params = AnswerQuestionParams(
          question: question,
          userAnswer: 'a',
          subject: 1,
        );

        // Act & Assert
        expect(() => usecase(params), throwsException);
      });
    });
  });
}
