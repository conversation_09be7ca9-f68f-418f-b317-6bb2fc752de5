import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/data/repositories/question_repository_impl.dart';
import 'package:exercise_bloc/src/data/datasources/question_local_datasource.dart';
import 'package:exercise_bloc/src/data/datasources/question_remote_datasource.dart';
import 'package:exercise_bloc/src/data/models/question_model.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';

// Mock classes
class MockQuestionLocalDataSource extends Mock
    implements QuestionLocalDataSource {}

class MockQuestionRemoteDataSource extends Mock
    implements QuestionRemoteDataSource {}

void main() {
  group('QuestionRepositoryImpl', () {
    late QuestionRepositoryImpl repository;
    late MockQuestionLocalDataSource mockLocalDataSource;
    late MockQuestionRemoteDataSource mockRemoteDataSource;

    setUp(() {
      mockLocalDataSource = MockQuestionLocalDataSource();
      mockRemoteDataSource = MockQuestionRemoteDataSource();
      repository = QuestionRepositoryImpl(
        localDataSource: mockLocalDataSource,
        remoteDataSource: mockRemoteDataSource,
      );
    });

    group('getQuestions', () {
      final mockQuestionModels = [
        const QuestionModel(
          id: 1,
          sortId: 1,
          title: 'Test Question 1',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
        ),
        const QuestionModel(
          id: 2,
          sortId: 1,
          title: 'Test Question 2',
          type: QuestionType.multiple,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
            QuestionOption(key: 'c', text: 'Option C'),
          ],
          correctAnswer: 'ab',
        ),
      ];

      test('returns questions from local when available', () async {
        // Arrange
        when(() => mockLocalDataSource.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
            )).thenAnswer((_) async => mockQuestionModels);

        // Act
        final result = await repository.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result[0].id, equals(1));
        expect(result[0].title, equals('Test Question 1'));
        expect(result[1].id, equals(2));
        expect(result[1].title, equals('Test Question 2'));

        verify(() => mockLocalDataSource.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
            )).called(1);
      });

      test('fetches from remote when local is empty', () async {
        // Arrange
        when(() => mockLocalDataSource.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
            )).thenAnswer((_) async => []);

        when(() => mockRemoteDataSource.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
            )).thenAnswer((_) async => mockQuestionModels);

        when(() => mockLocalDataSource.saveQuestions(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(result.length, equals(2));

        verify(() => mockLocalDataSource.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
            )).called(1);
        verify(() => mockRemoteDataSource.getQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
            )).called(1);
        verify(() => mockLocalDataSource.saveQuestions(mockQuestionModels))
            .called(1);
      });

      test('falls back to local when remote fails', () async {
        // Arrange
        when(() => mockLocalDataSource.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
            )).thenAnswer((_) async => []);

        when(() => mockRemoteDataSource.getQuestions(
              subject: any(named: 'subject'),
              type: any(named: 'type'),
              sortId: any(named: 'sortId'),
            )).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => repository.getQuestions(
            subject: 1,
            type: 'chapter',
            sortId: 1,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('saveUserAnswer', () {
      test('saves user answer successfully', () async {
        // Arrange
        when(() => mockLocalDataSource.saveUserAnswer(
              questionId: any(named: 'questionId'),
              answer: any(named: 'answer'),
              isCorrect: any(named: 'isCorrect'),
            )).thenAnswer((_) async {});

        // Act
        await repository.saveUserAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
        );

        // Assert
        verify(() => mockLocalDataSource.saveUserAnswer(
              questionId: 1,
              answer: 'a',
              isCorrect: true,
            )).called(1);
      });

      test('throws exception when save fails', () async {
        // Arrange
        when(() => mockLocalDataSource.saveUserAnswer(
              questionId: any(named: 'questionId'),
              answer: any(named: 'answer'),
              isCorrect: any(named: 'isCorrect'),
            )).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.saveUserAnswer(
            questionId: 1,
            answer: 'a',
            isCorrect: true,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('getQuestionById', () {
      const mockQuestionModel = QuestionModel(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
        ],
        correctAnswer: 'a',
      );

      test('returns question when found', () async {
        // Arrange
        when(() => mockLocalDataSource.getQuestionById(any()))
            .thenAnswer((_) async => mockQuestionModel);

        // Act
        final result = await repository.getQuestionById(1);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(1));
        expect(result.title, equals('Test Question'));

        verify(() => mockLocalDataSource.getQuestionById(1)).called(1);
      });

      test('returns null when question not found', () async {
        // Arrange
        when(() => mockLocalDataSource.getQuestionById(any()))
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getQuestionById(1);

        // Assert
        expect(result, isNull);

        verify(() => mockLocalDataSource.getQuestionById(1)).called(1);
      });
    });

    group('getErrorQuestions', () {
      test('returns error questions successfully', () async {
        // Arrange
        final mockErrorQuestions = [
          const QuestionModel(
            id: 1,
            sortId: 1,
            title: 'Error Question 1',
            type: QuestionType.single,
            options: [
              QuestionOption(key: 'a', text: 'Option A'),
              QuestionOption(key: 'b', text: 'Option B'),
            ],
            correctAnswer: 'a',
          ),
        ];

        when(() => mockLocalDataSource.getErrorQuestions(
              subject: any(named: 'subject'),
            )).thenAnswer((_) async => mockErrorQuestions);

        // Act
        final result = await repository.getErrorQuestions(subject: 1);

        // Assert
        expect(result.length, equals(1));
        expect(result[0].title, equals('Error Question 1'));

        verify(() => mockLocalDataSource.getErrorQuestions(subject: 1))
            .called(1);
      });
    });

    group('getFavoriteQuestions', () {
      test('returns favorite questions successfully', () async {
        // Arrange
        final mockFavoriteQuestions = [
          const QuestionModel(
            id: 1,
            sortId: 1,
            title: 'Favorite Question 1',
            type: QuestionType.single,
            options: [
              QuestionOption(key: 'a', text: 'Option A'),
              QuestionOption(key: 'b', text: 'Option B'),
            ],
            correctAnswer: 'a',
          ),
        ];

        when(() => mockLocalDataSource.getFavoriteQuestions(
              subject: any(named: 'subject'),
            )).thenAnswer((_) async => mockFavoriteQuestions);

        // Act
        final result = await repository.getFavoriteQuestions(subject: 1);

        // Assert
        expect(result.length, equals(1));
        expect(result[0].title, equals('Favorite Question 1'));

        verify(() => mockLocalDataSource.getFavoriteQuestions(subject: 1))
            .called(1);
      });
    });

    group('toggleFavorite', () {
      test('toggles favorite status successfully', () async {
        // Arrange
        when(() => mockLocalDataSource.toggleFavorite(any()))
            .thenAnswer((_) async {});

        // Act
        await repository.toggleFavorite(1);

        // Assert
        verify(() => mockLocalDataSource.toggleFavorite(1)).called(1);
      });
    });

    group('syncWithServer', () {
      test('syncs data with server successfully', () async {
        // Arrange
        when(() => mockRemoteDataSource.syncData()).thenAnswer((_) async {});

        // Act
        await repository.syncWithServer();

        // Assert
        verify(() => mockRemoteDataSource.syncData()).called(1);
      });

      test('handles sync failure gracefully', () async {
        // Arrange
        when(() => mockRemoteDataSource.syncData())
            .thenThrow(Exception('Sync failed'));

        // Act & Assert
        expect(
          () => repository.syncWithServer(),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
