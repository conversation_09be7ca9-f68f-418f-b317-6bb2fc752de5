import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/data/repositories/question_repository_impl.dart';
import 'package:exercise_bloc/src/data/datasources/question_remote_datasource.dart';
import 'package:exercise_bloc/src/data/datasources/question_local_datasource.dart';
import 'package:exercise_bloc/src/data/models/question_model.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';

class MockQuestionRemoteDataSource extends Mock implements QuestionRemoteDataSource {}
class MockQuestionLocalDataSource extends Mock implements QuestionLocalDataSource {}

void main() {
  group('QuestionRepositoryImpl', () {
    late QuestionRepositoryImpl repository;
    late MockQuestionRemoteDataSource mockRemoteDataSource;
    late MockQuestionLocalDataSource mockLocalDataSource;

    setUp(() {
      mockRemoteDataSource = MockQuestionRemoteDataSource();
      mockLocalDataSource = MockQuestionLocalDataSource();
      repository = QuestionRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockLocalDataSource,
      );
    });

    group('getQuestions', () {
      final mockQuestionModels = [
        const QuestionModel(
          id: 1,
          sortId: 1,
          title: 'Test Question 1',
          type: 'single',
          options: [
            QuestionOptionModel(key: 'a', text: 'Option A'),
            QuestionOptionModel(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
        ),
        const QuestionModel(
          id: 2,
          sortId: 1,
          title: 'Test Question 2',
          type: 'multiple',
          options: [
            QuestionOptionModel(key: 'a', text: 'Option A'),
            QuestionOptionModel(key: 'b', text: 'Option B'),
            QuestionOptionModel(key: 'c', text: 'Option C'),
          ],
          correctAnswer: 'ab',
        ),
      ];

      test('should return questions from remote and cache them locally', () async {
        // Arrange
        when(() => mockRemoteDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
          sortId: any(named: 'sortId'),
        )).thenAnswer((_) async => mockQuestionModels);

        when(() => mockLocalDataSource.saveQuestions(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(result, isA<List<QuestionEntity>>());
        expect(result.length, 2);
        expect(result[0].id, 1);
        expect(result[0].title, 'Test Question 1');
        expect(result[0].type, QuestionType.single);
        expect(result[1].id, 2);
        expect(result[1].type, QuestionType.multiple);

        verify(() => mockRemoteDataSource.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )).called(1);

        verify(() => mockLocalDataSource.saveQuestions(mockQuestionModels))
            .called(1);
      });

      test('should fallback to local data when remote fails', () async {
        // Arrange
        when(() => mockRemoteDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
          sortId: any(named: 'sortId'),
        )).thenThrow(Exception('Network error'));

        when(() => mockLocalDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
          sortId: any(named: 'sortId'),
        )).thenAnswer((_) async => mockQuestionModels);

        // Act
        final result = await repository.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        );

        // Assert
        expect(result, isA<List<QuestionEntity>>());
        expect(result.length, 2);

        verify(() => mockRemoteDataSource.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )).called(1);

        verify(() => mockLocalDataSource.getQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )).called(1);

        verifyNever(() => mockLocalDataSource.saveQuestions(any()));
      });

      test('should throw exception when both remote and local fail', () async {
        // Arrange
        when(() => mockRemoteDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
          sortId: any(named: 'sortId'),
        )).thenThrow(Exception('Network error'));

        when(() => mockLocalDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
          sortId: any(named: 'sortId'),
        )).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getQuestions(
            subject: 1,
            type: 'chapter',
            sortId: 1,
          ),
          throwsException,
        );
      });
    });

    group('saveAnswer', () {
      test('should save answer locally and sync with remote', () async {
        // Arrange
        when(() => mockLocalDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRemoteDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        // Act
        await repository.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        );

        // Assert
        verify(() => mockLocalDataSource.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        )).called(1);

        verify(() => mockRemoteDataSource.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        )).called(1);
      });

      test('should save locally even when remote sync fails', () async {
        // Arrange
        when(() => mockLocalDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenAnswer((_) async {});

        when(() => mockRemoteDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenThrow(Exception('Network error'));

        // Act
        await repository.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        );

        // Assert
        verify(() => mockLocalDataSource.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        )).called(1);

        verify(() => mockRemoteDataSource.saveAnswer(
          questionId: 1,
          answer: 'a',
          isCorrect: true,
          subject: 1,
        )).called(1);
      });

      test('should throw exception when local save fails', () async {
        // Arrange
        when(() => mockLocalDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        )).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.saveAnswer(
            questionId: 1,
            answer: 'a',
            isCorrect: true,
            subject: 1,
          ),
          throwsException,
        );

        verifyNever(() => mockRemoteDataSource.saveAnswer(
          questionId: any(named: 'questionId'),
          answer: any(named: 'answer'),
          isCorrect: any(named: 'isCorrect'),
          subject: any(named: 'subject'),
        ));
      });
    });

    group('getErrorQuestions', () {
      final mockErrorQuestions = [
        const QuestionModel(
          id: 1,
          sortId: 1,
          title: 'Error Question 1',
          type: 'single',
          options: [QuestionOptionModel(key: 'a', text: 'Option A')],
          correctAnswer: 'a',
          isAnswered: true,
          isCorrect: false,
        ),
      ];

      test('should return error questions from remote first', () async {
        // Arrange
        when(() => mockRemoteDataSource.getErrorQuestions(
          subject: any(named: 'subject'),
          userId: any(named: 'userId'),
        )).thenAnswer((_) async => mockErrorQuestions);

        // Act
        final result = await repository.getErrorQuestions(
          subject: 1,
          userId: 'user123',
        );

        // Assert
        expect(result, isA<List<QuestionEntity>>());
        expect(result.length, 1);
        expect(result[0].isAnswered, true);
        expect(result[0].isCorrect, false);

        verify(() => mockRemoteDataSource.getErrorQuestions(
          subject: 1,
          userId: 'user123',
        )).called(1);
      });

      test('should fallback to local when remote fails', () async {
        // Arrange
        when(() => mockRemoteDataSource.getErrorQuestions(
          subject: any(named: 'subject'),
          userId: any(named: 'userId'),
        )).thenThrow(Exception('Network error'));

        when(() => mockLocalDataSource.getErrorQuestions(
          subject: any(named: 'subject'),
          userId: any(named: 'userId'),
        )).thenAnswer((_) async => mockErrorQuestions);

        // Act
        final result = await repository.getErrorQuestions(
          subject: 1,
          userId: 'user123',
        );

        // Assert
        expect(result, isA<List<QuestionEntity>>());
        expect(result.length, 1);

        verify(() => mockLocalDataSource.getErrorQuestions(
          subject: 1,
          userId: 'user123',
        )).called(1);
      });
    });

    group('searchQuestions', () {
      final mockAllQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Traffic light question',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Red means stop'),
            QuestionOption(key: 'b', text: 'Green means go'),
          ],
          correctAnswer: 'a',
        ),
        const QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Speed limit question',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Drive slowly'),
            QuestionOption(key: 'b', text: 'Drive fast'),
          ],
          correctAnswer: 'a',
        ),
      ];

      test('should filter questions by keyword in title', () async {
        // Arrange
        when(() => mockRemoteDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
        )).thenAnswer((_) async => mockAllQuestions.map((e) => QuestionModel.fromEntity(e)).toList());

        when(() => mockLocalDataSource.saveQuestions(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.searchQuestions(
          keyword: 'traffic',
          subject: 1,
        );

        // Assert
        expect(result.length, 1);
        expect(result[0].title, contains('Traffic'));
      });

      test('should filter questions by keyword in options', () async {
        // Arrange
        when(() => mockRemoteDataSource.getQuestions(
          subject: any(named: 'subject'),
          type: any(named: 'type'),
        )).thenAnswer((_) async => mockAllQuestions.map((e) => QuestionModel.fromEntity(e)).toList());

        when(() => mockLocalDataSource.saveQuestions(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.searchQuestions(
          keyword: 'stop',
          subject: 1,
        );

        // Assert
        expect(result.length, 1);
        expect(result[0].options.any((option) => option.text.contains('stop')), true);
      });
    });
  });
}
