import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/data/repositories/audio_repository_impl.dart';
import 'package:exercise_bloc/src/data/datasources/audio_datasource.dart';
import 'package:exercise_bloc/src/domain/repositories/audio_repository.dart';

// Mock classes
class MockAudioDataSource extends Mock implements AudioDataSource {}

void main() {
  group('AudioRepositoryImpl', () {
    late AudioRepositoryImpl repository;
    late MockAudioDataSource mockDataSource;

    setUp(() {
      mockDataSource = MockAudioDataSource();
      repository = AudioRepositoryImpl(audioDataSource: mockDataSource);
    });

    group('play', () {
      test('plays audio successfully', () async {
        // Arrange
        when(() => mockDataSource.play(any())).thenAnswer((_) async {});

        // Act
        await repository.play('test_audio.mp3');

        // Assert
        verify(() => mockDataSource.play('test_audio.mp3')).called(1);
      });

      test('throws exception when play fails', () async {
        // Arrange
        when(() => mockDataSource.play(any()))
            .thenThrow(Exception('Audio play failed'));

        // Act & Assert
        expect(
          () => repository.play('test_audio.mp3'),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('pause', () {
      test('pauses audio successfully', () async {
        // Arrange
        when(() => mockDataSource.pause()).thenAnswer((_) async {});

        // Act
        await repository.pause();

        // Assert
        verify(() => mockDataSource.pause()).called(1);
      });

      test('throws exception when pause fails', () async {
        // Arrange
        when(() => mockDataSource.pause())
            .thenThrow(Exception('Audio pause failed'));

        // Act & Assert
        expect(
          () => repository.pause(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('resume', () {
      test('resumes audio successfully', () async {
        // Arrange
        when(() => mockDataSource.resume()).thenAnswer((_) async {});

        // Act
        await repository.resume();

        // Assert
        verify(() => mockDataSource.resume()).called(1);
      });

      test('throws exception when resume fails', () async {
        // Arrange
        when(() => mockDataSource.resume())
            .thenThrow(Exception('Audio resume failed'));

        // Act & Assert
        expect(
          () => repository.resume(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('stop', () {
      test('stops audio successfully', () async {
        // Arrange
        when(() => mockDataSource.stop()).thenAnswer((_) async {});

        // Act
        await repository.stop();

        // Assert
        verify(() => mockDataSource.stop()).called(1);
      });

      test('throws exception when stop fails', () async {
        // Arrange
        when(() => mockDataSource.stop())
            .thenThrow(Exception('Audio stop failed'));

        // Act & Assert
        expect(
          () => repository.stop(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('seek', () {
      test('seeks to position successfully', () async {
        // Arrange
        const position = Duration(seconds: 30);
        when(() => mockDataSource.seek(any())).thenAnswer((_) async {});

        // Act
        await repository.seek(position);

        // Assert
        verify(() => mockDataSource.seek(position)).called(1);
      });

      test('throws exception when seek fails', () async {
        // Arrange
        const position = Duration(seconds: 30);
        when(() => mockDataSource.seek(any()))
            .thenThrow(Exception('Audio seek failed'));

        // Act & Assert
        expect(
          () => repository.seek(position),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('setVolume', () {
      test('sets volume successfully', () async {
        // Arrange
        const volume = 0.5;
        when(() => mockDataSource.setVolume(any())).thenAnswer((_) async {});

        // Act
        await repository.setVolume(volume);

        // Assert
        verify(() => mockDataSource.setVolume(volume)).called(1);
      });

      test('throws exception for invalid volume', () async {
        // Act & Assert
        expect(
          () => repository.setVolume(-0.1),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => repository.setVolume(1.1),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('throws exception when setVolume fails', () async {
        // Arrange
        const volume = 0.5;
        when(() => mockDataSource.setVolume(any()))
            .thenThrow(Exception('Set volume failed'));

        // Act & Assert
        expect(
          () => repository.setVolume(volume),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('setPlaybackSpeed', () {
      test('sets playback speed successfully', () async {
        // Arrange
        const speed = 1.5;
        when(() => mockDataSource.setPlaybackSpeed(any()))
            .thenAnswer((_) async {});

        // Act
        await repository.setPlaybackSpeed(speed);

        // Assert
        verify(() => mockDataSource.setPlaybackSpeed(speed)).called(1);
      });

      test('throws exception for invalid speed', () async {
        // Act & Assert
        expect(
          () => repository.setPlaybackSpeed(0.0),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => repository.setPlaybackSpeed(-1.0),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('throws exception when setPlaybackSpeed fails', () async {
        // Arrange
        const speed = 1.5;
        when(() => mockDataSource.setPlaybackSpeed(any()))
            .thenThrow(Exception('Set playback speed failed'));

        // Act & Assert
        expect(
          () => repository.setPlaybackSpeed(speed),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('configureAudioSession', () {
      test('configures audio session successfully', () async {
        // Arrange
        when(() => mockDataSource.configureAudioSession(
              enableBackgroundPlayback: any(named: 'enableBackgroundPlayback'),
              mixWithOthers: any(named: 'mixWithOthers'),
            )).thenAnswer((_) async {});

        // Act
        await repository.configureAudioSession(
          enableBackgroundPlayback: true,
          mixWithOthers: false,
        );

        // Assert
        verify(() => mockDataSource.configureAudioSession(
              enableBackgroundPlayback: true,
              mixWithOthers: false,
            )).called(1);
      });

      test('throws exception when configuration fails', () async {
        // Arrange
        when(() => mockDataSource.configureAudioSession(
              enableBackgroundPlayback: any(named: 'enableBackgroundPlayback'),
              mixWithOthers: any(named: 'mixWithOthers'),
            )).thenThrow(Exception('Configuration failed'));

        // Act & Assert
        expect(
          () => repository.configureAudioSession(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('state getters', () {
      test('returns playing state', () async {
        // Arrange
        when(() => mockDataSource.isPlaying).thenAnswer((_) async => true);

        // Act
        final result = await repository.isPlaying;

        // Assert
        expect(result, isTrue);
        verify(() => mockDataSource.isPlaying).called(1);
      });

      test('returns false when isPlaying fails', () async {
        // Arrange
        when(() => mockDataSource.isPlaying)
            .thenThrow(Exception('State error'));

        // Act
        final result = await repository.isPlaying;

        // Assert
        expect(result, isFalse);
      });

      test('returns current position', () async {
        // Arrange
        const position = Duration(seconds: 30);
        when(() => mockDataSource.currentPosition)
            .thenAnswer((_) async => position);

        // Act
        final result = await repository.currentPosition;

        // Assert
        expect(result, equals(position));
        verify(() => mockDataSource.currentPosition).called(1);
      });

      test('returns zero duration when currentPosition fails', () async {
        // Arrange
        when(() => mockDataSource.currentPosition)
            .thenThrow(Exception('Position error'));

        // Act
        final result = await repository.currentPosition;

        // Assert
        expect(result, equals(Duration.zero));
      });
    });

    group('streams', () {
      test('exposes position stream', () {
        // Arrange
        final positionStream = Stream<Duration>.value(const Duration(seconds: 30));
        when(() => mockDataSource.positionStream).thenAnswer((_) => positionStream);

        // Act
        final result = repository.positionStream;

        // Assert
        expect(result, equals(positionStream));
      });

      test('exposes player state stream', () {
        // Arrange
        final stateStream = Stream<AudioPlayerState>.value(AudioPlayerState.playing);
        when(() => mockDataSource.playerStateStream).thenAnswer((_) => stateStream);

        // Act
        final result = repository.playerStateStream;

        // Assert
        expect(result, equals(stateStream));
      });
    });

    group('dispose', () {
      test('disposes successfully', () async {
        // Arrange
        when(() => mockDataSource.dispose()).thenAnswer((_) async {});

        // Act
        await repository.dispose();

        // Assert
        verify(() => mockDataSource.dispose()).called(1);
      });

      test('throws exception when dispose fails', () async {
        // Arrange
        when(() => mockDataSource.dispose())
            .thenThrow(Exception('Dispose failed'));

        // Act & Assert
        expect(
          () => repository.dispose(),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
