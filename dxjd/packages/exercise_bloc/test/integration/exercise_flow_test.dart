import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_state.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_state.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';
import 'package:exercise_bloc/src/domain/entities/exercise_progress.dart';
import 'package:exercise_bloc/src/domain/usecases/load_questions.dart';
import 'package:exercise_bloc/src/domain/usecases/answer_question.dart';
import 'package:exercise_bloc/src/domain/usecases/save_progress.dart';
import 'package:exercise_bloc/src/domain/repositories/question_repository.dart';

// Mock classes
class MockLoadQuestions extends Mock implements LoadQuestions {}
class MockAnswerQuestion extends Mock implements AnswerQuestion {}
class MockSaveProgress extends Mock implements SaveProgress {}
class MockQuestionRepository extends Mock implements QuestionRepository {}

void main() {
  group('Exercise Flow Integration Tests', () {
    late ExerciseBloc exerciseBloc;
    late ModeSwitchBloc modeSwitchBloc;
    late MockLoadQuestions mockLoadQuestions;
    late MockAnswerQuestion mockAnswerQuestion;
    late MockSaveProgress mockSaveProgress;
    late MockQuestionRepository mockQuestionRepository;

    setUp(() {
      mockLoadQuestions = MockLoadQuestions();
      mockAnswerQuestion = MockAnswerQuestion();
      mockSaveProgress = MockSaveProgress();
      mockQuestionRepository = MockQuestionRepository();

      exerciseBloc = ExerciseBloc(
        loadQuestions: mockLoadQuestions,
        answerQuestion: mockAnswerQuestion,
        saveProgress: mockSaveProgress,
        questionRepository: mockQuestionRepository,
      );

      modeSwitchBloc = ModeSwitchBloc();
    });

    tearDown(() {
      exerciseBloc.close();
      modeSwitchBloc.close();
    });

    group('Complete Exercise Flow', () {
      final mockQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Question 1',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
          explanation: 'Explanation 1',
        ),
        const QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Question 2',
          type: QuestionType.multiple,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
            QuestionOption(key: 'c', text: 'Option C'),
          ],
          correctAnswer: 'ab',
          explanation: 'Explanation 2',
        ),
      ];

      test('should complete full exercise flow successfully', () async {
        // Setup mocks
        when(() => mockLoadQuestions(any()))
            .thenAnswer((_) async => mockQuestions);

        when(() => mockAnswerQuestion(any()))
            .thenAnswer((_) async => const AnswerResult(
                  isCorrect: true,
                  correctAnswer: 'a',
                  explanation: 'Explanation 1',
                ));

        when(() => mockSaveProgress(any())).thenAnswer((_) async {});

        // Step 1: Load questions
        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseLoaded>()
                .having((state) => state.questions.length, 'questions length', 2)
                .having((state) => state.currentIndex, 'current index', 0),
          ]),
        );

        // Step 2: Answer first question
        exerciseBloc.add(const AnswerQuestion(
          questionIndex: 0,
          answer: 'a',
        ));

        await expectLater(
          exerciseBloc.stream,
          emits(isA<QuestionAnswered>()
              .having((state) => state.isCorrect, 'is correct', true)
              .having((state) => state.progress.correctCount, 'correct count', 1)),
        );

        // Step 3: Navigate to next question
        exerciseBloc.add(const NextQuestion());

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()
              .having((state) => state.currentIndex, 'current index', 1)),
        );

        // Verify all interactions
        verify(() => mockLoadQuestions(any())).called(1);
        verify(() => mockAnswerQuestion(any())).called(1);
        verify(() => mockSaveProgress(any())).called(1);
      });

      test('should handle mode switching during exercise', () async {
        // Setup exercise bloc
        when(() => mockLoadQuestions(any()))
            .thenAnswer((_) async => mockQuestions);

        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseLoaded>(),
          ]),
        );

        // Switch to listen mode
        modeSwitchBloc.add(const SwitchToListen());

        await expectLater(
          modeSwitchBloc.stream,
          emitsInOrder([
            const ModeSwitchInProgress(
              currentMode: ExerciseMode.exercise,
              targetMode: ExerciseMode.listen,
            ),
            const ModeSwitchSuccess(currentMode: ExerciseMode.listen),
          ]),
        );

        // Switch back to exercise mode
        modeSwitchBloc.add(const SwitchToExercise());

        await expectLater(
          modeSwitchBloc.stream,
          emitsInOrder([
            const ModeSwitchInProgress(
              currentMode: ExerciseMode.listen,
              targetMode: ExerciseMode.exercise,
            ),
            const ModeSwitchSuccess(currentMode: ExerciseMode.exercise),
          ]),
        );
      });

      test('should handle error recovery flow', () async {
        // Setup initial failure
        when(() => mockLoadQuestions(any()))
            .thenThrow(Exception('Network error'));

        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseError>(),
          ]),
        );

        // Setup successful retry
        when(() => mockLoadQuestions(any()))
            .thenAnswer((_) async => mockQuestions);

        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseLoaded>(),
          ]),
        );

        verify(() => mockLoadQuestions(any())).called(2);
      });

      test('should handle progress saving throughout exercise', () async {
        // Setup mocks
        when(() => mockLoadQuestions(any()))
            .thenAnswer((_) async => mockQuestions);

        when(() => mockAnswerQuestion(any()))
            .thenAnswer((_) async => const AnswerResult(
                  isCorrect: true,
                  correctAnswer: 'a',
                  explanation: 'Explanation 1',
                ));

        when(() => mockSaveProgress(any())).thenAnswer((_) async {});

        // Load questions
        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseLoaded>(),
          ]),
        );

        // Answer multiple questions
        exerciseBloc.add(const AnswerQuestion(
          questionIndex: 0,
          answer: 'a',
        ));

        await expectLater(
          exerciseBloc.stream,
          emits(isA<QuestionAnswered>()),
        );

        exerciseBloc.add(const NextQuestion());

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()),
        );

        exerciseBloc.add(const AnswerQuestion(
          questionIndex: 1,
          answer: 'ab',
        ));

        await expectLater(
          exerciseBloc.stream,
          emits(isA<QuestionAnswered>()),
        );

        // Verify progress was saved multiple times
        verify(() => mockSaveProgress(any())).called(2);
      });

      test('should handle navigation edge cases', () async {
        // Setup
        when(() => mockLoadQuestions(any()))
            .thenAnswer((_) async => mockQuestions);

        exerciseBloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        ));

        await expectLater(
          exerciseBloc.stream,
          emitsInOrder([
            const ExerciseLoading(),
            isA<ExerciseLoaded>()
                .having((state) => state.currentIndex, 'current index', 0),
          ]),
        );

        // Try to go to previous when at first question (should not change)
        exerciseBloc.add(const PreviousQuestion());

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()
              .having((state) => state.currentIndex, 'current index', 0)),
        );

        // Go to last question
        exerciseBloc.add(const JumpToQuestion(1));

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()
              .having((state) => state.currentIndex, 'current index', 1)),
        );

        // Try to go to next when at last question (should not change)
        exerciseBloc.add(const NextQuestion());

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()
              .having((state) => state.currentIndex, 'current index', 1)),
        );

        // Try to jump to invalid index (should not change)
        exerciseBloc.add(const JumpToQuestion(10));

        await expectLater(
          exerciseBloc.stream,
          emits(isA<ExerciseLoaded>()
              .having((state) => state.currentIndex, 'current index', 1)),
        );
      });
    });
  });
}
