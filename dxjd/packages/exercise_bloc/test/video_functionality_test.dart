import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:exercise_bloc/src/domain/entities/video_entity.dart';
import 'package:exercise_bloc/src/data/models/video_model.dart';
import 'package:exercise_bloc/src/data/repositories/video_repository_impl.dart';
import 'package:exercise_bloc/src/data/datasources/video_datasource.dart';
import 'package:exercise_bloc/src/domain/usecases/play_video.dart';
import 'package:exercise_bloc/src/presentation/bloc/video/video_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/video/video_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/video/video_state.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Video Functionality Tests', () {
    test('VideoEntity can be created and has correct properties', () {
      // Arrange
      final video = VideoEntity(
        id: 1,
        subId: 100,
        aliyunVid: 'test_vid_123',
        cover: 'https://example.com/cover.jpg',
        favoriteCover: 'https://example.com/fav_cover.jpg',
        title: 'Test Video',
        duration: 300,
        currentPosition: 150,
      );

      // Act & Assert
      expect(video.id, equals(1));
      expect(video.subId, equals(100));
      expect(video.aliyunVid, equals('test_vid_123'));
      expect(video.title, equals('Test Video'));
      expect(video.duration, equals(300));
      expect(video.currentPosition, equals(150));
      expect(video.hasValidVideo, isTrue);
      expect(video.formattedDuration, equals('05:00'));
      expect(video.formattedCurrentPosition, equals('02:30'));
      expect(video.playProgress, equals(0.5));
      expect(video.isCompleted, isFalse);
    });

    test('VideoModel can be converted to/from VideoEntity', () {
      // Arrange
      final model = VideoModel(
        id: 2,
        subId: 200,
        aliyunVid: 'model_vid_456',
        cover: 'https://example.com/model_cover.jpg',
        favoriteCover: 'https://example.com/model_fav.jpg',
        title: 'Model Video',
        duration: 600,
        currentPosition: 300,
      );

      // Act
      final entity = model.toEntity();
      final backToModel = VideoModel.fromEntity(entity);

      // Assert
      expect(entity.id, equals(model.id));
      expect(entity.aliyunVid, equals(model.aliyunVid));
      expect(entity.title, equals(model.title));
      expect(backToModel.id, equals(model.id));
      expect(backToModel.aliyunVid, equals(model.aliyunVid));
      expect(backToModel.title, equals(model.title));
    });

    test('VideoModel JSON serialization works correctly', () {
      // Arrange
      final model = VideoModel(
        id: 3,
        subId: 300,
        aliyunVid: 'json_vid_789',
        cover: 'https://example.com/json_cover.jpg',
        favoriteCover: 'https://example.com/json_fav.jpg',
        title: 'JSON Video',
        duration: 900,
        currentPosition: 450,
        lastPlayTime: DateTime(2025, 6, 20, 10, 30),
      );

      // Act
      final json = model.toJson();
      final fromJson = VideoModel.fromJson(json);

      // Assert
      expect(fromJson.id, equals(model.id));
      expect(fromJson.aliyunVid, equals(model.aliyunVid));
      expect(fromJson.title, equals(model.title));
      expect(fromJson.duration, equals(model.duration));
      expect(fromJson.currentPosition, equals(model.currentPosition));
      expect(fromJson.lastPlayTime, equals(model.lastPlayTime));
    });

    test('VideoRepository can be instantiated', () {
      // Arrange
      final remoteDataSource = VideoRemoteDataSourceImpl(
        dio: Dio(),
        baseUrl: 'https://test.api.com',
      );
      final localDataSource = VideoLocalDataSourceImpl(
        databasePath: 'test.db',
      );

      // Act
      final repository = VideoRepositoryImpl(
        remoteDataSource: remoteDataSource,
        localDataSource: localDataSource,
      );

      // Assert
      expect(repository, isNotNull);
    });

    test('PlayVideo use case can be instantiated', () {
      // Arrange
      final remoteDataSource = VideoRemoteDataSourceImpl(
        dio: Dio(),
        baseUrl: 'https://test.api.com',
      );
      final localDataSource = VideoLocalDataSourceImpl(
        databasePath: 'test.db',
      );
      final repository = VideoRepositoryImpl(
        remoteDataSource: remoteDataSource,
        localDataSource: localDataSource,
      );

      // Act
      final useCase = PlayVideo(repository);

      // Assert
      expect(useCase, isNotNull);
    });

    test('VideoBloc can be instantiated and has correct initial state', () {
      // Arrange
      final remoteDataSource = VideoRemoteDataSourceImpl(
        dio: Dio(),
        baseUrl: 'https://test.api.com',
      );
      final localDataSource = VideoLocalDataSourceImpl(
        databasePath: 'test.db',
      );
      final repository = VideoRepositoryImpl(
        remoteDataSource: remoteDataSource,
        localDataSource: localDataSource,
      );
      final playVideo = PlayVideo(repository);

      // Act
      final bloc = VideoBloc(
        videoRepository: repository,
        playVideo: playVideo,
      );

      // Assert
      expect(bloc.state.status, equals(VideoPlaybackStatus.initial));
      expect(bloc.state.videos, isEmpty);
      expect(bloc.state.currentVideoIndex, equals(0));
      expect(bloc.state.hasVideos, isFalse);
      expect(bloc.state.hasCurrentVideo, isFalse);
      expect(bloc.state.canGoPrevious, isFalse);
      expect(bloc.state.canGoNext, isFalse);
    });

    test('VideoState copyWith works correctly', () {
      // Arrange
      const initialState = VideoState();
      final videos = [
        VideoEntity(
          id: 1,
          subId: 100,
          aliyunVid: 'vid1',
          cover: 'cover1.jpg',
          favoriteCover: 'fav1.jpg',
        ),
        VideoEntity(
          id: 2,
          subId: 200,
          aliyunVid: 'vid2',
          cover: 'cover2.jpg',
          favoriteCover: 'fav2.jpg',
        ),
      ];

      // Act
      final newState = initialState.copyWith(
        status: VideoPlaybackStatus.loading,
        videos: videos,
        currentVideoIndex: 1,
      );

      // Assert
      expect(newState.status, equals(VideoPlaybackStatus.loading));
      expect(newState.videos.length, equals(2));
      expect(newState.currentVideoIndex, equals(1));
      expect(newState.hasVideos, isTrue);
      expect(newState.canGoPrevious, isTrue);
      expect(newState.canGoNext, isFalse);
    });

    test('VideoState helper methods work correctly', () {
      // Arrange
      final videos = [
        VideoEntity(
          id: 1,
          subId: 100,
          aliyunVid: 'vid1',
          cover: 'cover1.jpg',
          favoriteCover: 'fav1.jpg',
        ),
        VideoEntity(
          id: 2,
          subId: 200,
          aliyunVid: 'vid2',
          cover: 'cover2.jpg',
          favoriteCover: 'fav2.jpg',
        ),
      ];

      final state = VideoState(
        videos: videos,
        currentVideoIndex: 0,
      );

      // Act & Assert
      expect(state.getCurrentVideo, equals(videos[0]));
      expect(state.canGoPrevious, isFalse);
      expect(state.canGoNext, isTrue);

      final nextState = state.copyWith(currentVideoIndex: 1);
      expect(nextState.getCurrentVideo, equals(videos[1]));
      expect(nextState.canGoPrevious, isTrue);
      expect(nextState.canGoNext, isFalse);
    });

    test('Video events can be created', () {
      // Test LoadVideosForQuestions event
      final loadEvent = LoadVideosForQuestions(questionIds: [1, 2, 3]);
      expect(loadEvent.questionIds, equals([1, 2, 3]));

      // Test PlayVideoForQuestion event
      final playEvent = PlayVideoForQuestion(
        questionId: 1,
        userId: 'user123',
        enableDownload: true,
      );
      expect(playEvent.questionId, equals(1));
      expect(playEvent.userId, equals('user123'));
      expect(playEvent.enableDownload, isTrue);

      // Test JumpToVideo event
      final jumpEvent = JumpToVideo(videoIndex: 2);
      expect(jumpEvent.videoIndex, equals(2));

      // Test UpdateVideoPosition event
      final updateEvent = UpdateVideoPosition(
        videoId: 1,
        position: 150,
        duration: 300,
        userId: 'user123',
      );
      expect(updateEvent.videoId, equals(1));
      expect(updateEvent.position, equals(150));
      expect(updateEvent.duration, equals(300));
      expect(updateEvent.userId, equals('user123'));
    });

    test('Video download status enum works correctly', () {
      expect(VideoDownloadStatus.none.toString(), contains('none'));
      expect(
          VideoDownloadStatus.downloading.toString(), contains('downloading'));
      expect(VideoDownloadStatus.completed.toString(), contains('completed'));
      expect(VideoDownloadStatus.error.toString(), contains('error'));
    });

    test('Video playback status enum works correctly', () {
      expect(VideoPlaybackStatus.initial.toString(), contains('initial'));
      expect(VideoPlaybackStatus.loading.toString(), contains('loading'));
      expect(VideoPlaybackStatus.ready.toString(), contains('ready'));
      expect(VideoPlaybackStatus.playing.toString(), contains('playing'));
      expect(VideoPlaybackStatus.paused.toString(), contains('paused'));
      expect(VideoPlaybackStatus.completed.toString(), contains('completed'));
      expect(VideoPlaybackStatus.error.toString(), contains('error'));
    });
  });
}

// Note: In real tests you would use mocktail for proper mocking
