import 'package:equatable/equatable.dart';

/// Question entity representing a driving exam question
class QuestionEntity extends Equatable {
  const QuestionEntity({
    required this.id,
    required this.sortId,
    required this.title,
    required this.type,
    required this.options,
    required this.correctAnswer,
    this.imagePath,
    this.videoPath,
    this.audioUrl,
    this.explanation,
    this.skills,
    this.userAnswer,
    this.isAnswered = false,
    this.isCorrect = false,
    this.isCollected = false,
    this.answerCount = 0,
    this.correctCount = 0,
    this.errorCount = 0,
    this.playCount = 0,
    this.lastPlayTime,
    this.isPlaying = false,
  });

  /// Unique identifier
  final int id;
  
  /// Sort identifier for grouping
  final int sortId;
  
  /// Question title/content
  final String title;
  
  /// Question type: single, multiple, judge
  final QuestionType type;
  
  /// Answer options
  final List<QuestionOption> options;
  
  /// Correct answer (e.g., "a", "ab", "true")
  final String correctAnswer;
  
  /// Optional image path
  final String? imagePath;
  
  /// Optional video path
  final String? videoPath;
  
  /// Optional audio URL for listening mode
  final String? audioUrl;
  
  /// Answer explanation
  final String? explanation;
  
  /// Learning skills/tips
  final String? skills;
  
  /// User's selected answer
  final String? userAnswer;
  
  /// Whether user has answered this question
  final bool isAnswered;
  
  /// Whether user's answer is correct
  final bool isCorrect;
  
  /// Whether question is bookmarked
  final bool isCollected;
  
  /// Total times this question was answered
  final int answerCount;
  
  /// Times answered correctly
  final int correctCount;
  
  /// Times answered incorrectly
  final int errorCount;
  
  /// Times audio was played (for listen mode)
  final int playCount;
  
  /// Last time audio was played
  final DateTime? lastPlayTime;
  
  /// Whether audio is currently playing
  final bool isPlaying;

  /// Create a copy with updated fields
  QuestionEntity copyWith({
    int? id,
    int? sortId,
    String? title,
    QuestionType? type,
    List<QuestionOption>? options,
    String? correctAnswer,
    String? imagePath,
    String? videoPath,
    String? audioUrl,
    String? explanation,
    String? skills,
    String? userAnswer,
    bool? isAnswered,
    bool? isCorrect,
    bool? isCollected,
    int? answerCount,
    int? correctCount,
    int? errorCount,
    int? playCount,
    DateTime? lastPlayTime,
    bool? isPlaying,
  }) {
    return QuestionEntity(
      id: id ?? this.id,
      sortId: sortId ?? this.sortId,
      title: title ?? this.title,
      type: type ?? this.type,
      options: options ?? this.options,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      imagePath: imagePath ?? this.imagePath,
      videoPath: videoPath ?? this.videoPath,
      audioUrl: audioUrl ?? this.audioUrl,
      explanation: explanation ?? this.explanation,
      skills: skills ?? this.skills,
      userAnswer: userAnswer ?? this.userAnswer,
      isAnswered: isAnswered ?? this.isAnswered,
      isCorrect: isCorrect ?? this.isCorrect,
      isCollected: isCollected ?? this.isCollected,
      answerCount: answerCount ?? this.answerCount,
      correctCount: correctCount ?? this.correctCount,
      errorCount: errorCount ?? this.errorCount,
      playCount: playCount ?? this.playCount,
      lastPlayTime: lastPlayTime ?? this.lastPlayTime,
      isPlaying: isPlaying ?? this.isPlaying,
    );
  }

  @override
  List<Object?> get props => [
        id,
        sortId,
        title,
        type,
        options,
        correctAnswer,
        imagePath,
        videoPath,
        audioUrl,
        explanation,
        skills,
        userAnswer,
        isAnswered,
        isCorrect,
        isCollected,
        answerCount,
        correctCount,
        errorCount,
        playCount,
        lastPlayTime,
        isPlaying,
      ];
}

/// Question type enumeration
enum QuestionType {
  single,   // 单选题
  multiple, // 多选题
  judge,    // 判断题
}

/// Question option
class QuestionOption extends Equatable {
  const QuestionOption({
    required this.key,
    required this.text,
    this.imagePath,
  });

  /// Option key (a, b, c, d)
  final String key;
  
  /// Option text content
  final String text;
  
  /// Optional image for this option
  final String? imagePath;

  @override
  List<Object?> get props => [key, text, imagePath];
}
