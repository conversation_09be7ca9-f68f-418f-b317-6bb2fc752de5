import '../entities/exercise_progress.dart';
import '../repositories/progress_repository.dart';

/// Use case for saving exercise progress
class SaveProgress {
  const SaveProgress(this._repository);

  final ProgressRepository _repository;

  /// Execute the use case
  Future<void> call(SaveProgressParams params) async {
    switch (params.type) {
      case ProgressType.exercise:
        if (params.exerciseProgress != null) {
          await _repository.saveProgress(params.exerciseProgress!);
        }
        break;
      case ProgressType.listen:
        // For listen progress, we can convert it to ExerciseProgress
        // or handle it differently based on your needs
        if (params.listenProgress != null) {
          // For now, we'll create a simple implementation
          // In a real app, you might want to extend this
          final exerciseProgress = ExerciseProgress.initial().copyWith(
            subject: params.listenProgress!.subject,
            type: 'listen',
            currentIndex: params.listenProgress!.currentIndex,
            totalQuestions: params.listenProgress!.totalQuestions,
            lastUpdated: DateTime.now(),
          );
          await _repository.saveProgress(exerciseProgress);
        }
        break;
    }
  }
}

/// Parameters for saving progress
class SaveProgressParams {
  const SaveProgressParams({
    required this.type,
    this.exerciseProgress,
    this.listenProgress,
  });

  /// Type of progress to save
  final ProgressType type;

  /// Exercise progress data
  final ExerciseProgress? exerciseProgress;

  /// Listen progress data
  final ListenProgress? listenProgress;
}

/// Types of progress
enum ProgressType {
  exercise,
  listen,
}
