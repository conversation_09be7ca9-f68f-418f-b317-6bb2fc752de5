import '../entities/question_entity.dart';
import '../repositories/question_repository.dart';

/// Use case for answering a question
class AnswerQuestion {
  const AnswerQuestion(this._repository);

  final QuestionRepository _repository;

  /// Execute the use case
  Future<AnswerResult> call(AnswerQuestionParams params) async {
    // Validate the answer
    final isCorrect = _validateAnswer(
      params.question,
      params.userAnswer,
    );

    // Save the answer
    await _repository.saveAnswer(
      questionId: params.question.id,
      answer: params.userAnswer,
      isCorrect: isCorrect,
      subject: params.subject,
    );

    // Update question statistics
    await _repository.updateQuestionStats(
      questionId: params.question.id,
      answerCount: params.question.answerCount + 1,
      correctCount: isCorrect 
          ? params.question.correctCount + 1 
          : params.question.correctCount,
      errorCount: !isCorrect 
          ? params.question.errorCount + 1 
          : params.question.errorCount,
    );

    return AnswerResult(
      isCorrect: isCorrect,
      correctAnswer: params.question.correctAnswer,
      explanation: params.question.explanation,
      skills: params.question.skills,
    );
  }

  /// Validate user's answer against correct answer
  bool _validateAnswer(QuestionEntity question, String userAnswer) {
    final correctAnswer = question.correctAnswer.toLowerCase();
    final normalizedUserAnswer = userAnswer.toLowerCase();

    switch (question.type) {
      case QuestionType.single:
      case QuestionType.judge:
        return correctAnswer == normalizedUserAnswer;
      
      case QuestionType.multiple:
        // For multiple choice, sort both answers and compare
        final correctChars = correctAnswer.split('')..sort();
        final userChars = normalizedUserAnswer.split('')..sort();
        return correctChars.join() == userChars.join();
    }
  }
}

/// Parameters for answering a question
class AnswerQuestionParams {
  const AnswerQuestionParams({
    required this.question,
    required this.userAnswer,
    required this.subject,
  });

  /// The question being answered
  final QuestionEntity question;
  
  /// User's selected answer
  final String userAnswer;
  
  /// Subject (1 for subject 1, 4 for subject 4)
  final int subject;
}

/// Result of answering a question
class AnswerResult {
  const AnswerResult({
    required this.isCorrect,
    required this.correctAnswer,
    this.explanation,
    this.skills,
  });

  /// Whether the answer is correct
  final bool isCorrect;
  
  /// The correct answer
  final String correctAnswer;
  
  /// Answer explanation
  final String? explanation;
  
  /// Learning skills/tips
  final String? skills;
}
