import '../entities/question_entity.dart';
import '../repositories/audio_repository.dart';
import '../repositories/question_repository.dart';

/// Use case for playing question audio
class PlayAudio {
  const PlayAudio(
    this._audioRepository,
    this._questionRepository,
  );

  final AudioRepository _audioRepository;
  final QuestionRepository _questionRepository;

  /// Execute the use case
  Future<void> call(PlayAudioParams params) async {
    final question = params.question;

    // Check if question has audio
    if (question.audioUrl == null || question.audioUrl!.isEmpty) {
      throw const AudioNotAvailableException(
          'No audio available for this question');
    }

    // Play the audio
    await _audioRepository.play(question.audioUrl!);

    // Update play statistics
    await _questionRepository.saveListenProgress(
      questionId: question.id,
      playCount: question.playCount + 1,
      playTime: DateTime.now(),
      userId: params.userId,
    );
  }
}

/// Parameters for playing audio
class PlayAudioParams {
  const PlayAudioParams({
    required this.question,
    this.userId,
  });

  /// The question whose audio to play
  final QuestionEntity question;

  /// User ID for tracking
  final String? userId;
}

/// Exception thrown when audio is not available
class AudioNotAvailableException implements Exception {
  const AudioNotAvailableException(this.message);

  final String message;

  @override
  String toString() => 'AudioNotAvailableException: $message';
}
