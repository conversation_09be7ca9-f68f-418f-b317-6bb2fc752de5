import '../entities/video_entity.dart';
import '../repositories/video_repository.dart';

/// Use case for playing videos
class PlayVideo {
  PlayVideo(this._videoRepository);

  final VideoRepository _videoRepository;

  /// Execute the use case
  Future<PlayVideoResult> call(PlayVideoParams params) async {
    try {
      // Get video information
      final video = await _videoRepository.getVideoByQuestionId(params.questionId);
      
      if (video == null || !video.hasValidVideo) {
        return PlayVideoResult.error('Video not available for this question');
      }

      // Check if video is downloaded locally
      final isDownloaded = await _videoRepository.isVideoDownloaded(video.aliyunVid);
      String videoUrl;

      if (isDownloaded) {
        // Use local file
        final localPath = await _videoRepository.getLocalVideoPath(video.aliyunVid);
        if (localPath != null) {
          videoUrl = localPath;
        } else {
          // Local file not found, get remote URL
          videoUrl = await _videoRepository.getVideoUrl(video.aliyunVid);
        }
      } else {
        // Get remote URL
        videoUrl = await _videoRepository.getVideoUrl(video.aliyunVid);
        
        // Start background download if enabled
        if (params.enableDownload) {
          _videoRepository.downloadVideo(video.aliyunVid, videoUrl).catchError((e) {
            // Download failed, but don't affect playback
          });
        }
      }

      // Update play count
      await _videoRepository.updateVideoPlayCount(
        videoId: video.id,
        userId: params.userId,
      );

      return PlayVideoResult.success(
        video: video,
        videoUrl: videoUrl,
        isLocal: isDownloaded,
      );
    } catch (e) {
      return PlayVideoResult.error('Failed to play video: ${e.toString()}');
    }
  }
}

/// Parameters for PlayVideo use case
class PlayVideoParams {
  const PlayVideoParams({
    required this.questionId,
    this.userId,
    this.enableDownload = true,
  });

  final int questionId;
  final String? userId;
  final bool enableDownload;
}

/// Result of PlayVideo use case
class PlayVideoResult {
  const PlayVideoResult._({
    this.video,
    this.videoUrl,
    this.isLocal = false,
    this.error,
  });

  final VideoEntity? video;
  final String? videoUrl;
  final bool isLocal;
  final String? error;

  bool get isSuccess => error == null && video != null && videoUrl != null;
  bool get isError => error != null;

  factory PlayVideoResult.success({
    required VideoEntity video,
    required String videoUrl,
    required bool isLocal,
  }) {
    return PlayVideoResult._(
      video: video,
      videoUrl: videoUrl,
      isLocal: isLocal,
    );
  }

  factory PlayVideoResult.error(String error) {
    return PlayVideoResult._(error: error);
  }
}
