import '../entities/question_entity.dart';
import '../repositories/question_repository.dart';

/// Use case for loading questions
class LoadQuestions {
  const LoadQuestions(this._repository);

  final QuestionRepository _repository;

  /// Execute the use case
  Future<List<QuestionEntity>> call(LoadQuestionsParams params) async {
    return await _repository.getQuestions(
      subject: params.subject,
      type: params.type,
      ids: params.ids,
      sortId: params.sortId,
      mainId: params.mainId,
      childId: params.childId,
      luid: params.luid,
    );
  }
}

/// Parameters for loading questions
class LoadQuestionsParams {
  const LoadQuestionsParams({
    required this.subject,
    required this.type,
    this.ids,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
  });

  /// Subject (1 for subject 1, 4 for subject 4)
  final int subject;
  
  /// Exercise type (chapter, random, error, collect, etc.)
  final String type;
  
  /// Specific question IDs to load
  final List<int>? ids;
  
  /// Sort/chapter ID
  final int? sortId;
  
  /// Main category ID
  final int? mainId;
  
  /// Child category ID
  final int? childId;
  
  /// Learning unit ID
  final int? luid;
}
