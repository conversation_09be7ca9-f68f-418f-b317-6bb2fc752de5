/// Audio player states
enum AudioPlayerState {
  stopped,
  playing,
  paused,
  completed,
  error,
}

/// Abstract repository for audio operations
abstract class AudioRepository {
  /// Play audio from URL
  Future<void> play(String url);

  /// Pause current audio
  Future<void> pause();

  /// Resume paused audio
  Future<void> resume();

  /// Stop current audio
  Future<void> stop();

  /// Seek to specific position
  Future<void> seekTo(Duration position);

  /// Set playback speed
  Future<void> setPlaybackRate(double rate);

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume);

  /// Get current playback position
  Future<Duration> getCurrentPosition();

  /// Get total duration of current audio
  Future<Duration> getDuration();

  /// Stream of playback position updates
  Stream<Duration> get positionStream;

  /// Stream of duration updates
  Stream<Duration> get durationStream;

  /// Stream of player state changes
  Stream<AudioPlayerState> get playerStateStream;

  /// Stream of playback completion events
  Stream<void> get onPlayerComplete;

  /// Check if audio is currently playing
  bool get isPlaying;

  /// Check if audio is paused
  bool get isPaused;

  /// Check if audio is stopped
  bool get isStopped;

  /// Release audio player resources
  Future<void> dispose();

  /// Configure audio session for background playback
  Future<void> configureAudioSession({
    bool enableBackgroundPlayback = true,
    bool mixWithOthers = false,
  });

  /// Handle audio interruptions (phone calls, etc.)
  Stream<AudioInterruption> get onAudioInterruption;
}

/// Audio interruption types
enum AudioInterruption {
  began,
  ended,
}
