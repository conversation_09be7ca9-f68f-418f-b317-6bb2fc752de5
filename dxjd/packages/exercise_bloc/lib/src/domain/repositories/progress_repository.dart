import '../entities/exercise_progress.dart';

/// Abstract repository for progress data operations
abstract class ProgressRepository {
  /// Get exercise progress
  Future<ExerciseProgress> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  });

  /// Save exercise progress
  Future<void> saveProgress(ExerciseProgress progress);

  /// Update progress with specific values
  Future<void> updateProgress({
    required int subject,
    required String type,
    required int totalQuestions,
    required int answeredQuestions,
    required int correctAnswers,
    required int wrongAnswers,
    required Duration timeSpent,
    String? userId,
    int? sortId,
  });

  /// Reset progress to initial state
  Future<void> resetProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  });

  /// Get all progress records for a user
  Future<List<ExerciseProgress>> getAllProgress({
    String? userId,
  });

  /// Get progress statistics
  Future<Map<String, dynamic>> getStatistics({
    required int subject,
    String? userId,
  });

  /// Sync progress with server
  Future<void> syncWithServer({
    String? userId,
  });

  /// Clear progress data
  Future<void> clearProgress({
    String? userId,
  });
}
