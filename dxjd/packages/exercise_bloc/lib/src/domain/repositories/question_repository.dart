import '../entities/question_entity.dart';

/// Abstract repository for question data operations
abstract class QuestionRepository {
  /// Load questions based on criteria
  Future<List<QuestionEntity>> getQuestions({
    required int subject,
    required String type,
    List<int>? ids,
    int? sortId,
    int? mainId,
    int? childId,
    int? luid,
  });

  /// Get a specific question by ID
  Future<QuestionEntity?> getQuestionById(int id);

  /// Save user's answer for a question
  Future<void> saveAnswer({
    required int questionId,
    required String answer,
    required bool isCorrect,
    required int subject,
  });

  /// Get error questions (questions answered incorrectly)
  Future<List<QuestionEntity>> getErrorQuestions({
    required int subject,
    String? userId,
  });

  /// Get collected questions (bookmarked questions)
  Future<List<QuestionEntity>> getCollectedQuestions({
    required int subject,
    String? userId,
  });

  /// Toggle question collection status
  Future<void> toggleCollection({
    required int questionId,
    required bool isCollected,
    String? userId,
  });

  /// Delete a question from error or collection list
  Future<void> deleteQuestion({
    required int questionId,
    required String type, // 'error' or 'collect'
    String? userId,
  });

  /// Save listening progress for a question
  Future<void> saveListenProgress({
    required int questionId,
    required int playCount,
    required DateTime playTime,
    String? userId,
  });

  /// Get questions by chapter/sort ID
  Future<List<QuestionEntity>> getQuestionsBySort({
    required int sortId,
    required int subject,
    String? userId,
  });

  /// Search questions by keyword
  Future<List<QuestionEntity>> searchQuestions({
    required String keyword,
    required int subject,
  });

  /// Get random questions
  Future<List<QuestionEntity>> getRandomQuestions({
    required int subject,
    required int count,
    List<int>? excludeIds,
  });

  /// Update question statistics
  Future<void> updateQuestionStats({
    required int questionId,
    int? correctCount,
    int? errorCount,
    int? answerCount,
  });

  /// Batch save answers (for exam mode)
  Future<void> batchSaveAnswers({
    required List<QuestionEntity> questions,
    required int subject,
    String? userId,
  });

  /// Get question statistics
  Future<Map<String, int>> getQuestionStats({
    required int subject,
    String? userId,
  });

  /// Sync local data with server
  Future<void> syncWithServer({
    String? userId,
  });

  /// Clear local cache
  Future<void> clearCache();
}
