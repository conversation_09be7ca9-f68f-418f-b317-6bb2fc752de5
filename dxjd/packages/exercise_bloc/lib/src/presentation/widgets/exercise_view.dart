import 'package:flutter/material.dart';
import 'package:quiz/quiz.dart';
import 'package:tools/tools.dart';

/// Exercise view widget for doing questions - 完全复现旧版做题界面
class ExerciseView extends StatefulWidget {
  const ExerciseView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
  });

  final int subject;
  final String type;
  final int? sortId;

  @override
  State<ExerciseView> createState() => _ExerciseViewState();
}

class _ExerciseViewState extends State<ExerciseView> {
  List<QuizMo> _questions = [];
  int _currentIndex = 0;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
  }

  Future<void> _loadQuestions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      List<QuizMo> questions = [];

      // 根据类型加载题目
      switch (widget.type) {
        case 'chapter':
          questions = await QuizUtils.get().getChapterQuiz(
            widget.sortId ?? 1,
            widget.subject == 1 ? '0' : '0',
            [], // cityid
            '', // uid
            true,
            'chapter',
          );
          break;
        case 'mock':
          questions = await QuizUtils.get().getMockQuiz(
            widget.subject,
            widget.subject == 1 ? '0' : '0',
            [],
            '',
            'mock',
            false,
          );
          break;
        case 'random':
          // 随机练习暂时使用章节题目
          questions = await QuizUtils.get().getChapterQuiz(
            widget.sortId ?? 1,
            widget.subject == 1 ? '0' : '0',
            [],
            '',
            true,
            'random',
          );
          break;
        default:
          questions = await QuizUtils.get().getChapterQuiz(
            widget.sortId ?? 1,
            widget.subject == 1 ? '0' : '0',
            [],
            '',
            true,
            'chapter',
          );
      }

      setState(() {
        _questions = questions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1992EF)),
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败: $_errorMessage',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadQuestions,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_questions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.quiz_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无题目',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return _buildQuestionView();
  }

  Widget _buildQuestionView() {
    final question = _questions[_currentIndex];

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 进度条和题目信息
          _buildProgressHeader(),

          // 题目内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(UIUtils.dp(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 题目文本
                  Text(
                    question.sub_Titles,
                    style: TextStyle(
                      fontSize: UIUtils.dp(16),
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF333333),
                      height: 1.5,
                    ),
                  ),

                  SizedBox(height: UIUtils.dp(24)),

                  // 选项列表
                  _buildOptions(question),
                ],
              ),
            ),
          ),

          // 底部导航按钮
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Container(
      padding: EdgeInsets.all(UIUtils.dp(16)),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
        ),
      ),
      child: Row(
        children: [
          Text(
            '${_currentIndex + 1}/${_questions.length}',
            style: TextStyle(
              fontSize: UIUtils.dp(14),
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1992EF),
            ),
          ),
          SizedBox(width: UIUtils.dp(12)),
          Expanded(
            child: LinearProgressIndicator(
              value: (_currentIndex + 1) / _questions.length,
              backgroundColor: const Color(0xFFE9ECEF),
              valueColor:
                  const AlwaysStoppedAnimation<Color>(Color(0xFF1992EF)),
              minHeight: UIUtils.dp(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(QuizMo question) {
    final options = <Map<String, String>>[];

    if (question.optA.isNotEmpty) {
      options.add({'key': 'A', 'text': question.optA});
    }
    if (question.optB.isNotEmpty) {
      options.add({'key': 'B', 'text': question.optB});
    }
    if (question.optC.isNotEmpty) {
      options.add({'key': 'C', 'text': question.optC});
    }
    if (question.optD.isNotEmpty) {
      options.add({'key': 'D', 'text': question.optD});
    }

    return Column(
      children: options.map((option) {
        final isSelected = question.selection == option['key'];
        final isCorrect = question.answer == option['key'];
        final showResult = question.selection.isNotEmpty;

        Color borderColor = const Color(0xFFE9ECEF);
        Color backgroundColor = Colors.white;
        Color textColor = const Color(0xFF333333);

        if (showResult) {
          if (isCorrect) {
            borderColor = const Color(0xFF52C41A);
            backgroundColor = const Color(0xFF52C41A).withOpacity(0.1);
            textColor = const Color(0xFF52C41A);
          } else if (isSelected) {
            borderColor = const Color(0xFFFF4D4F);
            backgroundColor = const Color(0xFFFF4D4F).withOpacity(0.1);
            textColor = const Color(0xFFFF4D4F);
          }
        } else if (isSelected) {
          borderColor = const Color(0xFF1992EF);
          backgroundColor = const Color(0xFF1992EF).withOpacity(0.1);
          textColor = const Color(0xFF1992EF);
        }

        return Container(
          margin: EdgeInsets.only(bottom: UIUtils.dp(12)),
          child: GestureDetector(
            onTap: question.selection.isEmpty
                ? () => _selectOption(option['key']!)
                : null,
            child: Container(
              padding: EdgeInsets.all(UIUtils.dp(16)),
              decoration: BoxDecoration(
                border: Border.all(color: borderColor, width: 1),
                borderRadius: BorderRadius.circular(UIUtils.dp(8)),
                color: backgroundColor,
              ),
              child: Row(
                children: [
                  // 选项标识
                  Container(
                    width: UIUtils.dp(24),
                    height: UIUtils.dp(24),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: borderColor, width: 2),
                      color: isSelected || (showResult && isCorrect)
                          ? borderColor
                          : Colors.white,
                    ),
                    child: (isSelected || (showResult && isCorrect))
                        ? Icon(
                            Icons.check,
                            size: UIUtils.dp(14),
                            color: Colors.white,
                          )
                        : null,
                  ),

                  SizedBox(width: UIUtils.dp(12)),

                  // 选项内容
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '${option['key']}. ',
                            style: TextStyle(
                              fontSize: UIUtils.dp(16),
                              fontWeight: FontWeight.bold,
                              color: textColor,
                            ),
                          ),
                          TextSpan(
                            text: option['text'],
                            style: TextStyle(
                              fontSize: UIUtils.dp(16),
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: EdgeInsets.all(UIUtils.dp(16)),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE9ECEF), width: 1),
        ),
      ),
      child: Row(
        children: [
          // 上一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: _currentIndex > 0 ? _previousQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF666666),
                side: const BorderSide(color: Color(0xFFE9ECEF)),
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: UIUtils.dp(12)),
              ),
              child: const Text('上一题'),
            ),
          ),

          SizedBox(width: UIUtils.dp(16)),

          // 下一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed:
                  _currentIndex < _questions.length - 1 ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1992EF),
                foregroundColor: Colors.white,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: UIUtils.dp(12)),
              ),
              child: Text(_currentIndex < _questions.length - 1 ? '下一题' : '完成'),
            ),
          ),
        ],
      ),
    );
  }

  void _selectOption(String option) {
    setState(() {
      _questions[_currentIndex].selection = option;
    });
  }

  void _previousQuestion() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    }
  }

  void _nextQuestion() {
    if (_currentIndex < _questions.length - 1) {
      setState(() {
        _currentIndex++;
      });
    }
  }
}
