import 'package:flutter/material.dart';

/// Exercise module specific styles that match the original implementation
class ExerciseStyles {
  ExerciseStyles._();

  // Colors from original Style class
  static const Color bg = Color(0xFFF6F5F5);
  static const Color bg4 = Color(0xFFF2F2F2);
  static const Color line = Color(0xFFF4F8FC);
  static const Color dark = Color(0xFFD4D4D4);
  
  static const Color text1 = Color(0xFF333333);
  static const Color text2 = Color(0xFF666666);
  static const Color text3 = Color(0xFF999999);
  static const Color text4 = Color(0xFFCCCCCC);
  
  static const Color blue = Color(0xFF1992EF);
  static const Color red = Color(0xFFFF4D4D);
  static const Color green1 = Color(0xFF00C853);
  static const Color green2 = Color(0xFF4CAF50);
  static const Color yellow = Color(0xFFFFCB63);
  static const Color orange = Color(0xFFFF9800);

  // Text styles matching original Option class
  static const TextStyle styleText1H18W500 = TextStyle(
    color: text1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static const TextStyle styleText1H16W500 = TextStyle(
    color: text1,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static const TextStyle styleText2H14W500 = TextStyle(
    color: text2,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static const TextStyle styleBlue18W500 = TextStyle(
    color: blue,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle styleRed18W500 = TextStyle(
    color: red,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle styleGreen18W500 = TextStyle(
    color: green1,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle styleWhite14W500 = TextStyle(
    color: Colors.white,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle styleWhite16W500 = TextStyle(
    color: Colors.white,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  // Box decorations for options
  static const BoxDecoration optionNone = BoxDecoration(
    border: Border.fromBorderSide(
      BorderSide(width: 1, color: text3, style: BorderStyle.solid),
    ),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration optionNone3 = BoxDecoration(
    border: Border.fromBorderSide(
      BorderSide(width: 1, color: text3, style: BorderStyle.solid),
    ),
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration optionSelect = BoxDecoration(
    color: blue,
    borderRadius: BorderRadius.all(Radius.circular(50)),
    boxShadow: [
      BoxShadow(
        color: Color(0x33000000),
        offset: Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );

  static const BoxDecoration optionCorrect = BoxDecoration(
    color: blue,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration optionCorrect3 = BoxDecoration(
    color: blue,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  static const BoxDecoration optionWrong = BoxDecoration(
    color: red,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static const BoxDecoration optionWrong3 = BoxDecoration(
    color: red,
    borderRadius: BorderRadius.all(Radius.circular(8)),
  );

  // Button styles
  static const BoxDecoration styleBtnBlue = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xFF1992EF),
        Color(0xFF61B3F5),
      ],
    ),
    borderRadius: BorderRadius.all(Radius.circular(23)),
    boxShadow: [
      BoxShadow(
        color: Color(0x33000000),
        offset: Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );

  // Container styles
  static const BoxDecoration styleBoxWhiteR4 = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(4)),
    boxShadow: [
      BoxShadow(
        color: Color(0x0F000000),
        offset: Offset(0, 1),
        blurRadius: 3,
      ),
    ],
  );

  static const BoxDecoration styleBoxYellow2R50 = BoxDecoration(
    color: Color(0xFFFFCB63),
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  // Tab bar styles
  static const BoxDecoration styleTebLine = BoxDecoration(
    color: Color(0xFFF0F0F0),
    borderRadius: BorderRadius.all(Radius.circular(16)),
  );

  static const BoxDecoration styleTebIndicatorLine = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xFF1992EF),
        Color(0xFF61B3F5),
      ],
    ),
    borderRadius: BorderRadius.all(Radius.circular(16)),
    boxShadow: [
      BoxShadow(
        color: Color(0x33000000),
        offset: Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );

  // Progress bar styles
  static const BoxDecoration numNone = BoxDecoration(
    color: Color(0xFFF0F0F0),
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration numCorrect = BoxDecoration(
    color: green1,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration numWrong = BoxDecoration(
    color: red,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration numNoneCurrent = BoxDecoration(
    color: blue,
    borderRadius: BorderRadius.all(Radius.circular(4)),
  );

  static const BoxDecoration numCorrectCurrent = BoxDecoration(
    color: green1,
    borderRadius: BorderRadius.all(Radius.circular(4)),
    border: Border.fromBorderSide(
      BorderSide(width: 2, color: Colors.white),
    ),
  );

  static const BoxDecoration numWrongCurrent = BoxDecoration(
    color: red,
    borderRadius: BorderRadius.all(Radius.circular(4)),
    border: Border.fromBorderSide(
      BorderSide(width: 2, color: Colors.white),
    ),
  );

  // Utility methods
  static double dp(double value) {
    // Simple dp conversion - in real app this would use MediaQuery
    return value;
  }

  static double get checkWidth => dp(32);
  static double get statusHeight => 44; // Default status bar height

  // Font size adaptation (simplified version)
  static TextStyle adaptTextStyle(TextStyle baseStyle, {String fontSize = 'normal'}) {
    double multiplier = 1.0;
    switch (fontSize) {
      case 'min':
        multiplier = 0.9;
        break;
      case 'middle':
        multiplier = 1.1;
        break;
      case 'big':
        multiplier = 1.2;
        break;
      default:
        multiplier = 1.0;
    }
    
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * multiplier,
    );
  }

  // Option state constants
  static const int stateNone = 11;
  static const int stateSelected = 12;
  static const int stateCorrect = 13;
  static const int stateWrong = 14;
  static const int stateRightKey = 15;
}

/// Extension for easier access to common measurements
extension ExerciseStylesExtension on ExerciseStyles {
  static EdgeInsets get defaultPadding => EdgeInsets.all(ExerciseStyles.dp(16));
  static EdgeInsets get smallPadding => EdgeInsets.all(ExerciseStyles.dp(8));
  static EdgeInsets get largePadding => EdgeInsets.all(ExerciseStyles.dp(24));
}
