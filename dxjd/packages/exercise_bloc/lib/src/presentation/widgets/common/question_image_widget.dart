import 'package:flutter/material.dart';
import '../../../domain/entities/question_entity.dart';
import 'exercise_styles.dart';

/// Question image widget that handles different image types
class QuestionImageWidget extends StatelessWidget {
  const QuestionImageWidget({
    super.key,
    required this.question,
    this.onImageTap,
  });

  final QuestionEntity question;
  final VoidCallback? onImageTap;

  @override
  Widget build(BuildContext context) {
    final imagePath = question.imagePath;
    
    if (imagePath == null || imagePath.isEmpty || imagePath == '1') {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.only(
        top: ExerciseStyles.dp(16),
        left: ExerciseStyles.dp(16),
        right: ExerciseStyles.dp(16),
      ),
      alignment: AlignmentDirectional.center,
      child: _buildImageWidget(imagePath),
    );
  }

  Widget _buildImageWidget(String imagePath) {
    if (imagePath.endsWith('.gif')) {
      return _buildGifImage(imagePath);
    } else {
      return _buildStaticImage(imagePath);
    }
  }

  Widget _buildGifImage(String imagePath) {
    return GestureDetector(
      onTap: onImageTap,
      child: Image.asset(
        'assets/quiz/$imagePath',
        width: ExerciseStyles.dp(345),
        height: ExerciseStyles.dp(164),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(
          imagePath,
          ExerciseStyles.dp(345),
          ExerciseStyles.dp(164),
        ),
      ),
    );
  }

  Widget _buildStaticImage(String imagePath) {
    return GestureDetector(
      onTap: onImageTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(ExerciseStyles.dp(8)),
        child: Image.asset(
          'assets/quiz/$imagePath',
          width: ExerciseStyles.dp(345),
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(
            imagePath,
            ExerciseStyles.dp(345),
            null,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String imagePath, double width, double? height) {
    return GestureDetector(
      onTap: onImageTap,
      child: Container(
        width: width,
        height: height ?? ExerciseStyles.dp(200),
        decoration: BoxDecoration(
          color: ExerciseStyles.bg4,
          borderRadius: BorderRadius.circular(ExerciseStyles.dp(8)),
          border: Border.all(
            color: ExerciseStyles.dark,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: ExerciseStyles.dp(48),
              color: ExerciseStyles.text3,
            ),
            SizedBox(height: ExerciseStyles.dp(8)),
            Text(
              '图片加载失败',
              style: ExerciseStyles.styleText2H14W500,
            ),
            SizedBox(height: ExerciseStyles.dp(4)),
            Text(
              imagePath,
              style: TextStyle(
                color: ExerciseStyles.text3,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Video section widget for questions with video content
class QuestionVideoWidget extends StatelessWidget {
  const QuestionVideoWidget({
    super.key,
    required this.question,
    required this.onVideoTap,
  });

  final QuestionEntity question;
  final VoidCallback onVideoTap;

  @override
  Widget build(BuildContext context) {
    final videoPath = question.videoPath;
    
    if (videoPath == null || videoPath.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(
            left: ExerciseStyles.dp(16),
            right: ExerciseStyles.dp(16),
          ),
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                margin: EdgeInsets.only(top: ExerciseStyles.dp(2)),
                child: Image.asset(
                  'assets/exercise/ic_video_icon.png',
                  width: ExerciseStyles.dp(20),
                  height: ExerciseStyles.dp(20),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: ExerciseStyles.dp(6)),
                alignment: Alignment.centerLeft,
                child: Stack(
                  children: [
                    Positioned(
                      bottom: ExerciseStyles.dp(2),
                      child: Container(
                        width: ExerciseStyles.dp(68),
                        height: ExerciseStyles.dp(6),
                        decoration: ExerciseStyles.styleBoxYellow2R50,
                      ),
                    ),
                    Text(
                      '视频讲题',
                      style: ExerciseStyles.styleText1H16W500,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: ExerciseStyles.dp(12)),
        GestureDetector(
          onTap: onVideoTap,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: ExerciseStyles.dp(16)),
            height: ExerciseStyles.dp(200),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(ExerciseStyles.dp(8)),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Video thumbnail placeholder
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black87,
                    borderRadius: BorderRadius.circular(ExerciseStyles.dp(8)),
                  ),
                  child: Icon(
                    Icons.video_library,
                    size: ExerciseStyles.dp(48),
                    color: Colors.white54,
                  ),
                ),
                // Play button
                Container(
                  width: ExerciseStyles.dp(64),
                  height: ExerciseStyles.dp(64),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.play_arrow,
                    size: ExerciseStyles.dp(32),
                    color: ExerciseStyles.blue,
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: ExerciseStyles.dp(16)),
      ],
    );
  }
}

/// Question explanation widget
class QuestionExplanationWidget extends StatelessWidget {
  const QuestionExplanationWidget({
    super.key,
    required this.explanation,
    this.isVisible = true,
  });

  final String? explanation;
  final bool isVisible;

  @override
  Widget build(BuildContext context) {
    if (!isVisible || explanation == null || explanation!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.all(ExerciseStyles.dp(16)),
      padding: EdgeInsets.all(ExerciseStyles.dp(16)),
      decoration: ExerciseStyles.styleBoxWhiteR4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: ExerciseStyles.dp(20),
                color: ExerciseStyles.orange,
              ),
              SizedBox(width: ExerciseStyles.dp(8)),
              Text(
                '答案解析',
                style: ExerciseStyles.styleText1H16W500.copyWith(
                  color: ExerciseStyles.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: ExerciseStyles.dp(12)),
          Text(
            explanation!,
            style: ExerciseStyles.styleText1H16W500,
          ),
        ],
      ),
    );
  }
}
