import 'package:flutter/material.dart';
import '../../../domain/entities/question_entity.dart';
import 'exercise_styles.dart';

/// Option widget that matches the original OptionView implementation
class OptionWidget extends StatelessWidget {
  const OptionWidget({
    super.key,
    required this.option,
    required this.text,
    required this.question,
    required this.userAnswer,
    required this.onTap,
    this.isEnabled = true,
  });

  final String option;
  final String text;
  final QuestionEntity question;
  final String userAnswer;
  final VoidCallback onTap;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        padding: EdgeInsets.only(
          top: ExerciseStyles.dp(8),
          bottom: ExerciseStyles.dp(8),
          left: ExerciseStyles.dp(16),
          right: ExerciseStyles.dp(16),
        ),
        constraints: BoxConstraints(
          minHeight: ExerciseStyles.dp(54),
        ),
        alignment: AlignmentDirectional.centerStart,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(top: ExerciseStyles.dp(1)),
              alignment: Alignment.center,
              width: ExerciseStyles.checkWidth,
              height: ExerciseStyles.checkWidth,
              decoration: _getCheckBoxDecoration(),
              child: Text(
                _getCheckText(),
                style: _getCheckTextStyle(),
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(left: ExerciseStyles.dp(20)),
                constraints: BoxConstraints(
                  minHeight: ExerciseStyles.checkWidth,
                ),
                alignment: Alignment.centerLeft,
                child: RichText(
                  text: TextSpan(
                    style: _getOptionTextStyle(),
                    children: _buildTextSpans(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get the state of this option based on question and user answer
  int _getOptionState() {
    final correctAnswer = question.correctAnswer.toLowerCase();
    final selection = userAnswer.toLowerCase();
    final optionKey = option.toLowerCase();

    if (question.type == QuestionType.multiple) {
      if (question.isAnswered) {
        if (!selection.contains(optionKey) && !correctAnswer.contains(optionKey)) {
          return ExerciseStyles.stateNone;
        } else if (!userAnswer.toLowerCase().contains(optionKey)) {
          return ExerciseStyles.stateRightKey;
        } else if (correctAnswer.contains(optionKey)) {
          return ExerciseStyles.stateCorrect;
        } else if (selection.contains(optionKey)) {
          return ExerciseStyles.stateWrong;
        }
      } else if (userAnswer.toLowerCase().contains(optionKey)) {
        return ExerciseStyles.stateSelected;
      }
    } else {
      // Single choice or judge
      if (question.isAnswered) {
        if (correctAnswer == optionKey) {
          return ExerciseStyles.stateCorrect;
        } else if (selection == optionKey) {
          return ExerciseStyles.stateWrong;
        }
      } else if (selection == optionKey) {
        return ExerciseStyles.stateSelected;
      }
    }
    
    return ExerciseStyles.stateNone;
  }

  /// Get checkbox decoration based on state
  BoxDecoration _getCheckBoxDecoration() {
    final state = _getOptionState();
    
    switch (state) {
      case ExerciseStyles.stateSelected:
        return ExerciseStyles.optionSelect;
      case ExerciseStyles.stateCorrect:
        return question.type == QuestionType.multiple
            ? ExerciseStyles.optionCorrect3
            : ExerciseStyles.optionCorrect;
      case ExerciseStyles.stateWrong:
        return question.type == QuestionType.multiple
            ? ExerciseStyles.optionWrong3
            : ExerciseStyles.optionWrong;
      default:
        return question.type == QuestionType.multiple
            ? ExerciseStyles.optionNone3
            : ExerciseStyles.optionNone;
    }
  }

  /// Get checkbox text style based on state
  TextStyle _getCheckTextStyle() {
    final state = _getOptionState();
    
    if (state == ExerciseStyles.stateCorrect || state == ExerciseStyles.stateWrong) {
      return ExerciseStyles.styleWhite14W500;
    }
    
    return ExerciseStyles.styleText2H14W500;
  }

  /// Get checkbox text based on state
  String _getCheckText() {
    final state = _getOptionState();
    
    if (state == ExerciseStyles.stateSelected) {
      return '';
    }
    
    return option.toUpperCase();
  }

  /// Get option text style based on state
  TextStyle _getOptionTextStyle() {
    final state = _getOptionState();
    
    switch (state) {
      case ExerciseStyles.stateCorrect:
        return ExerciseStyles.styleBlue18W500;
      case ExerciseStyles.stateWrong:
        return ExerciseStyles.styleRed18W500;
      default:
        return ExerciseStyles.styleText1H18W500;
    }
  }

  /// Build text spans for rich text (simplified version)
  List<TextSpan> _buildTextSpans() {
    // For now, return simple text span
    // In the original, this handles highlighting of keywords
    return [
      TextSpan(text: text),
    ];
  }
}

/// Browse mode option widget (read-only)
class BrowseOptionWidget extends StatelessWidget {
  const BrowseOptionWidget({
    super.key,
    required this.option,
    required this.text,
    required this.question,
  });

  final String option;
  final String text;
  final QuestionEntity question;

  @override
  Widget build(BuildContext context) {
    final isCorrect = question.correctAnswer.toLowerCase().contains(option.toLowerCase());
    
    return Container(
      padding: EdgeInsets.only(
        top: ExerciseStyles.dp(8),
        bottom: ExerciseStyles.dp(8),
        left: ExerciseStyles.dp(16),
        right: ExerciseStyles.dp(16),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: ExerciseStyles.dp(1)),
            alignment: Alignment.center,
            width: ExerciseStyles.checkWidth,
            height: ExerciseStyles.checkWidth,
            decoration: isCorrect 
                ? (question.type == QuestionType.multiple
                    ? ExerciseStyles.optionCorrect3
                    : ExerciseStyles.optionCorrect)
                : (question.type == QuestionType.multiple
                    ? ExerciseStyles.optionNone3
                    : ExerciseStyles.optionNone),
            child: Text(
              option.toUpperCase(),
              style: isCorrect 
                  ? ExerciseStyles.styleWhite14W500
                  : ExerciseStyles.styleText2H14W500,
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: ExerciseStyles.dp(20)),
              constraints: BoxConstraints(
                minHeight: ExerciseStyles.checkWidth,
              ),
              alignment: Alignment.centerLeft,
              child: RichText(
                text: TextSpan(
                  style: isCorrect 
                      ? ExerciseStyles.styleBlue18W500
                      : ExerciseStyles.styleText1H18W500,
                  children: [
                    TextSpan(text: text),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Listen mode option widget (simplified display)
class ListenOptionWidget extends StatelessWidget {
  const ListenOptionWidget({
    super.key,
    required this.option,
    required this.text,
  });

  final String option;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${option.toUpperCase()}、',
          style: ExerciseStyles.styleText2H14W500,
        ),
        Expanded(
          child: Text(
            text,
            style: ExerciseStyles.styleText2H14W500,
          ),
        ),
      ],
    );
  }
}
