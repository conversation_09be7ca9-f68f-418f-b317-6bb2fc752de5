import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player/video_player.dart';

import '../bloc/video/video_bloc.dart';
import '../bloc/video/video_event.dart';
import '../bloc/video/video_state.dart';
import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/exercise/exercise_state.dart';

/// Video view for video-based learning
class VideoView extends StatefulWidget {
  const VideoView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
  });

  final int subject;
  final String type;
  final int? sortId;

  @override
  State<VideoView> createState() => _VideoViewState();
}

class _VideoViewState extends State<VideoView> {
  VideoPlayerController? _videoController;
  bool _isControlsVisible = true;

  @override
  void initState() {
    super.initState();
    _loadVideosForCurrentQuestions();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  void _loadVideosForCurrentQuestions() {
    final exerciseState = context.read<ExerciseBloc>().state;
    if (exerciseState is ExerciseLoaded) {
      final questionIds = exerciseState.questions.map((q) => q.id).toList();
      context
          .read<VideoBloc>()
          .add(LoadVideosForQuestions(questionIds: questionIds));
    }
  }

  Future<void> _initializeVideoPlayer(String videoUrl, bool isLocal) async {
    await _videoController?.dispose();

    if (isLocal) {
      _videoController = VideoPlayerController.file(File(videoUrl));
    } else {
      _videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
    }

    await _videoController!.initialize();

    // Add listeners
    _videoController!.addListener(_onVideoPlayerUpdate);

    if (mounted) {
      setState(() {});

      // Auto-play
      _videoController!.play();
      context.read<VideoBloc>().add(VideoPlaybackStarted(
            video: context.read<VideoBloc>().state.currentVideo!,
          ));
    }
  }

  void _onVideoPlayerUpdate() {
    final controller = _videoController;
    if (controller == null) return;

    final videoBloc = context.read<VideoBloc>();
    final currentVideo = videoBloc.state.currentVideo;
    if (currentVideo == null) return;

    // Update position
    final position = controller.value.position.inSeconds;
    final duration = controller.value.duration.inSeconds;

    if (position > 0 && duration > 0) {
      videoBloc.add(UpdateVideoPosition(
        videoId: currentVideo.id,
        position: position,
        duration: duration,
      ));
    }

    // Handle completion
    if (controller.value.position >= controller.value.duration) {
      videoBloc.add(VideoPlaybackCompleted(video: currentVideo));
    }

    // Handle errors
    if (controller.value.hasError) {
      videoBloc.add(VideoPlaybackError(
        error: controller.value.errorDescription ?? 'Video playback error',
        video: currentVideo,
      ));
    }
  }

  void _togglePlayPause() {
    final controller = _videoController;
    if (controller == null) return;

    final videoBloc = context.read<VideoBloc>();
    final currentVideo = videoBloc.state.currentVideo;
    if (currentVideo == null) return;

    if (controller.value.isPlaying) {
      controller.pause();
      videoBloc.add(VideoPlaybackPaused(video: currentVideo));
    } else {
      controller.play();
      videoBloc.add(VideoPlaybackStarted(video: currentVideo));
    }
  }

  void _toggleControlsVisibility() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VideoBloc, VideoState>(
      listener: (context, state) {
        if (state.status == VideoPlaybackStatus.ready &&
            state.hasCurrentVideo) {
          _initializeVideoPlayer(state.currentVideoUrl!, state.isLocalVideo);
        } else if (state.status == VideoPlaybackStatus.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.error ?? 'Video error')),
          );
        }
      },
      child: BlocBuilder<VideoBloc, VideoState>(
        builder: (context, state) {
          return Column(
            children: [
              // Video list header
              _buildVideoListHeader(state),

              // Video player area
              Expanded(
                flex: 3,
                child: _buildVideoPlayer(state),
              ),

              // Video controls
              if (state.hasCurrentVideo) _buildVideoControls(state),

              // Video list
              Expanded(
                flex: 2,
                child: _buildVideoList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildVideoListHeader(VideoState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Icon(Icons.video_library, color: Colors.blue),
          const SizedBox(width: 8),
          Text(
            '视频列表 (${state.videos.length})',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          if (state.downloadStatus == VideoDownloadStatus.downloading)
            Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    value: state.downloadProgress,
                    strokeWidth: 2,
                  ),
                ),
                const SizedBox(width: 8),
                Text('${(state.downloadProgress * 100).toInt()}%'),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer(VideoState state) {
    if (state.status == VideoPlaybackStatus.loading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.status == VideoPlaybackStatus.error) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              state.error ?? 'Video error',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                if (state.currentVideo != null) {
                  context.read<VideoBloc>().add(
                        PlayVideoForQuestion(
                            questionId: state.currentVideo!.id),
                      );
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (!state.hasCurrentVideo || _videoController == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '请选择视频播放',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: _toggleControlsVisibility,
      child: Container(
        color: Colors.black,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Video player
            if (_videoController!.value.isInitialized)
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              )
            else
              const CircularProgressIndicator(),

            // Play/Pause overlay
            if (_isControlsVisible)
              Container(
                color: Colors.black26,
                child: Center(
                  child: IconButton(
                    onPressed: _togglePlayPause,
                    icon: Icon(
                      _videoController!.value.isPlaying
                          ? Icons.pause_circle_filled
                          : Icons.play_circle_filled,
                      size: 64,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoControls(VideoState state) {
    if (_videoController == null || !_videoController!.value.isInitialized) {
      return const SizedBox.shrink();
    }

    final position = _videoController!.value.position;
    final duration = _videoController!.value.duration;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Progress bar
          Row(
            children: [
              Text(
                _formatDuration(position),
                style: const TextStyle(fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: position.inMilliseconds.toDouble(),
                  max: duration.inMilliseconds.toDouble(),
                  onChanged: (value) {
                    _videoController!.seekTo(
                      Duration(milliseconds: value.toInt()),
                    );
                  },
                ),
              ),
              Text(
                _formatDuration(duration),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: state.canGoPrevious
                    ? () => context.read<VideoBloc>().add(
                          JumpToVideo(videoIndex: state.currentVideoIndex - 1),
                        )
                    : null,
                icon: const Icon(Icons.skip_previous),
              ),
              IconButton(
                onPressed: _togglePlayPause,
                icon: Icon(
                  _videoController!.value.isPlaying
                      ? Icons.pause
                      : Icons.play_arrow,
                ),
              ),
              IconButton(
                onPressed: state.canGoNext
                    ? () => context.read<VideoBloc>().add(
                          JumpToVideo(videoIndex: state.currentVideoIndex + 1),
                        )
                    : null,
                icon: const Icon(Icons.skip_next),
              ),
              IconButton(
                onPressed: () {
                  if (state.currentVideo != null) {
                    context.read<VideoBloc>().add(
                          DownloadVideo(video: state.currentVideo!),
                        );
                  }
                },
                icon: Icon(
                  state.isLocalVideo ? Icons.download_done : Icons.download,
                  color: state.isLocalVideo ? Colors.green : null,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVideoList(VideoState state) {
    if (state.videos.isEmpty) {
      return const Center(
        child: Text(
          '暂无视频',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      itemCount: state.videos.length,
      itemBuilder: (context, index) {
        final video = state.videos[index];
        final isSelected = index == state.currentVideoIndex;

        return ListTile(
          leading: Container(
            width: 60,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4),
            ),
            child: video.coverImageUrl.isNotEmpty
                ? Image.network(
                    video.coverImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.video_file, color: Colors.grey);
                    },
                  )
                : const Icon(Icons.video_file, color: Colors.grey),
          ),
          title: Text(
            video.title.isNotEmpty ? video.title : '视频 ${index + 1}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(video.formattedDuration),
          trailing: isSelected
              ? const Icon(Icons.play_circle_filled, color: Colors.blue)
              : null,
          selected: isSelected,
          onTap: () {
            context.read<VideoBloc>().add(JumpToVideo(videoIndex: index));
          },
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
