import 'package:equatable/equatable.dart';
import '../../../domain/entities/question_entity.dart';

/// Base class for browse states
abstract class BrowseState extends Equatable {
  const BrowseState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class BrowseInitial extends BrowseState {
  const BrowseInitial();
}

/// Loading state
class BrowseLoading extends BrowseState {
  const BrowseLoading();
}

/// Questions loaded successfully
class BrowseLoaded extends BrowseState {
  const BrowseLoaded({
    required this.questions,
    required this.filteredQuestions,
    required this.currentIndex,
    required this.subject,
    required this.type,
    this.showAnswers = true,
    this.searchKeyword = '',
    this.questionTypeFilter,
    this.showAnswered,
    this.showUnanswered,
    this.showCorrect,
    this.showIncorrect,
    this.canGoNext = true,
    this.canGoPrevious = true,
  });

  final List<QuestionEntity> questions;
  final List<QuestionEntity> filteredQuestions;
  final int currentIndex;
  final int subject;
  final String type;
  final bool showAnswers;
  final String searchKeyword;
  final String? questionTypeFilter;
  final bool? showAnswered;
  final bool? showUnanswered;
  final bool? showCorrect;
  final bool? showIncorrect;
  final bool canGoNext;
  final bool canGoPrevious;

  /// Get current question from filtered list
  QuestionEntity? get currentQuestion {
    if (currentIndex >= 0 && currentIndex < filteredQuestions.length) {
      return filteredQuestions[currentIndex];
    }
    return null;
  }

  /// Get total number of questions
  int get totalQuestions => questions.length;

  /// Get filtered questions count
  int get filteredCount => filteredQuestions.length;

  /// Check if filters are applied
  bool get hasFilters {
    return searchKeyword.isNotEmpty ||
           questionTypeFilter != null ||
           showAnswered != null ||
           showUnanswered != null ||
           showCorrect != null ||
           showIncorrect != null;
  }

  /// Get statistics
  Map<String, int> get statistics {
    final stats = <String, int>{
      'total': questions.length,
      'answered': questions.where((q) => q.isAnswered).length,
      'correct': questions.where((q) => q.isCorrect).length,
      'incorrect': questions.where((q) => q.isAnswered && !q.isCorrect).length,
      'collected': questions.where((q) => q.isCollected).length,
      'single': questions.where((q) => q.type == QuestionType.single).length,
      'multiple': questions.where((q) => q.type == QuestionType.multiple).length,
      'judge': questions.where((q) => q.type == QuestionType.judge).length,
    };
    return stats;
  }

  /// Create a copy with updated fields
  BrowseLoaded copyWith({
    List<QuestionEntity>? questions,
    List<QuestionEntity>? filteredQuestions,
    int? currentIndex,
    int? subject,
    String? type,
    bool? showAnswers,
    String? searchKeyword,
    String? questionTypeFilter,
    bool? showAnswered,
    bool? showUnanswered,
    bool? showCorrect,
    bool? showIncorrect,
    bool? canGoNext,
    bool? canGoPrevious,
  }) {
    return BrowseLoaded(
      questions: questions ?? this.questions,
      filteredQuestions: filteredQuestions ?? this.filteredQuestions,
      currentIndex: currentIndex ?? this.currentIndex,
      subject: subject ?? this.subject,
      type: type ?? this.type,
      showAnswers: showAnswers ?? this.showAnswers,
      searchKeyword: searchKeyword ?? this.searchKeyword,
      questionTypeFilter: questionTypeFilter ?? this.questionTypeFilter,
      showAnswered: showAnswered ?? this.showAnswered,
      showUnanswered: showUnanswered ?? this.showUnanswered,
      showCorrect: showCorrect ?? this.showCorrect,
      showIncorrect: showIncorrect ?? this.showIncorrect,
      canGoNext: canGoNext ?? this.canGoNext,
      canGoPrevious: canGoPrevious ?? this.canGoPrevious,
    );
  }

  @override
  List<Object?> get props => [
        questions,
        filteredQuestions,
        currentIndex,
        subject,
        type,
        showAnswers,
        searchKeyword,
        questionTypeFilter,
        showAnswered,
        showUnanswered,
        showCorrect,
        showIncorrect,
        canGoNext,
        canGoPrevious,
      ];
}

/// Error state
class BrowseError extends BrowseState {
  const BrowseError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// Question collection toggled
class BrowseCollectionToggled extends BrowseState {
  const BrowseCollectionToggled({
    required this.questions,
    required this.filteredQuestions,
    required this.currentIndex,
    required this.subject,
    required this.type,
    required this.isCollected,
    this.showAnswers = true,
  });

  final List<QuestionEntity> questions;
  final List<QuestionEntity> filteredQuestions;
  final int currentIndex;
  final int subject;
  final String type;
  final bool isCollected;
  final bool showAnswers;

  @override
  List<Object> get props => [
        questions,
        filteredQuestions,
        currentIndex,
        subject,
        type,
        isCollected,
        showAnswers,
      ];
}

/// Question deleted
class BrowseQuestionDeleted extends BrowseState {
  const BrowseQuestionDeleted({
    required this.questions,
    required this.filteredQuestions,
    required this.currentIndex,
    required this.subject,
    required this.type,
    this.showAnswers = true,
  });

  final List<QuestionEntity> questions;
  final List<QuestionEntity> filteredQuestions;
  final int currentIndex;
  final int subject;
  final String type;
  final bool showAnswers;

  @override
  List<Object> get props => [
        questions,
        filteredQuestions,
        currentIndex,
        subject,
        type,
        showAnswers,
      ];
}

/// Answer visibility toggled
class BrowseAnswerVisibilityToggled extends BrowseState {
  const BrowseAnswerVisibilityToggled({
    required this.questions,
    required this.filteredQuestions,
    required this.currentIndex,
    required this.subject,
    required this.type,
    required this.showAnswers,
  });

  final List<QuestionEntity> questions;
  final List<QuestionEntity> filteredQuestions;
  final int currentIndex;
  final int subject;
  final String type;
  final bool showAnswers;

  @override
  List<Object> get props => [
        questions,
        filteredQuestions,
        currentIndex,
        subject,
        type,
        showAnswers,
      ];
}

/// Questions filtered
class BrowseQuestionsFiltered extends BrowseState {
  const BrowseQuestionsFiltered({
    required this.questions,
    required this.filteredQuestions,
    required this.currentIndex,
    required this.subject,
    required this.type,
    this.showAnswers = true,
    this.searchKeyword = '',
    this.questionTypeFilter,
    this.showAnswered,
    this.showUnanswered,
    this.showCorrect,
    this.showIncorrect,
  });

  final List<QuestionEntity> questions;
  final List<QuestionEntity> filteredQuestions;
  final int currentIndex;
  final int subject;
  final String type;
  final bool showAnswers;
  final String searchKeyword;
  final String? questionTypeFilter;
  final bool? showAnswered;
  final bool? showUnanswered;
  final bool? showCorrect;
  final bool? showIncorrect;

  @override
  List<Object?> get props => [
        questions,
        filteredQuestions,
        currentIndex,
        subject,
        type,
        showAnswers,
        searchKeyword,
        questionTypeFilter,
        showAnswered,
        showUnanswered,
        showCorrect,
        showIncorrect,
      ];
}
