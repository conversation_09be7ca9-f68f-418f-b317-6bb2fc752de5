import 'package:equatable/equatable.dart';

/// Base class for browse events
abstract class Browse<PERSON>vent extends Equatable {
  const BrowseEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load questions for browsing
class LoadBrowseQuestions extends BrowseEvent {
  const LoadBrowseQuestions({
    required this.subject,
    required this.type,
    this.ids,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
  });

  final int subject;
  final String type;
  final List<int>? ids;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;

  @override
  List<Object?> get props => [
        subject,
        type,
        ids,
        sortId,
        mainId,
        childId,
        luid,
      ];
}

/// Event to jump to specific question
class JumpToBrowseQuestion extends BrowseEvent {
  const JumpToBrowseQuestion(this.index);

  final int index;

  @override
  List<Object> get props => [index];
}

/// Event to navigate to next question
class NextBrowseQuestion extends BrowseEvent {
  const NextBrowseQuestion();
}

/// Event to navigate to previous question
class PreviousBrowseQuestion extends BrowseEvent {
  const PreviousBrowseQuestion();
}

/// Event to toggle question collection
class ToggleBrowseCollection extends BrowseEvent {
  const ToggleBrowseCollection(this.questionIndex);

  final int questionIndex;

  @override
  List<Object> get props => [questionIndex];
}

/// Event to delete question from browse list
class DeleteBrowseQuestion extends BrowseEvent {
  const DeleteBrowseQuestion(this.questionIndex);

  final int questionIndex;

  @override
  List<Object> get props => [questionIndex];
}

/// Event to show/hide answer and explanation
class ToggleAnswerVisibility extends BrowseEvent {
  const ToggleAnswerVisibility();
}

/// Event to filter questions by type
class FilterQuestions extends BrowseEvent {
  const FilterQuestions({
    this.questionType,
    this.showAnswered,
    this.showUnanswered,
    this.showCorrect,
    this.showIncorrect,
  });

  final String? questionType; // 'single', 'multiple', 'judge'
  final bool? showAnswered;
  final bool? showUnanswered;
  final bool? showCorrect;
  final bool? showIncorrect;

  @override
  List<Object?> get props => [
        questionType,
        showAnswered,
        showUnanswered,
        showCorrect,
        showIncorrect,
      ];
}

/// Event to search questions by keyword
class SearchBrowseQuestions extends BrowseEvent {
  const SearchBrowseQuestions(this.keyword);

  final String keyword;

  @override
  List<Object> get props => [keyword];
}

/// Event to clear search
class ClearBrowseSearch extends BrowseEvent {
  const ClearBrowseSearch();
}

/// Event to reset browse state
class ResetBrowse extends BrowseEvent {
  const ResetBrowse();
}

/// Event to load error questions for browsing
class LoadBrowseErrorQuestions extends BrowseEvent {
  const LoadBrowseErrorQuestions(this.subject);

  final int subject;

  @override
  List<Object> get props => [subject];
}

/// Event to load collected questions for browsing
class LoadBrowseCollectedQuestions extends BrowseEvent {
  const LoadBrowseCollectedQuestions(this.subject);

  final int subject;

  @override
  List<Object> get props => [subject];
}
