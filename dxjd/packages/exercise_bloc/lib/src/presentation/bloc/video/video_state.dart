import 'package:equatable/equatable.dart';
import '../../../domain/entities/video_entity.dart';

/// Video playback status
enum VideoPlaybackStatus {
  initial,
  loading,
  ready,
  playing,
  paused,
  completed,
  error,
}

/// Video download status
enum VideoDownloadStatus {
  none,
  downloading,
  completed,
  error,
}

/// State for video BLoC
class VideoState extends Equatable {
  const VideoState({
    this.status = VideoPlaybackStatus.initial,
    this.videos = const [],
    this.currentVideoIndex = 0,
    this.currentVideo,
    this.currentVideoUrl,
    this.isLocalVideo = false,
    this.videoHistory = const [],
    this.downloadStatus = VideoDownloadStatus.none,
    this.downloadProgress = 0.0,
    this.error,
  });

  final VideoPlaybackStatus status;
  final List<VideoEntity> videos;
  final int currentVideoIndex;
  final VideoEntity? currentVideo;
  final String? currentVideoUrl;
  final bool isLocalVideo;
  final List<VideoEntity> videoHistory;
  final VideoDownloadStatus downloadStatus;
  final double downloadProgress;
  final String? error;

  /// Check if there are videos available
  bool get hasVideos => videos.isNotEmpty;

  /// Check if current video is valid
  bool get hasCurrentVideo => currentVideo != null && currentVideoUrl != null;

  /// Check if can go to previous video
  bool get canGoPrevious => currentVideoIndex > 0;

  /// Check if can go to next video
  bool get canGoNext => currentVideoIndex < videos.length - 1;

  /// Get current video or null
  VideoEntity? get getCurrentVideo {
    if (videos.isEmpty ||
        currentVideoIndex < 0 ||
        currentVideoIndex >= videos.length) {
      return null;
    }
    return videos[currentVideoIndex];
  }

  /// Create a copy with updated fields
  VideoState copyWith({
    VideoPlaybackStatus? status,
    List<VideoEntity>? videos,
    int? currentVideoIndex,
    VideoEntity? currentVideo,
    String? currentVideoUrl,
    bool? isLocalVideo,
    List<VideoEntity>? videoHistory,
    VideoDownloadStatus? downloadStatus,
    double? downloadProgress,
    String? error,
    bool clearCurrentVideo = false,
    bool clearCurrentVideoUrl = false,
  }) {
    return VideoState(
      status: status ?? this.status,
      videos: videos ?? this.videos,
      currentVideoIndex: currentVideoIndex ?? this.currentVideoIndex,
      currentVideo:
          clearCurrentVideo ? null : (currentVideo ?? this.currentVideo),
      currentVideoUrl: clearCurrentVideoUrl
          ? null
          : (currentVideoUrl ?? this.currentVideoUrl),
      isLocalVideo: isLocalVideo ?? this.isLocalVideo,
      videoHistory: videoHistory ?? this.videoHistory,
      downloadStatus: downloadStatus ?? this.downloadStatus,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      error: error,
    );
  }

  /// Create loading state
  VideoState copyWithLoading() {
    return copyWith(
      status: VideoPlaybackStatus.loading,
      error: null,
    );
  }

  /// Create error state
  VideoState copyWithError(String error) {
    return copyWith(
      status: VideoPlaybackStatus.error,
      error: error,
    );
  }

  /// Create ready state
  VideoState copyWithReady({
    required VideoEntity video,
    required String videoUrl,
    required bool isLocal,
  }) {
    return copyWith(
      status: VideoPlaybackStatus.ready,
      currentVideo: video,
      currentVideoUrl: videoUrl,
      isLocalVideo: isLocal,
      error: null,
    );
  }

  /// Create playing state
  VideoState copyWithPlaying() {
    return copyWith(
      status: VideoPlaybackStatus.playing,
      error: null,
    );
  }

  /// Create paused state
  VideoState copyWithPaused() {
    return copyWith(
      status: VideoPlaybackStatus.paused,
      error: null,
    );
  }

  /// Create completed state
  VideoState copyWithCompleted() {
    return copyWith(
      status: VideoPlaybackStatus.completed,
      error: null,
    );
  }

  @override
  List<Object?> get props => [
        status,
        videos,
        currentVideoIndex,
        currentVideo,
        currentVideoUrl,
        isLocalVideo,
        videoHistory,
        downloadStatus,
        downloadProgress,
        error,
      ];

  @override
  String toString() {
    return 'VideoState(status: $status, videosCount: ${videos.length}, '
        'currentIndex: $currentVideoIndex, hasCurrentVideo: $hasCurrentVideo, '
        'isLocal: $isLocalVideo, downloadStatus: $downloadStatus, '
        'downloadProgress: $downloadProgress, error: $error)';
  }
}
