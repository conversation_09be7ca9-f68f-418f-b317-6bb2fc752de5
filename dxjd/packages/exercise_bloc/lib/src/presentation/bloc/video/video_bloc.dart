import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/repositories/video_repository.dart';
import '../../../domain/usecases/play_video.dart';
import 'video_event.dart';
import 'video_state.dart';

/// BLoC for managing video playback
class VideoBloc extends Bloc<VideoEvent, VideoState> {
  VideoBloc({
    required VideoRepository videoRepository,
    required PlayVideo playVideo,
  }) : _videoRepository = videoRepository,
       _playVideo = playVideo,
       super(const VideoState()) {
    on<LoadVideosForQuestions>(_onLoadVideosForQuestions);
    on<PlayVideoForQuestion>(_onPlayVideoForQuestion);
    on<JumpToVideo>(_onJumpToVideo);
    on<UpdateVideoPosition>(_onUpdateVideoPosition);
    on<VideoPlaybackStarted>(_onVideoPlaybackStarted);
    on<VideoPlaybackPaused>(_onVideoPlaybackPaused);
    on<VideoPlaybackCompleted>(_onVideoPlaybackCompleted);
    on<VideoPlaybackError>(_onVideoPlaybackError);
    on<LoadVideoHistory>(_onLoadVideoHistory);
    on<ClearVideoCache>(_onClearVideoCache);
    on<DownloadVideo>(_onDownloadVideo);
    on<CancelVideoDownload>(_onCancelVideoDownload);
  }

  final VideoRepository _videoRepository;
  final PlayVideo _playVideo;

  /// Load videos for questions
  Future<void> _onLoadVideosForQuestions(
    LoadVideosForQuestions event,
    Emitter<VideoState> emit,
  ) async {
    try {
      emit(state.copyWithLoading());

      final videos = await _videoRepository.getVideosForQuestions(event.questionIds);

      emit(state.copyWith(
        status: VideoPlaybackStatus.initial,
        videos: videos,
        currentVideoIndex: 0,
        error: null,
        clearCurrentVideo: true,
        clearCurrentVideoUrl: true,
      ));
    } catch (e) {
      emit(state.copyWithError('Failed to load videos: ${e.toString()}'));
    }
  }

  /// Play video for a specific question
  Future<void> _onPlayVideoForQuestion(
    PlayVideoForQuestion event,
    Emitter<VideoState> emit,
  ) async {
    try {
      emit(state.copyWithLoading());

      final result = await _playVideo(PlayVideoParams(
        questionId: event.questionId,
        userId: event.userId,
        enableDownload: event.enableDownload,
      ));

      if (result.isSuccess) {
        emit(state.copyWithReady(
          video: result.video!,
          videoUrl: result.videoUrl!,
          isLocal: result.isLocal,
        ));
      } else {
        emit(state.copyWithError(result.error ?? 'Unknown error'));
      }
    } catch (e) {
      emit(state.copyWithError('Failed to play video: ${e.toString()}'));
    }
  }

  /// Jump to specific video in list
  Future<void> _onJumpToVideo(
    JumpToVideo event,
    Emitter<VideoState> emit,
  ) async {
    if (event.videoIndex < 0 || event.videoIndex >= state.videos.length) {
      emit(state.copyWithError('Invalid video index'));
      return;
    }

    try {
      emit(state.copyWith(
        currentVideoIndex: event.videoIndex,
        status: VideoPlaybackStatus.initial,
        clearCurrentVideo: true,
        clearCurrentVideoUrl: true,
      ));

      final video = state.videos[event.videoIndex];
      
      // Auto-play the selected video
      add(PlayVideoForQuestion(questionId: video.id));
    } catch (e) {
      emit(state.copyWithError('Failed to jump to video: ${e.toString()}'));
    }
  }

  /// Update video playback position
  Future<void> _onUpdateVideoPosition(
    UpdateVideoPosition event,
    Emitter<VideoState> emit,
  ) async {
    try {
      await _videoRepository.saveVideoProgress(
        videoId: event.videoId,
        currentPosition: event.position,
        duration: event.duration,
        userId: event.userId,
      );

      // Update current video with new position
      if (state.currentVideo?.id == event.videoId) {
        final updatedVideo = state.currentVideo!.copyWith(
          currentPosition: event.position,
          duration: event.duration,
        );

        emit(state.copyWith(currentVideo: updatedVideo));
      }
    } catch (e) {
      // Don't emit error for position updates, just log it
      // Position updates should not interrupt playback
    }
  }

  /// Handle video playback started
  Future<void> _onVideoPlaybackStarted(
    VideoPlaybackStarted event,
    Emitter<VideoState> emit,
  ) async {
    emit(state.copyWithPlaying());
  }

  /// Handle video playback paused
  Future<void> _onVideoPlaybackPaused(
    VideoPlaybackPaused event,
    Emitter<VideoState> emit,
  ) async {
    emit(state.copyWithPaused());
  }

  /// Handle video playback completed
  Future<void> _onVideoPlaybackCompleted(
    VideoPlaybackCompleted event,
    Emitter<VideoState> emit,
  ) async {
    emit(state.copyWithCompleted());

    // Auto-play next video if available
    if (state.canGoNext) {
      add(JumpToVideo(videoIndex: state.currentVideoIndex + 1));
    }
  }

  /// Handle video playback error
  Future<void> _onVideoPlaybackError(
    VideoPlaybackError event,
    Emitter<VideoState> emit,
  ) async {
    emit(state.copyWithError(event.error));
  }

  /// Load video history
  Future<void> _onLoadVideoHistory(
    LoadVideoHistory event,
    Emitter<VideoState> emit,
  ) async {
    try {
      final history = await _videoRepository.getVideoHistory(
        userId: event.userId,
        limit: event.limit,
      );

      emit(state.copyWith(videoHistory: history));
    } catch (e) {
      emit(state.copyWithError('Failed to load video history: ${e.toString()}'));
    }
  }

  /// Clear video cache
  Future<void> _onClearVideoCache(
    ClearVideoCache event,
    Emitter<VideoState> emit,
  ) async {
    try {
      await _videoRepository.clearVideoCache();
      
      // Reset download status
      emit(state.copyWith(
        downloadStatus: VideoDownloadStatus.none,
        downloadProgress: 0.0,
      ));
    } catch (e) {
      emit(state.copyWithError('Failed to clear video cache: ${e.toString()}'));
    }
  }

  /// Download video for offline viewing
  Future<void> _onDownloadVideo(
    DownloadVideo event,
    Emitter<VideoState> emit,
  ) async {
    try {
      emit(state.copyWith(
        downloadStatus: VideoDownloadStatus.downloading,
        downloadProgress: 0.0,
      ));

      // Get video URL first
      final videoUrl = await _videoRepository.getVideoUrl(event.video.aliyunVid);

      // Start download
      await _videoRepository.downloadVideo(event.video.aliyunVid, videoUrl);

      emit(state.copyWith(
        downloadStatus: VideoDownloadStatus.completed,
        downloadProgress: 1.0,
      ));
    } catch (e) {
      emit(state.copyWith(
        downloadStatus: VideoDownloadStatus.error,
        error: 'Failed to download video: ${e.toString()}',
      ));
    }
  }

  /// Cancel video download
  Future<void> _onCancelVideoDownload(
    CancelVideoDownload event,
    Emitter<VideoState> emit,
  ) async {
    try {
      await _videoRepository.cancelDownload(event.aliyunVid);

      emit(state.copyWith(
        downloadStatus: VideoDownloadStatus.none,
        downloadProgress: 0.0,
      ));
    } catch (e) {
      emit(state.copyWithError('Failed to cancel download: ${e.toString()}'));
    }
  }
}
