import 'package:equatable/equatable.dart';
import '../../../domain/entities/video_entity.dart';

/// Base class for video events
abstract class VideoEvent extends Equatable {
  const VideoEvent();

  @override
  List<Object?> get props => [];
}

/// Load videos for questions
class LoadVideosForQuestions extends VideoEvent {
  const LoadVideosForQuestions({
    required this.questionIds,
  });

  final List<int> questionIds;

  @override
  List<Object?> get props => [questionIds];
}

/// Play video for a specific question
class PlayVideoForQuestion extends VideoEvent {
  const PlayVideoForQuestion({
    required this.questionId,
    this.userId,
    this.enableDownload = true,
  });

  final int questionId;
  final String? userId;
  final bool enableDownload;

  @override
  List<Object?> get props => [questionId, userId, enableDownload];
}

/// Jump to specific video in list
class JumpToVideo extends VideoEvent {
  const JumpToVideo({
    required this.videoIndex,
  });

  final int videoIndex;

  @override
  List<Object?> get props => [videoIndex];
}

/// Update video playback position
class UpdateVideoPosition extends VideoEvent {
  const UpdateVideoPosition({
    required this.videoId,
    required this.position,
    required this.duration,
    this.userId,
  });

  final int videoId;
  final int position;
  final int duration;
  final String? userId;

  @override
  List<Object?> get props => [videoId, position, duration, userId];
}

/// Video playback started
class VideoPlaybackStarted extends VideoEvent {
  const VideoPlaybackStarted({
    required this.video,
  });

  final VideoEntity video;

  @override
  List<Object?> get props => [video];
}

/// Video playback paused
class VideoPlaybackPaused extends VideoEvent {
  const VideoPlaybackPaused({
    required this.video,
  });

  final VideoEntity video;

  @override
  List<Object?> get props => [video];
}

/// Video playback completed
class VideoPlaybackCompleted extends VideoEvent {
  const VideoPlaybackCompleted({
    required this.video,
  });

  final VideoEntity video;

  @override
  List<Object?> get props => [video];
}

/// Video playback error
class VideoPlaybackError extends VideoEvent {
  const VideoPlaybackError({
    required this.error,
    this.video,
  });

  final String error;
  final VideoEntity? video;

  @override
  List<Object?> get props => [error, video];
}

/// Load video history
class LoadVideoHistory extends VideoEvent {
  const LoadVideoHistory({
    this.userId,
    this.limit,
  });

  final String? userId;
  final int? limit;

  @override
  List<Object?> get props => [userId, limit];
}

/// Clear video cache
class ClearVideoCache extends VideoEvent {
  const ClearVideoCache();
}

/// Download video for offline viewing
class DownloadVideo extends VideoEvent {
  const DownloadVideo({
    required this.video,
  });

  final VideoEntity video;

  @override
  List<Object?> get props => [video];
}

/// Cancel video download
class CancelVideoDownload extends VideoEvent {
  const CancelVideoDownload({
    required this.aliyunVid,
  });

  final String aliyunVid;

  @override
  List<Object?> get props => [aliyunVid];
}
