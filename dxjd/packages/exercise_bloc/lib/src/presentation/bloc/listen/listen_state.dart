import 'package:equatable/equatable.dart';
import '../../../domain/entities/question_entity.dart';
import '../../../domain/entities/exercise_progress.dart';
import '../../../domain/repositories/audio_repository.dart';

/// Base class for listen states
abstract class ListenState extends Equatable {
  const ListenState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ListenInitial extends ListenState {
  const ListenInitial();
}

/// Loading state
class ListenLoading extends ListenState {
  const ListenLoading();
}

/// Questions loaded successfully
class ListenLoaded extends ListenState {
  const ListenLoaded({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.playerState,
    required this.currentPosition,
    required this.totalDuration,
    required this.playbackSpeed,
    this.showQuestionDetails = false,
    this.currentPlayingIndex = -1,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final AudioPlayerState playerState;
  final Duration currentPosition;
  final Duration totalDuration;
  final double playbackSpeed;
  final bool showQuestionDetails;
  final int currentPlayingIndex;

  /// Get current question
  QuestionEntity? get currentQuestion {
    if (currentIndex >= 0 && currentIndex < questions.length) {
      return questions[currentIndex];
    }
    return null;
  }

  /// Get currently playing question
  QuestionEntity? get playingQuestion {
    if (currentPlayingIndex >= 0 && currentPlayingIndex < questions.length) {
      return questions[currentPlayingIndex];
    }
    return null;
  }

  /// Check if audio is playing
  bool get isPlaying => playerState == AudioPlayerState.playing;

  /// Check if audio is paused
  bool get isPaused => playerState == AudioPlayerState.paused;

  /// Check if audio is stopped
  bool get isStopped => playerState == AudioPlayerState.stopped;

  /// Check if there's an error
  bool get hasError => playerState == AudioPlayerState.error;

  /// Get progress percentage
  double get progressPercentage {
    if (totalDuration.inMilliseconds == 0) return 0.0;
    return currentPosition.inMilliseconds / totalDuration.inMilliseconds;
  }

  /// Create a copy with updated fields
  ListenLoaded copyWith({
    List<QuestionEntity>? questions,
    int? currentIndex,
    ListenProgress? progress,
    AudioPlayerState? playerState,
    Duration? currentPosition,
    Duration? totalDuration,
    double? playbackSpeed,
    bool? showQuestionDetails,
    int? currentPlayingIndex,
  }) {
    return ListenLoaded(
      questions: questions ?? this.questions,
      currentIndex: currentIndex ?? this.currentIndex,
      progress: progress ?? this.progress,
      playerState: playerState ?? this.playerState,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      showQuestionDetails: showQuestionDetails ?? this.showQuestionDetails,
      currentPlayingIndex: currentPlayingIndex ?? this.currentPlayingIndex,
    );
  }

  @override
  List<Object?> get props => [
        questions,
        currentIndex,
        progress,
        playerState,
        currentPosition,
        totalDuration,
        playbackSpeed,
        showQuestionDetails,
        currentPlayingIndex,
      ];
}

/// Audio playing state
class AudioPlaying extends ListenState {
  const AudioPlaying({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.playingIndex,
    required this.currentPosition,
    required this.totalDuration,
    required this.playbackSpeed,
    this.showQuestionDetails = false,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final int playingIndex;
  final Duration currentPosition;
  final Duration totalDuration;
  final double playbackSpeed;
  final bool showQuestionDetails;

  /// Get currently playing question
  QuestionEntity? get playingQuestion {
    if (playingIndex >= 0 && playingIndex < questions.length) {
      return questions[playingIndex];
    }
    return null;
  }

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        playingIndex,
        currentPosition,
        totalDuration,
        playbackSpeed,
        showQuestionDetails,
      ];
}

/// Audio paused state
class AudioPaused extends ListenState {
  const AudioPaused({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.pausedIndex,
    required this.pausedPosition,
    required this.totalDuration,
    required this.playbackSpeed,
    this.showQuestionDetails = false,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final int pausedIndex;
  final Duration pausedPosition;
  final Duration totalDuration;
  final double playbackSpeed;
  final bool showQuestionDetails;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        pausedIndex,
        pausedPosition,
        totalDuration,
        playbackSpeed,
        showQuestionDetails,
      ];
}

/// Audio stopped state
class AudioStopped extends ListenState {
  const AudioStopped({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    this.showQuestionDetails = false,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final bool showQuestionDetails;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        showQuestionDetails,
      ];
}

/// Audio completed state (finished playing)
class AudioCompleted extends ListenState {
  const AudioCompleted({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.completedIndex,
    this.autoPlayNext = false,
    this.showQuestionDetails = false,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final int completedIndex;
  final bool autoPlayNext;
  final bool showQuestionDetails;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        completedIndex,
        autoPlayNext,
        showQuestionDetails,
      ];
}

/// Error state
class ListenError extends ListenState {
  const ListenError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// Question details toggled
class QuestionDetailsToggled extends ListenState {
  const QuestionDetailsToggled({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.playerState,
    required this.showQuestionDetails,
    required this.currentPosition,
    required this.totalDuration,
    required this.playbackSpeed,
    this.currentPlayingIndex = -1,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;
  final AudioPlayerState playerState;
  final bool showQuestionDetails;
  final Duration currentPosition;
  final Duration totalDuration;
  final double playbackSpeed;
  final int currentPlayingIndex;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        playerState,
        showQuestionDetails,
        currentPosition,
        totalDuration,
        playbackSpeed,
        currentPlayingIndex,
      ];
}

/// Question removed from list
class QuestionRemoved extends ListenState {
  const QuestionRemoved({
    required this.questions,
    required this.currentIndex,
    required this.progress,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ListenProgress progress;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
      ];
}
