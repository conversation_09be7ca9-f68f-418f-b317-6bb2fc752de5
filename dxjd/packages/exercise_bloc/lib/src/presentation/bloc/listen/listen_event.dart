import 'package:equatable/equatable.dart';

/// Base class for listen events
abstract class ListenEvent extends Equatable {
  const ListenEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load questions for listening
class LoadListenQuestions extends ListenEvent {
  const LoadListenQuestions({
    required this.subject,
    required this.type,
    this.ids,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
  });

  final int subject;
  final String type;
  final List<int>? ids;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;

  @override
  List<Object?> get props => [
        subject,
        type,
        ids,
        sortId,
        mainId,
        childId,
        luid,
      ];
}

/// Event to play audio for a specific question
class PlayQuestionAudio extends ListenEvent {
  const PlayQuestionAudio({
    required this.questionIndex,
    this.autoPlay = false,
  });

  final int questionIndex;
  final bool autoPlay;

  @override
  List<Object> get props => [questionIndex, autoPlay];
}

/// Event to pause current audio
class PauseAudio extends ListenEvent {
  const PauseAudio();
}

/// Event to resume paused audio
class ResumeAudio extends ListenEvent {
  const ResumeAudio();
}

/// Event to stop current audio
class StopAudio extends ListenEvent {
  const StopAudio();
}

/// Event to seek to specific position
class SeekAudio extends ListenEvent {
  const SeekAudio(this.position);

  final Duration position;

  @override
  List<Object> get props => [position];
}

/// Event to jump to specific question
class JumpToListenQuestion extends ListenEvent {
  const JumpToListenQuestion(this.index);

  final int index;

  @override
  List<Object> get props => [index];
}

/// Event to toggle question details visibility
class ToggleQuestionDetails extends ListenEvent {
  const ToggleQuestionDetails();
}

/// Event to set playback speed
class SetPlaybackSpeed extends ListenEvent {
  const SetPlaybackSpeed(this.speed);

  final double speed;

  @override
  List<Object> get props => [speed];
}

/// Event to handle audio completion
class AudioCompleted extends ListenEvent {
  const AudioCompleted();
}

/// Event to handle audio error
class AudioError extends ListenEvent {
  const AudioError(this.error);

  final String error;

  @override
  List<Object> get props => [error];
}

/// Event to update audio position
class AudioPositionChanged extends ListenEvent {
  const AudioPositionChanged({
    required this.position,
    required this.duration,
  });

  final Duration position;
  final Duration duration;

  @override
  List<Object> get props => [position, duration];
}

/// Event to handle route changes (pause on navigation)
class RouteChanged extends ListenEvent {
  const RouteChanged({
    required this.isPushed,
  });

  final bool isPushed;

  @override
  List<Object> get props => [isPushed];
}

/// Event to configure audio session
class ConfigureAudioSession extends ListenEvent {
  const ConfigureAudioSession({
    this.enableBackgroundPlayback = true,
    this.mixWithOthers = false,
  });

  final bool enableBackgroundPlayback;
  final bool mixWithOthers;

  @override
  List<Object> get props => [enableBackgroundPlayback, mixWithOthers];
}

/// Event to remove question from listen list
class RemoveListenQuestion extends ListenEvent {
  const RemoveListenQuestion(this.questionIndex);

  final int questionIndex;

  @override
  List<Object> get props => [questionIndex];
}
