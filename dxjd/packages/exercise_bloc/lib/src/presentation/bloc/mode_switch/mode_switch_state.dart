import 'package:equatable/equatable.dart';
import 'mode_switch_event.dart';

/// Base class for mode switch states
abstract class ModeSwitchState extends Equatable {
  const ModeSwitchState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ModeSwitchInitial extends ModeSwitchState {
  const ModeSwitchInitial();
}

/// Exercise mode active
class ExerciseModeActive extends ModeSwitchState {
  const ExerciseModeActive({
    this.exerciseType,
    this.canSwitchModes = true,
  });

  final String? exerciseType;
  final bool canSwitchModes;

  @override
  List<Object?> get props => [exerciseType, canSwitchModes];
}

/// Listen mode active
class ListenModeActive extends ModeSwitchState {
  const ListenModeActive({
    this.canSwitchModes = true,
  });

  final bool canSwitchModes;

  @override
  List<Object> get props => [canSwitchModes];
}

/// Browse mode active
class BrowseModeActive extends ModeSwitchState {
  const BrowseModeActive({
    this.canSwitchModes = true,
  });

  final bool canSwitchModes;

  @override
  List<Object> get props => [canSwitchModes];
}

/// Video mode active
class VideoModeActive extends ModeSwitchState {
  const VideoModeActive({
    this.canSwitchModes = true,
  });

  final bool canSwitchModes;

  @override
  List<Object> get props => [canSwitchModes];
}

/// Mode switch confirmation required
class ModeSwitchConfirmationRequired extends ModeSwitchState {
  const ModeSwitchConfirmationRequired({
    required this.currentMode,
    required this.targetMode,
    required this.reason,
  });

  final ExerciseMode currentMode;
  final ExerciseMode targetMode;
  final String reason;

  @override
  List<Object> get props => [currentMode, targetMode, reason];
}

/// Mode switching in progress
class ModeSwitching extends ModeSwitchState {
  const ModeSwitching({
    required this.fromMode,
    required this.toMode,
  });

  final ExerciseMode fromMode;
  final ExerciseMode toMode;

  @override
  List<Object> get props => [fromMode, toMode];
}

/// Mode switch error
class ModeSwitchError extends ModeSwitchState {
  const ModeSwitchError({
    required this.message,
    required this.currentMode,
  });

  final String message;
  final ExerciseMode currentMode;

  @override
  List<Object> get props => [message, currentMode];
}

/// Extension to get current mode from state
extension ModeSwitchStateExtension on ModeSwitchState {
  ExerciseMode get currentMode {
    if (this is ExerciseModeActive) {
      return ExerciseMode.exercise;
    } else if (this is ListenModeActive) {
      return ExerciseMode.listen;
    } else if (this is BrowseModeActive) {
      return ExerciseMode.browse;
    } else if (this is VideoModeActive) {
      return ExerciseMode.video;
    } else if (this is ModeSwitchConfirmationRequired) {
      return (this as ModeSwitchConfirmationRequired).currentMode;
    } else if (this is ModeSwitching) {
      return (this as ModeSwitching).fromMode;
    } else if (this is ModeSwitchError) {
      return (this as ModeSwitchError).currentMode;
    }
    return ExerciseMode.exercise; // Default
  }

  bool get canSwitchModes {
    if (this is ExerciseModeActive) {
      return (this as ExerciseModeActive).canSwitchModes;
    } else if (this is ListenModeActive) {
      return (this as ListenModeActive).canSwitchModes;
    } else if (this is BrowseModeActive) {
      return (this as BrowseModeActive).canSwitchModes;
    } else if (this is VideoModeActive) {
      return (this as VideoModeActive).canSwitchModes;
    }
    return true; // Default
  }

  bool get isActive {
    return this is ExerciseModeActive ||
           this is ListenModeActive ||
           this is BrowseModeActive ||
           this is VideoModeActive;
  }

  bool get isConfirmationRequired {
    return this is ModeSwitchConfirmationRequired;
  }

  bool get isSwitching {
    return this is ModeSwitching;
  }

  bool get hasError {
    return this is ModeSwitchError;
  }
}
