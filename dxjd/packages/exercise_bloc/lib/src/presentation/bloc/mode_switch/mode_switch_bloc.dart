import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'mode_switch_event.dart';
import 'mode_switch_state.dart';

/// BLoC for managing mode switching
class ModeSwitchBloc extends Bloc<ModeSwitchEvent, ModeSwitchState> {
  ModeSwitchBloc() : super(const ModeSwitchInitial()) {
    on<InitializeMode>(_onInitializeMode);
    on<SwitchToExercise>(_onSwitchToExercise);
    on<SwitchToListen>(_onSwitchToListen);
    on<SwitchToBrowse>(_onSwitchToBrowse);
    on<SwitchToVideo>(_onSwitchToVideo);
    on<ConfirmModeSwitch>(_onConfirmModeSwitch);
    on<CancelModeSwitch>(_onCancelModeSwitch);
    on<DisableModeSwitch>(_onDisableModeSwitch);
    on<EnableModeSwitch>(_onEnableModeSwitch);
  }

  /// Handle initialize mode
  void _onInitializeMode(
    InitializeMode event,
    Emitter<ModeSwitchState> emit,
  ) {
    switch (event.initialMode) {
      case ExerciseMode.exercise:
        emit(ExerciseModeActive(
          exerciseType: event.exerciseType,
          canSwitchModes: true,
        ));
        break;
      case ExerciseMode.listen:
        emit(const ListenModeActive(canSwitchModes: true));
        break;
      case ExerciseMode.browse:
        emit(const BrowseModeActive(canSwitchModes: true));
        break;
      case ExerciseMode.video:
        emit(const VideoModeActive(canSwitchModes: true));
        break;
    }
  }

  /// Handle switch to exercise mode
  void _onSwitchToExercise(
    SwitchToExercise event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    // If already in exercise mode, do nothing
    if (currentState is ExerciseModeActive) return;

    // Check if confirmation is required
    if (event.confirmationRequired && _requiresConfirmation(currentState)) {
      emit(ModeSwitchConfirmationRequired(
        currentMode: currentState.currentMode,
        targetMode: ExerciseMode.exercise,
        reason: event.reason ??
            'Switch to exercise mode will stop current activity',
      ));
      return;
    }

    // Perform the switch
    _performModeSwitch(
      currentState,
      ExerciseMode.exercise,
      emit,
    );
  }

  /// Handle switch to listen mode
  void _onSwitchToListen(
    SwitchToListen event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    // If already in listen mode, do nothing
    if (currentState is ListenModeActive) return;

    // Check if confirmation is required
    if (event.confirmationRequired && _requiresConfirmation(currentState)) {
      emit(ModeSwitchConfirmationRequired(
        currentMode: currentState.currentMode,
        targetMode: ExerciseMode.listen,
        reason:
            event.reason ?? 'Switch to listen mode will stop current activity',
      ));
      return;
    }

    // Perform the switch
    _performModeSwitch(
      currentState,
      ExerciseMode.listen,
      emit,
    );
  }

  /// Handle switch to browse mode
  void _onSwitchToBrowse(
    SwitchToBrowse event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    // If already in browse mode, do nothing
    if (currentState is BrowseModeActive) return;

    // Check if confirmation is required
    if (event.confirmationRequired && _requiresConfirmation(currentState)) {
      emit(ModeSwitchConfirmationRequired(
        currentMode: currentState.currentMode,
        targetMode: ExerciseMode.browse,
        reason:
            event.reason ?? 'Switch to browse mode will stop current activity',
      ));
      return;
    }

    // Perform the switch
    _performModeSwitch(
      currentState,
      ExerciseMode.browse,
      emit,
    );
  }

  /// Handle switch to video mode
  void _onSwitchToVideo(
    SwitchToVideo event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    // If already in video mode, do nothing
    if (currentState is VideoModeActive) return;

    // Video mode switch is always immediate (no confirmation needed)
    _performModeSwitch(
      currentState,
      ExerciseMode.video,
      emit,
    );
  }

  /// Handle confirm mode switch
  void _onConfirmModeSwitch(
    ConfirmModeSwitch event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    if (currentState is! ModeSwitchConfirmationRequired) return;

    // Perform the confirmed switch
    _performModeSwitch(
      currentState,
      event.targetMode,
      emit,
    );
  }

  /// Handle cancel mode switch
  void _onCancelModeSwitch(
    CancelModeSwitch event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    if (currentState is! ModeSwitchConfirmationRequired) return;

    // Return to the current mode
    switch (currentState.currentMode) {
      case ExerciseMode.exercise:
        emit(const ExerciseModeActive(canSwitchModes: true));
        break;
      case ExerciseMode.listen:
        emit(const ListenModeActive(canSwitchModes: true));
        break;
      case ExerciseMode.browse:
        emit(const BrowseModeActive(canSwitchModes: true));
        break;
      case ExerciseMode.video:
        emit(const VideoModeActive(canSwitchModes: true));
        break;
    }
  }

  /// Check if mode switch requires confirmation
  bool _requiresConfirmation(ModeSwitchState currentState) {
    // Listen mode requires confirmation if audio is playing
    if (currentState is ListenModeActive) {
      return true; // Assume audio might be playing
    }

    // Exercise mode requires confirmation if there's progress
    if (currentState is ExerciseModeActive) {
      return true; // Assume there might be unsaved progress
    }

    return false;
  }

  /// Perform the actual mode switch
  void _performModeSwitch(
    ModeSwitchState currentState,
    ExerciseMode targetMode,
    Emitter<ModeSwitchState> emit,
  ) {
    // Emit switching state
    emit(ModeSwitching(
      fromMode: currentState.currentMode,
      toMode: targetMode,
    ));

    // Simulate async operation (cleanup, initialization, etc.)
    Timer(const Duration(milliseconds: 300), () {
      switch (targetMode) {
        case ExerciseMode.exercise:
          emit(const ExerciseModeActive(canSwitchModes: true));
          break;
        case ExerciseMode.listen:
          emit(const ListenModeActive(canSwitchModes: true));
          break;
        case ExerciseMode.browse:
          emit(const BrowseModeActive(canSwitchModes: true));
          break;
        case ExerciseMode.video:
          emit(const VideoModeActive(canSwitchModes: true));
          break;
      }
    });
  }

  /// Disable mode switching (e.g., during exam)
  void disableModeSwitch() {
    add(const DisableModeSwitch());
  }

  /// Enable mode switching
  void enableModeSwitch() {
    add(const EnableModeSwitch());
  }

  /// Handle disable mode switch event
  void _onDisableModeSwitch(
    DisableModeSwitch event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    if (currentState is ExerciseModeActive) {
      emit(currentState.copyWith(canSwitchModes: false));
    } else if (currentState is ListenModeActive) {
      emit(const ListenModeActive(canSwitchModes: false));
    } else if (currentState is BrowseModeActive) {
      emit(const BrowseModeActive(canSwitchModes: false));
    } else if (currentState is VideoModeActive) {
      emit(const VideoModeActive(canSwitchModes: false));
    }
  }

  /// Handle enable mode switch event
  void _onEnableModeSwitch(
    EnableModeSwitch event,
    Emitter<ModeSwitchState> emit,
  ) {
    final currentState = state;

    if (currentState is ExerciseModeActive) {
      emit(currentState.copyWith(canSwitchModes: true));
    } else if (currentState is ListenModeActive) {
      emit(const ListenModeActive(canSwitchModes: true));
    } else if (currentState is BrowseModeActive) {
      emit(const BrowseModeActive(canSwitchModes: true));
    } else if (currentState is VideoModeActive) {
      emit(const VideoModeActive(canSwitchModes: true));
    }
  }
}

/// Extension for ExerciseModeActive to support copyWith
extension ExerciseModeActiveExtension on ExerciseModeActive {
  ExerciseModeActive copyWith({
    String? exerciseType,
    bool? canSwitchModes,
  }) {
    return ExerciseModeActive(
      exerciseType: exerciseType ?? this.exerciseType,
      canSwitchModes: canSwitchModes ?? this.canSwitchModes,
    );
  }
}
