import 'package:equatable/equatable.dart';
import '../../../domain/entities/question_entity.dart';
import '../../../domain/entities/exercise_progress.dart';

/// Base class for exercise states
abstract class ExerciseState extends Equatable {
  const ExerciseState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ExerciseInitial extends ExerciseState {
  const ExerciseInitial();
}

/// Loading state
class ExerciseLoading extends ExerciseState {
  const ExerciseLoading();
}

/// Questions loaded successfully
class ExerciseLoaded extends ExerciseState {
  const ExerciseLoaded({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    this.canGoNext = true,
    this.canGoPrevious = true,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ExerciseProgress progress;
  final bool canGoNext;
  final bool canGoPrevious;

  /// Get current question
  QuestionEntity? get currentQuestion {
    if (currentIndex >= 0 && currentIndex < questions.length) {
      return questions[currentIndex];
    }
    return null;
  }

  /// Check if all questions are answered
  bool get isCompleted {
    return progress.isCompleted;
  }

  /// Create a copy with updated fields
  ExerciseLoaded copyWith({
    List<QuestionEntity>? questions,
    int? currentIndex,
    ExerciseProgress? progress,
    bool? canGoNext,
    bool? canGoPrevious,
  }) {
    return ExerciseLoaded(
      questions: questions ?? this.questions,
      currentIndex: currentIndex ?? this.currentIndex,
      progress: progress ?? this.progress,
      canGoNext: canGoNext ?? this.canGoNext,
      canGoPrevious: canGoPrevious ?? this.canGoPrevious,
    );
  }

  @override
  List<Object?> get props => [
        questions,
        currentIndex,
        progress,
        canGoNext,
        canGoPrevious,
      ];
}

/// Question answered state
class QuestionAnswered extends ExerciseState {
  const QuestionAnswered({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.isCorrect,
    required this.correctAnswer,
    this.explanation,
    this.skills,
    this.autoAdvance = false,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ExerciseProgress progress;
  final bool isCorrect;
  final String correctAnswer;
  final String? explanation;
  final String? skills;
  final bool autoAdvance;

  /// Get current question
  QuestionEntity? get currentQuestion {
    if (currentIndex >= 0 && currentIndex < questions.length) {
      return questions[currentIndex];
    }
    return null;
  }

  @override
  List<Object?> get props => [
        questions,
        currentIndex,
        progress,
        isCorrect,
        correctAnswer,
        explanation,
        skills,
        autoAdvance,
      ];
}

/// Exercise completed state
class ExerciseCompleted extends ExerciseState {
  const ExerciseCompleted({
    required this.progress,
    required this.questions,
  });

  final ExerciseProgress progress;
  final List<QuestionEntity> questions;

  @override
  List<Object> get props => [progress, questions];
}

/// Error state
class ExerciseError extends ExerciseState {
  const ExerciseError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// Question collection toggled
class QuestionCollectionToggled extends ExerciseState {
  const QuestionCollectionToggled({
    required this.questions,
    required this.currentIndex,
    required this.progress,
    required this.isCollected,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ExerciseProgress progress;
  final bool isCollected;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
        isCollected,
      ];
}

/// Question deleted
class QuestionDeleted extends ExerciseState {
  const QuestionDeleted({
    required this.questions,
    required this.currentIndex,
    required this.progress,
  });

  final List<QuestionEntity> questions;
  final int currentIndex;
  final ExerciseProgress progress;

  @override
  List<Object> get props => [
        questions,
        currentIndex,
        progress,
      ];
}
