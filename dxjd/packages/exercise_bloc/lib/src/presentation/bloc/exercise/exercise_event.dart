import 'package:equatable/equatable.dart';

/// Base class for exercise events
abstract class ExerciseEvent extends Equatable {
  const ExerciseEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load questions
class LoadQuestions extends ExerciseEvent {
  const LoadQuestions({
    required this.subject,
    required this.type,
    this.ids,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
  });

  final int subject;
  final String type;
  final List<int>? ids;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;

  @override
  List<Object?> get props => [
        subject,
        type,
        ids,
        sortId,
        mainId,
        childId,
        luid,
      ];
}

/// Event to answer a question
class AnswerQuestion extends ExerciseEvent {
  const AnswerQuestion({
    required this.questionIndex,
    required this.answer,
  });

  final int questionIndex;
  final String answer;

  @override
  List<Object> get props => [questionIndex, answer];
}

/// Event to navigate to next question
class NextQuestion extends ExerciseEvent {
  const NextQuestion();
}

/// Event to navigate to previous question
class PreviousQuestion extends ExerciseEvent {
  const PreviousQuestion();
}

/// Event to jump to specific question
class JumpToQuestion extends ExerciseEvent {
  const JumpToQuestion(this.index);

  final int index;

  @override
  List<Object> get props => [index];
}

/// Event to toggle question collection
class ToggleCollection extends ExerciseEvent {
  const ToggleCollection(this.questionIndex);

  final int questionIndex;

  @override
  List<Object> get props => [questionIndex];
}

/// Event to delete question from error/collection list
class DeleteQuestion extends ExerciseEvent {
  const DeleteQuestion(this.questionIndex);

  final int questionIndex;

  @override
  List<Object> get props => [questionIndex];
}

/// Event to submit exercise (for exam mode)
class SubmitExercise extends ExerciseEvent {
  const SubmitExercise();
}

/// Event to reset exercise
class ResetExercise extends ExerciseEvent {
  const ResetExercise();
}

/// Event to load error questions
class LoadErrorQuestions extends ExerciseEvent {
  const LoadErrorQuestions(this.subject);

  final int subject;

  @override
  List<Object> get props => [subject];
}

/// Event to load collected questions
class LoadCollectedQuestions extends ExerciseEvent {
  const LoadCollectedQuestions(this.subject);

  final int subject;

  @override
  List<Object> get props => [subject];
}
