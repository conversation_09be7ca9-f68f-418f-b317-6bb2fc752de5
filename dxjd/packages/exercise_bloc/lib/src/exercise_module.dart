import 'package:flutter/foundation.dart';
import 'injection/injection.dart';

/// Exercise module initialization and configuration
class ExerciseModule {
  static bool _isInitialized = false;

  /// Initialize the exercise module
  /// This should be called once during app startup
  static Future<void> initialize({
    bool enableDebugMode = kDebugMode,
    String? baseUrl,
    String? databasePath,
  }) async {
    if (_isInitialized) {
      debugPrint('ExerciseModule: Already initialized');
      return;
    }

    debugPrint('ExerciseModule: Initializing...');

    try {
      // Setup dependency injection
      setupDependencies(
        baseUrl: baseUrl,
        databasePath: databasePath,
      );

      // Initialize data sources
      await initializeDataSources();

      // Configure debug settings
      if (enableDebugMode) {
        _configureDebugMode();
      }

      _isInitialized = true;
      debugPrint('ExerciseModule: Initialization completed successfully');
    } catch (e) {
      debugPrint('ExerciseModule: Initialization failed - $e');
      rethrow;
    }
  }

  /// Check if module is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset module (for testing purposes)
  @visibleForTesting
  static void reset() {
    _isInitialized = false;
    resetDependencies();
  }

  /// Configure debug mode settings
  static void _configureDebugMode() {
    debugPrint('ExerciseModule: Debug mode enabled');

    // Add debug configurations here
    // For example: logging, debug overlays, etc.
  }

  /// Get module version
  static String get version => '0.0.1';

  /// Get module information
  static Map<String, dynamic> get info => {
        'name': 'Exercise BLoC',
        'version': version,
        'description': 'Modern exercise module with BLoC architecture',
        'initialized': _isInitialized,
        'dependencies': [
          'flutter_bloc',
          'get_it',
          'injectable',
          'audioplayers',
          'cached_network_image',
          'dio',
          'sqflite',
        ],
      };
}
