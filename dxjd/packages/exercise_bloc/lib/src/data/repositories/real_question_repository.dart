import 'package:quiz/quiz.dart';
import '../../domain/entities/question_entity.dart';
import '../../domain/repositories/question_repository.dart';

/// 真实数据源的题目Repository实现
/// 集成现有的quiz包数据库查询逻辑
class RealQuestionRepository implements QuestionRepository {
  @override
  Future<List<QuestionEntity>> getQuestions({
    required int subject,
    required String type,
    int? sortId,
    int? limit,
    int? offset,
  }) async {
    try {
      List<QuizMo> quizList = [];
      
      // 根据不同类型调用相应的查询方法
      switch (type) {
        case 'chapter':
          // 顺序练习 - 章节题目
          if (sortId != null) {
            quizList = await QuizUtils.get().getChapterQuiz(
              sortId,
              _getQuizType(subject),
              [], // cityid - 可以从配置获取
              '', // uid - 可以从用户信息获取
              true, // status
              'chapter',
            );
          }
          break;
          
        case 'random':
          // 随机练习
          quizList = await QuizUtils.get().getRandomQuiz(
            subject,
            _getQuizType(subject),
            [], // cityid
            '', // uid
            true, // status
            'random',
          );
          break;
          
        case 'error':
          // 错题练习
          quizList = await QuizUtils.get().getErrorQuiz(
            subject,
            _getQuizType(subject),
            [], // cityid
            '', // uid
            true, // status
            'error',
          );
          break;
          
        case 'mock':
          // 模拟考试
          quizList = await QuizUtils.get().getMockQuiz(
            subject,
            _getQuizType(subject),
            [], // cityid
            '', // uid
            true, // status
            'mock',
          );
          break;
          
        default:
          // 默认获取章节题目
          if (sortId != null) {
            quizList = await QuizUtils.get().getChapterQuiz(
              sortId,
              _getQuizType(subject),
              [],
              '',
              true,
              'chapter',
            );
          }
      }
      
      // 应用分页
      if (offset != null && limit != null) {
        final startIndex = offset;
        final endIndex = (startIndex + limit).clamp(0, quizList.length);
        quizList = quizList.sublist(startIndex, endIndex);
      } else if (limit != null) {
        quizList = quizList.take(limit).toList();
      }
      
      // 转换为QuestionEntity
      return quizList.map((quiz) => _convertToQuestionEntity(quiz)).toList();
    } catch (e) {
      throw Exception('Failed to load questions: $e');
    }
  }

  @override
  Future<void> saveUserAnswer({
    required int questionId,
    required String answer,
    required bool isCorrect,
    String? userId,
  }) async {
    try {
      // 保存用户答题记录
      final exerciseMo = ExerciseMo(
        DateTime.now().millisecondsSinceEpoch,
        questionId,
        0, // sortId - 需要从题目信息获取
        1, // examType - 根据实际情况设置
        answer,
        isCorrect ? 1 : 0,
        DateTime.now().toString(),
        userId ?? '',
      );
      
      await ExerciseDao.insertOrUpdate(exerciseMo);
    } catch (e) {
      throw Exception('Failed to save user answer: $e');
    }
  }

  @override
  Future<List<QuestionEntity>> getErrorQuestions({
    String? userId,
    int? limit,
  }) async {
    try {
      final quizList = await QuizUtils.get().getErrorQuiz(
        1, // subject - 默认科目一
        '0', // type
        [], // cityid
        userId ?? '',
        true,
        'error',
      );
      
      final limitedList = limit != null ? quizList.take(limit).toList() : quizList;
      return limitedList.map((quiz) => _convertToQuestionEntity(quiz)).toList();
    } catch (e) {
      throw Exception('Failed to load error questions: $e');
    }
  }

  @override
  Future<List<QuestionEntity>> getFavoriteQuestions({
    String? userId,
    int? limit,
  }) async {
    try {
      final quizList = await QuizUtils.get().getCollectQuiz(
        1, // subject
        '0', // type
        [], // cityid
        userId ?? '',
        true,
        'collect',
      );
      
      final limitedList = limit != null ? quizList.take(limit).toList() : quizList;
      return limitedList.map((quiz) => _convertToQuestionEntity(quiz)).toList();
    } catch (e) {
      throw Exception('Failed to load favorite questions: $e');
    }
  }

  @override
  Future<void> syncWithServer() async {
    try {
      // 同步逻辑 - 可以调用现有的同步方法
      await IQuiz.get().setRecord();
    } catch (e) {
      throw Exception('Failed to sync with server: $e');
    }
  }

  /// 根据科目获取题目类型字符串
  String _getQuizType(int subject) {
    switch (subject) {
      case 1:
        return '0'; // 科目一
      case 4:
        return '0'; // 科目四
      default:
        return '0';
    }
  }

  /// 将QuizMo转换为QuestionEntity
  QuestionEntity _convertToQuestionEntity(QuizMo quiz) {
    // 构建选项列表
    final options = <QuestionOption>[];
    if (quiz.optA.isNotEmpty) {
      options.add(QuestionOption(key: 'A', text: quiz.optA));
    }
    if (quiz.optB.isNotEmpty) {
      options.add(QuestionOption(key: 'B', text: quiz.optB));
    }
    if (quiz.optC.isNotEmpty) {
      options.add(QuestionOption(key: 'C', text: quiz.optC));
    }
    if (quiz.optD.isNotEmpty) {
      options.add(QuestionOption(key: 'D', text: quiz.optD));
    }

    // 判断题目类型
    QuestionType questionType = QuestionType.single;
    if (options.length == 2 && 
        (quiz.sub_type == '1' || quiz.sub_type == 'true_false')) {
      questionType = QuestionType.trueFalse;
    } else if (options.length > 2) {
      questionType = QuestionType.single;
    }

    // 判断难度
    QuestionDifficulty difficulty = QuestionDifficulty.medium;
    if (quiz.easy_error == 1) {
      difficulty = QuestionDifficulty.easy;
    } else if (quiz.errorNumber > 5) {
      difficulty = QuestionDifficulty.hard;
    }

    return QuestionEntity(
      id: quiz.sub_Id,
      subId: quiz.sub_Id,
      question: quiz.sub_Titles,
      options: options,
      correctAnswer: quiz.answer,
      userAnswer: quiz.selection.isNotEmpty ? quiz.selection : null,
      explanation: quiz.infos,
      imagePath: quiz.sub_pic.isNotEmpty ? quiz.sub_pic : null,
      audioUrl: quiz.jqtp.isNotEmpty ? quiz.jqtp : null,
      videoUrl: quiz.dtjq.isNotEmpty ? quiz.dtjq : null,
      difficulty: difficulty,
      tags: [quiz.exam_type, quiz.sub_type].where((tag) => tag.isNotEmpty).toList(),
      isMarked: quiz.collect == 1,
      isAnswered: quiz.selection.isNotEmpty,
      answerTime: 0, // 可以从其他地方获取
      type: questionType,
      sortId: quiz.sort_Id,
    );
  }
}
