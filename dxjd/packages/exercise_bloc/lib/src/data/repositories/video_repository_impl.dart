import 'dart:async';
import '../../domain/entities/video_entity.dart';
import '../../domain/repositories/video_repository.dart';
import '../datasources/video_datasource.dart';

/// Implementation of VideoRepository
class VideoRepositoryImpl implements VideoRepository {
  VideoRepositoryImpl({
    required VideoDataSource remoteDataSource,
    required VideoDataSource localDataSource,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource;

  final VideoDataSource _remoteDataSource;
  final VideoDataSource _localDataSource;

  // Download progress controllers
  final Map<String, StreamController<double>> _downloadControllers = {};

  @override
  Future<List<VideoEntity>> getVideosForQuestions(List<int> questionIds) async {
    try {
      // Try remote first
      final remoteVideos = await _remoteDataSource.getVideosForQuestions(questionIds);
      
      // Save to local cache
      // Note: In a real implementation, you would save to local database
      
      return remoteVideos.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local
      try {
        final localVideos = await _localDataSource.getVideosForQuestions(questionIds);
        return localVideos.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception('Failed to load videos: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<String> getVideoUrl(String aliyunVid) async {
    try {
      return await _remoteDataSource.getVideoUrl(aliyunVid);
    } catch (e) {
      throw Exception('Failed to get video URL: $e');
    }
  }

  @override
  Future<VideoEntity?> getVideoByQuestionId(int questionId) async {
    try {
      final videos = await getVideosForQuestions([questionId]);
      return videos.isNotEmpty ? videos.first : null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveVideoProgress({
    required int videoId,
    required int currentPosition,
    required int duration,
    String? userId,
  }) async {
    try {
      // Save to local first
      await _localDataSource.saveVideoProgress(
        videoId: videoId,
        currentPosition: currentPosition,
        duration: duration,
        userId: userId,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.saveVideoProgress(
          videoId: videoId,
          currentPosition: currentPosition,
          duration: duration,
          userId: userId,
        );
      } catch (remoteError) {
        // Log remote error but don't fail the operation
        // The data is safely stored locally and can be synced later
      }
    } catch (e) {
      throw Exception('Failed to save video progress: $e');
    }
  }

  @override
  Future<void> updateVideoPlayCount({
    required int videoId,
    String? userId,
  }) async {
    try {
      // Update local first
      await _localDataSource.updateVideoPlayCount(
        videoId: videoId,
        userId: userId,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.updateVideoPlayCount(
          videoId: videoId,
          userId: userId,
        );
      } catch (remoteError) {
        // Log remote error but don't fail the operation
      }
    } catch (e) {
      throw Exception('Failed to update video play count: $e');
    }
  }

  @override
  Future<List<VideoEntity>> getVideoHistory({
    String? userId,
    int? limit,
  }) async {
    try {
      // Try remote first
      final remoteHistory = await _remoteDataSource.getVideoHistory(
        userId: userId,
        limit: limit,
      );

      return remoteHistory.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local
      try {
        final localHistory = await _localDataSource.getVideoHistory(
          userId: userId,
          limit: limit,
        );

        return localHistory.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception('Failed to load video history: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<bool> isVideoDownloaded(String aliyunVid) async {
    try {
      return await VideoFileManager.isVideoDownloaded(aliyunVid);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> getLocalVideoPath(String aliyunVid) async {
    try {
      final isDownloaded = await VideoFileManager.isVideoDownloaded(aliyunVid);
      if (isDownloaded) {
        return await VideoFileManager.getLocalVideoPath(aliyunVid);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> downloadVideo(String aliyunVid, String videoUrl) async {
    try {
      // Create progress controller
      final controller = StreamController<double>.broadcast();
      _downloadControllers[aliyunVid] = controller;

      // Start download
      await VideoFileManager.downloadVideo(aliyunVid, videoUrl);

      // Complete progress
      controller.add(1.0);
      controller.close();
      _downloadControllers.remove(aliyunVid);
    } catch (e) {
      // Clean up on error
      final controller = _downloadControllers[aliyunVid];
      if (controller != null) {
        controller.addError(e);
        controller.close();
        _downloadControllers.remove(aliyunVid);
      }
      throw Exception('Failed to download video: $e');
    }
  }

  @override
  Future<void> clearVideoCache() async {
    try {
      await VideoFileManager.clearVideoCache();
    } catch (e) {
      throw Exception('Failed to clear video cache: $e');
    }
  }

  @override
  Stream<double> getDownloadProgress(String aliyunVid) {
    final controller = _downloadControllers[aliyunVid];
    if (controller != null) {
      return controller.stream;
    }
    
    // Return empty stream if no download in progress
    return const Stream.empty();
  }

  @override
  Future<void> cancelDownload(String aliyunVid) async {
    final controller = _downloadControllers[aliyunVid];
    if (controller != null) {
      controller.close();
      _downloadControllers.remove(aliyunVid);
    }
  }
}
