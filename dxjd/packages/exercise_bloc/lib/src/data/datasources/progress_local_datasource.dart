import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../models/progress_model.dart';

/// Local data source for progress data using SQLite
abstract class ProgressLocalDataSource {
  Future<void> initialize();
  Future<ProgressModel?> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  });
  Future<void> saveProgress(ProgressModel progress);
  Future<List<ProgressModel>> getAllProgress({String? userId});
  Future<Map<String, dynamic>> getStatistics({
    required int subject,
    String? userId,
  });
  Future<void> clearProgress({String? userId});
}

/// SQLite implementation of ProgressLocalDataSource
class ProgressLocalDataSourceImpl implements ProgressLocalDataSource {
  ProgressLocalDataSourceImpl({
    required String databasePath,
  }) : _databasePath = databasePath;

  final String _databasePath;
  Database? _database;

  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  Future<Database> _initDatabase() async {
    return await openDatabase(
      _databasePath,
      version: 1,
      onCreate: _onCreate,
    );
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subject INTEGER NOT NULL,
        type TEXT NOT NULL,
        user_id TEXT,
        sort_id INTEGER,
        current_index INTEGER NOT NULL DEFAULT 0,
        total_questions INTEGER NOT NULL DEFAULT 0,
        answered_questions INTEGER NOT NULL DEFAULT 0,
        correct_count INTEGER NOT NULL DEFAULT 0,
        wrong_count INTEGER NOT NULL DEFAULT 0,
        time_spent INTEGER NOT NULL DEFAULT 0,
        score INTEGER NOT NULL DEFAULT 0,
        is_completed INTEGER NOT NULL DEFAULT 0,
        start_time TEXT,
        end_time TEXT,
        last_updated TEXT NOT NULL,
        created_at TEXT NOT NULL,
        UNIQUE(subject, type, user_id, sort_id)
      )
    ''');

    // Create indexes for better performance
    await db.execute(
        'CREATE INDEX idx_progress_subject_type ON progress (subject, type)');
    await db.execute('CREATE INDEX idx_progress_user ON progress (user_id)');
    await db.execute(
        'CREATE INDEX idx_progress_updated ON progress (last_updated)');
  }

  @override
  Future<void> initialize() async {
    await database;
  }

  @override
  Future<ProgressModel?> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    final db = await database;

    String whereClause = 'subject = ? AND type = ?';
    List<dynamic> whereArgs = [subject, type];

    if (userId != null) {
      whereClause += ' AND user_id = ?';
      whereArgs.add(userId);
    } else {
      whereClause += ' AND user_id IS NULL';
    }

    if (sortId != null) {
      whereClause += ' AND sort_id = ?';
      whereArgs.add(sortId);
    } else {
      whereClause += ' AND sort_id IS NULL';
    }

    final result = await db.query(
      'progress',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'last_updated DESC',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return _mapToProgressModel(result.first);
    }

    return null;
  }

  @override
  Future<void> saveProgress(ProgressModel progress) async {
    final db = await database;

    final data = {
      'subject': progress.subject,
      'type': progress.type,
      'user_id': progress.userId,
      'sort_id': progress.sortId,
      'current_index': progress.currentIndex,
      'total_questions': progress.totalQuestions,
      'answered_questions': jsonEncode(progress.answeredQuestions),
      'correct_count': progress.correctCount,
      'wrong_count': progress.wrongCount,
      'time_spent': progress.timeSpent.inMilliseconds,
      'score': progress.score,
      'is_completed': progress.isCompleted ? 1 : 0,
      'start_time': progress.startTime?.toIso8601String(),
      'end_time': progress.endTime?.toIso8601String(),
      'last_updated': progress.lastUpdated.toIso8601String(),
    };

    // Check if progress already exists
    final existing = await getProgress(
      subject: progress.subject,
      type: progress.type,
      userId: progress.userId,
      sortId: progress.sortId,
    );

    if (existing != null) {
      // Update existing progress
      await db.update(
        'progress',
        data,
        where: 'subject = ? AND type = ? AND '
            '(user_id = ? OR (user_id IS NULL AND ? IS NULL)) AND '
            '(sort_id = ? OR (sort_id IS NULL AND ? IS NULL))',
        whereArgs: [
          progress.subject,
          progress.type,
          progress.userId,
          progress.userId,
          progress.sortId,
          progress.sortId,
        ],
      );
    } else {
      // Insert new progress
      data['created_at'] = DateTime.now().toIso8601String();
      await db.insert('progress', data);
    }
  }

  @override
  Future<List<ProgressModel>> getAllProgress({String? userId}) async {
    final db = await database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause = 'user_id = ?';
      whereArgs.add(userId);
    }

    final result = await db.query(
      'progress',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'last_updated DESC',
    );

    return result.map((map) => _mapToProgressModel(map)).toList();
  }

  @override
  Future<Map<String, dynamic>> getStatistics({
    required int subject,
    String? userId,
  }) async {
    final db = await database;

    String whereClause = 'subject = ?';
    List<dynamic> whereArgs = [subject];

    if (userId != null) {
      whereClause += ' AND user_id = ?';
      whereArgs.add(userId);
    }

    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_sessions,
        SUM(total_questions) as total_questions,
        SUM(answered_questions) as total_answered,
        SUM(correct_count) as total_correct,
        SUM(wrong_count) as total_wrong,
        SUM(time_spent) as total_time,
        AVG(score) as average_score,
        COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_sessions
      FROM progress 
      WHERE $whereClause
    ''', whereArgs);

    if (result.isNotEmpty) {
      final row = result.first;
      return {
        'total_sessions': row['total_sessions'] ?? 0,
        'total_questions': row['total_questions'] ?? 0,
        'total_answered': row['total_answered'] ?? 0,
        'total_correct': row['total_correct'] ?? 0,
        'total_wrong': row['total_wrong'] ?? 0,
        'total_time_minutes': ((row['total_time'] ?? 0) as int) / 60000,
        'average_score': row['average_score'] ?? 0.0,
        'completed_sessions': row['completed_sessions'] ?? 0,
        'accuracy_percentage': _calculateAccuracy(
          row['total_correct'] as int? ?? 0,
          row['total_answered'] as int? ?? 0,
        ),
      };
    }

    return {};
  }

  @override
  Future<void> clearProgress({String? userId}) async {
    final db = await database;

    if (userId != null) {
      await db.delete(
        'progress',
        where: 'user_id = ?',
        whereArgs: [userId],
      );
    } else {
      await db.delete('progress');
    }
  }

  /// Helper methods

  /// Convert database map to ProgressModel
  ProgressModel _mapToProgressModel(Map<String, dynamic> map) {
    return ProgressModel(
      subject: map['subject'] as int,
      type: map['type'] as String,
      userId: map['user_id'] as String?,
      sortId: map['sort_id'] as int?,
      currentIndex: map['current_index'] as int,
      totalQuestions: map['total_questions'] as int,
      answeredQuestions: map['answered_questions'] != null
          ? Map<int, String>.from(
              jsonDecode(map['answered_questions'] as String))
          : const {},
      correctCount: map['correct_count'] as int,
      wrongCount: map['wrong_count'] as int,
      timeSpent: Duration(milliseconds: map['time_spent'] as int),
      score: map['score'] as int,
      isCompleted: (map['is_completed'] as int) == 1,
      startTime: map['start_time'] != null
          ? DateTime.parse(map['start_time'] as String)
          : null,
      endTime: map['end_time'] != null
          ? DateTime.parse(map['end_time'] as String)
          : null,
      lastUpdated: DateTime.parse(map['last_updated'] as String),
    );
  }

  /// Calculate accuracy percentage
  double _calculateAccuracy(int correct, int total) {
    if (total == 0) return 0.0;
    return (correct / total) * 100.0;
  }
}
