import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import '../../domain/repositories/audio_repository.dart';

/// Abstract audio data source interface
abstract class AudioDataSource {
  Future<void> play(String url);
  Future<void> pause();
  Future<void> resume();
  Future<void> stop();
  Future<void> seek(Duration position);
  Future<void> setPlaybackSpeed(double speed);
  Future<void> setVolume(double volume);
  Future<void> configureAudioSession({
    bool enableBackgroundPlayback = true,
    bool mixWithOthers = false,
  });
  Future<void> preload(String url);
  Future<void> dispose();

  Stream<Duration> get positionStream;
  Stream<Duration> get durationStream;
  Stream<AudioPlayerState> get playerStateStream;
  Stream<void> get completionStream;

  Future<bool> get isPlaying;
  Future<bool> get isPaused;
  Future<Duration> get currentPosition;
  Future<Duration> get totalDuration;
  Future<double> get currentVolume;
  Future<double> get currentPlaybackSpeed;
}

/// Implementation of audio data source using audioplayers package
class AudioDataSourceImpl implements AudioDataSource {
  AudioDataSourceImpl() {
    _initializePlayer();
  }

  final AudioPlayer _audioPlayer = AudioPlayer();

  // Stream controllers for audio events
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController =
      StreamController<Duration>.broadcast();
  final StreamController<AudioPlayerState> _playerStateController =
      StreamController<AudioPlayerState>.broadcast();
  final StreamController<void> _completionController =
      StreamController<void>.broadcast();

  // Current state
  String? _currentUrl;
  AudioPlayerState _currentState = AudioPlayerState.stopped;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  /// Initialize audio player with event listeners
  void _initializePlayer() {
    // Listen to position changes
    _audioPlayer.onPositionChanged.listen((position) {
      _currentPosition = position;
      _positionController.add(position);
    });

    // Listen to duration changes
    _audioPlayer.onDurationChanged.listen((duration) {
      _totalDuration = duration;
      _durationController.add(duration);
    });

    // Listen to player state changes
    _audioPlayer.onPlayerStateChanged.listen((state) {
      final audioState = _mapPlayerState(state);
      _currentState = audioState;
      _playerStateController.add(audioState);
    });

    // Listen to completion events
    _audioPlayer.onPlayerComplete.listen((_) {
      _currentState = AudioPlayerState.completed;
      _playerStateController.add(AudioPlayerState.completed);
      _completionController.add(null);
    });
  }

  /// Play audio from URL
  @override
  Future<void> play(String url) async {
    try {
      if (_currentUrl != url) {
        await _audioPlayer.stop();
        await _audioPlayer.setSource(UrlSource(url));
        _currentUrl = url;
      }

      await _audioPlayer.resume();
    } catch (e) {
      _currentState = AudioPlayerState.error;
      _playerStateController.add(AudioPlayerState.error);
      throw Exception('Failed to play audio: $e');
    }
  }

  /// Pause current audio
  @override
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      throw Exception('Failed to pause audio: $e');
    }
  }

  /// Resume paused audio
  @override
  Future<void> resume() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      throw Exception('Failed to resume audio: $e');
    }
  }

  /// Stop current audio
  @override
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _currentUrl = null;
      _currentPosition = Duration.zero;
      _positionController.add(Duration.zero);
    } catch (e) {
      throw Exception('Failed to stop audio: $e');
    }
  }

  /// Seek to specific position
  @override
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      throw Exception('Failed to seek audio: $e');
    }
  }

  /// Set playback speed
  @override
  Future<void> setPlaybackSpeed(double rate) async {
    try {
      await _audioPlayer.setPlaybackRate(rate);
    } catch (e) {
      throw Exception('Failed to set playback rate: $e');
    }
  }

  /// Set volume (0.0 to 1.0)
  @override
  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume);
    } catch (e) {
      throw Exception('Failed to set volume: $e');
    }
  }

  /// Get current playback position
  @override
  Future<Duration> get currentPosition async => _currentPosition;

  /// Get total duration of current audio
  @override
  Future<Duration> get totalDuration async => _totalDuration;

  /// Get current volume
  @override
  Future<double> get currentVolume async => 1.0; // Default volume

  /// Get current playback speed
  @override
  Future<double> get currentPlaybackSpeed async => 1.0; // Default speed

  /// Stream of playback position updates
  @override
  Stream<Duration> get positionStream => _positionController.stream;

  /// Stream of duration updates
  @override
  Stream<Duration> get durationStream => _durationController.stream;

  /// Stream of player state changes
  @override
  Stream<AudioPlayerState> get playerStateStream =>
      _playerStateController.stream;

  /// Stream of playback completion events
  @override
  Stream<void> get completionStream => _completionController.stream;

  /// Check if audio is currently playing
  @override
  Future<bool> get isPlaying async => _currentState == AudioPlayerState.playing;

  /// Check if audio is paused
  @override
  Future<bool> get isPaused async => _currentState == AudioPlayerState.paused;

  /// Check if audio is stopped
  bool get isStopped => _currentState == AudioPlayerState.stopped;

  /// Preload audio for faster playback
  @override
  Future<void> preload(String url) async {
    try {
      await _audioPlayer.setSource(UrlSource(url));
      _currentUrl = url;
    } catch (e) {
      throw Exception('Failed to preload audio: $e');
    }
  }

  /// Configure audio session for background playback
  @override
  Future<void> configureAudioSession({
    bool enableBackgroundPlayback = true,
    bool mixWithOthers = false,
  }) async {
    try {
      // Configure audio context for different platforms
      await _audioPlayer.setAudioContext(
        AudioContext(
          iOS: AudioContextIOS(
            defaultToSpeaker: true,
            category: enableBackgroundPlayback
                ? AVAudioSessionCategory.playback
                : AVAudioSessionCategory.ambient,
            options: mixWithOthers ? [AVAudioSessionOptions.mixWithOthers] : [],
          ),
          android: AudioContextAndroid(
            isSpeakerphoneOn: true,
            stayAwake: enableBackgroundPlayback,
            contentType: AndroidContentType.music,
            usageType: AndroidUsageType.media,
            audioFocus: mixWithOthers
                ? AndroidAudioFocus.gainTransientMayDuck
                : AndroidAudioFocus.gain,
          ),
        ),
      );
    } catch (e) {
      throw Exception('Failed to configure audio session: $e');
    }
  }

  /// Handle audio interruptions (phone calls, etc.)
  Stream<AudioInterruption> get onAudioInterruption {
    // This would need platform-specific implementation
    // For now, return an empty stream
    return const Stream.empty();
  }

  /// Release audio player resources
  @override
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _positionController.close();
    await _durationController.close();
    await _playerStateController.close();
    await _completionController.close();
  }

  /// Map audioplayers PlayerState to our AudioPlayerState
  AudioPlayerState _mapPlayerState(PlayerState state) {
    switch (state) {
      case PlayerState.stopped:
        return AudioPlayerState.stopped;
      case PlayerState.playing:
        return AudioPlayerState.playing;
      case PlayerState.paused:
        return AudioPlayerState.paused;
      case PlayerState.completed:
        return AudioPlayerState.completed;
    }
  }
}
