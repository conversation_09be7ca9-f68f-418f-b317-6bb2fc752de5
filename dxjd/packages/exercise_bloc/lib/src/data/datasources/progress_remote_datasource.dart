import 'package:dio/dio.dart';
import '../models/progress_model.dart';

/// Remote data source for progress data using HTTP API
abstract class ProgressRemoteDataSource {
  Future<ProgressModel?> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  });
  Future<void> saveProgress(ProgressModel progress);
  Future<List<ProgressModel>> getAllProgress({String? userId});
  Future<void> syncData({String? userId});
  Future<void> clearProgress({String? userId});
}

/// HTTP API implementation of ProgressRemoteDataSource
class ProgressRemoteDataSourceImpl implements ProgressRemoteDataSource {
  ProgressRemoteDataSourceImpl({
    required Dio dio,
    required String baseUrl,
  })  : _dio = dio,
        _baseUrl = baseUrl;

  final Dio _dio;
  final String _baseUrl;

  @override
  Future<ProgressModel?> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'subject': subject,
        'type': type,
      };

      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      if (sortId != null) {
        queryParams['sort_id'] = sortId;
      }

      final response = await _dio.get(
        '$_baseUrl/progress',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        if (data['success'] == true && data['data'] != null) {
          return ProgressModel.fromJson(data['data'] as Map<String, dynamic>);
        }
      }

      return null;
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        // Progress not found is not an error
        return null;
      }
      throw Exception('Failed to get progress from server: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get progress from server: $e');
    }
  }

  @override
  Future<void> saveProgress(ProgressModel progress) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/progress',
        data: {
          'progress': progress.toJson(),
        },
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Server returned status code: ${response.statusCode}');
      }

      final data = response.data as Map<String, dynamic>;
      if (data['success'] != true) {
        throw Exception('Server returned error: ${data['message'] ?? 'Unknown error'}');
      }
    } on DioException catch (e) {
      throw Exception('Failed to save progress to server: ${e.message}');
    } catch (e) {
      throw Exception('Failed to save progress to server: $e');
    }
  }

  @override
  Future<List<ProgressModel>> getAllProgress({String? userId}) async {
    try {
      final queryParams = <String, dynamic>{};

      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      final response = await _dio.get(
        '$_baseUrl/progress/all',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        if (data['success'] == true && data['data'] != null) {
          final progressList = data['data'] as List<dynamic>;
          return progressList
              .map((item) => ProgressModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
      }

      return [];
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        // No progress found is not an error
        return [];
      }
      throw Exception('Failed to get all progress from server: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get all progress from server: $e');
    }
  }

  @override
  Future<void> syncData({String? userId}) async {
    try {
      final queryParams = <String, dynamic>{};

      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      final response = await _dio.post(
        '$_baseUrl/progress/sync',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode != 200) {
        throw Exception('Server returned status code: ${response.statusCode}');
      }

      final data = response.data as Map<String, dynamic>;
      if (data['success'] != true) {
        throw Exception('Server returned error: ${data['message'] ?? 'Unknown error'}');
      }
    } on DioException catch (e) {
      throw Exception('Failed to sync data with server: ${e.message}');
    } catch (e) {
      throw Exception('Failed to sync data with server: $e');
    }
  }

  @override
  Future<void> clearProgress({String? userId}) async {
    try {
      final queryParams = <String, dynamic>{};

      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      final response = await _dio.delete(
        '$_baseUrl/progress',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Server returned status code: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        if (data['success'] != true) {
          throw Exception('Server returned error: ${data['message'] ?? 'Unknown error'}');
        }
      }
    } on DioException catch (e) {
      throw Exception('Failed to clear progress on server: ${e.message}');
    } catch (e) {
      throw Exception('Failed to clear progress on server: $e');
    }
  }
}

/// Mock implementation for testing and development
class ProgressRemoteDataSourceMock implements ProgressRemoteDataSource {
  final Map<String, ProgressModel> _mockData = {};

  @override
  Future<ProgressModel?> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final key = _generateKey(subject, type, userId, sortId);
    return _mockData[key];
  }

  @override
  Future<void> saveProgress(ProgressModel progress) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    final key = _generateKey(
      progress.subject,
      progress.type,
      progress.userId,
      progress.sortId,
    );
    _mockData[key] = progress;
  }

  @override
  Future<List<ProgressModel>> getAllProgress({String? userId}) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    if (userId != null) {
      return _mockData.values
          .where((progress) => progress.userId == userId)
          .toList();
    }

    return _mockData.values.toList();
  }

  @override
  Future<void> syncData({String? userId}) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));
    // Mock sync operation - in real implementation this would sync with server
  }

  @override
  Future<void> clearProgress({String? userId}) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    if (userId != null) {
      _mockData.removeWhere((key, progress) => progress.userId == userId);
    } else {
      _mockData.clear();
    }
  }

  /// Generate unique key for progress identification
  String _generateKey(int subject, String type, String? userId, int? sortId) {
    return '${subject}_${type}_${userId ?? 'null'}_${sortId ?? 'null'}';
  }
}
