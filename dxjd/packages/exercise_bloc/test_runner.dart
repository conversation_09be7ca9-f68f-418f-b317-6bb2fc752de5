#!/usr/bin/env dart

import 'dart:io';

/// Test runner script for exercise_bloc package
/// 
/// This script runs all tests in the package and provides detailed output
/// about test coverage and results.
void main(List<String> args) async {
  print('🧪 Running Exercise BLoC Tests');
  print('=' * 50);

  // Check if we're in the right directory
  final pubspecFile = File('pubspec.yaml');
  if (!pubspecFile.existsSync()) {
    print('❌ Error: pubspec.yaml not found. Please run from package root.');
    exit(1);
  }

  // Parse command line arguments
  final bool verbose = args.contains('--verbose') || args.contains('-v');
  final bool coverage = args.contains('--coverage') || args.contains('-c');
  final String? testPattern = _getTestPattern(args);

  try {
    // Step 1: Get dependencies
    print('📦 Getting dependencies...');
    await _runCommand(['flutter', 'pub', 'get'], verbose: verbose);

    // Step 2: Run tests
    print('\n🧪 Running tests...');
    final testArgs = ['test'];
    
    if (testPattern != null) {
      testArgs.addAll(['--name', testPattern]);
      print('🔍 Running tests matching pattern: $testPattern');
    }
    
    if (coverage) {
      testArgs.add('--coverage');
      print('📊 Coverage collection enabled');
    }
    
    if (verbose) {
      testArgs.add('--reporter=expanded');
    }

    final testResult = await _runCommand(['flutter', 'pub', 'run', ...testArgs], verbose: verbose);

    // Step 3: Generate coverage report if requested
    if (coverage && testResult == 0) {
      print('\n📊 Generating coverage report...');
      await _generateCoverageReport(verbose);
    }

    // Step 4: Summary
    print('\n' + '=' * 50);
    if (testResult == 0) {
      print('✅ All tests passed successfully!');
      if (coverage) {
        print('📊 Coverage report generated in coverage/html/index.html');
      }
    } else {
      print('❌ Some tests failed. Exit code: $testResult');
      exit(testResult);
    }

  } catch (e) {
    print('❌ Error running tests: $e');
    exit(1);
  }
}

/// Extract test pattern from command line arguments
String? _getTestPattern(List<String> args) {
  final patternIndex = args.indexOf('--pattern');
  if (patternIndex != -1 && patternIndex + 1 < args.length) {
    return args[patternIndex + 1];
  }
  
  final nameIndex = args.indexOf('--name');
  if (nameIndex != -1 && nameIndex + 1 < args.length) {
    return args[nameIndex + 1];
  }
  
  return null;
}

/// Run a command and return the exit code
Future<int> _runCommand(List<String> command, {bool verbose = false}) async {
  if (verbose) {
    print('🔧 Running: ${command.join(' ')}');
  }

  final process = await Process.start(
    command.first,
    command.skip(1).toList(),
    mode: ProcessStartMode.inheritStdio,
  );

  return await process.exitCode;
}

/// Generate HTML coverage report
Future<void> _generateCoverageReport(bool verbose) async {
  // Check if genhtml is available (part of lcov)
  try {
    final result = await Process.run('which', ['genhtml']);
    if (result.exitCode != 0) {
      print('⚠️  genhtml not found. Install lcov to generate HTML coverage reports.');
      print('   On macOS: brew install lcov');
      print('   On Ubuntu: sudo apt-get install lcov');
      return;
    }
  } catch (e) {
    print('⚠️  Could not check for genhtml: $e');
    return;
  }

  // Create coverage directory
  final coverageDir = Directory('coverage');
  if (!coverageDir.existsSync()) {
    coverageDir.createSync(recursive: true);
  }

  // Generate HTML report
  await _runCommand([
    'genhtml',
    'coverage/lcov.info',
    '-o',
    'coverage/html',
    '--title',
    'Exercise BLoC Coverage Report',
  ], verbose: verbose);
}
