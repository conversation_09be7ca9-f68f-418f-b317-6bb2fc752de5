import 'dart:io';

import 'package:flutter/foundation.dart';

class HttpConstant {
  static const bool IsRelease = false; // 正式/测试环境切换

  static const String Http_Host =
      IsRelease ? 'dxjk.daxiangjd.com' : 'dxjk-test.daxiangjd.com';
  static  String AccessKeyId =
      Platform.isIOS ? '23KLH9bRHLOc6yw6' : '6WhwtAp60FM1KHM8'; // xs060637D2h87pM3
  static  String AccessKeySecret =
  Platform.isIOS ? '8FN4GR66SwT1ml3EQf8o093u5C8FT022' : '4keIoM2ZLg6gJ3Il96joRd9M1yBa7w7o';

  static const int ReceiveTimeout = 15000;
  static const int ConnectTimeout = 15000;
  static const int Retry_Max_Count = 3;
  static const String Version = 'v1.0';
  static const int SignType = 101;
  static const int Client = 10;
  static const bool Proxy_Enable = false; //kDebugMode; // 是否启用代理，方便调试抓包

  // 代理设置 Charles 抓包用
  static const CompanyIp = '************'; // 公司ip
  static const HomeIp = '***********'; // 家ip
  static const IphoneIp = '************'; // 手机 热点ip
  static var Proxy_Ip = CompanyIp; // 代理服务ip
  static const Proxy_Port = 8888; // 代理服务端口

  static const int renewalTokenCode = 1000102; // token续期的code
  static const int reLoginCode = 1000103; // token长失效，重新登录code
  static const int msgVCodeMaxLength = 5; // 短信验证码的长度
}

class AliyunOSSConstant {
  static const AccessKey = 'mkMB160eV3GA';

  static const BucketName = 'ovsx-usr';
  static const Endpoint = 'http://oss-cn-zhangjiakou.aliyuncs.com';
  static const OSSUrl = 'https://ovsx-usr.oss-cn-zhangjiakou.aliyuncs.com';

  static const FeedBackBucketName = 'feedback2';
  static const FeedBackOSSUrl = 'http://feedback2.oss-cn-shenzhen.aliyuncs.com';

  static const Subject1ScoreBucketName = 'feedback2';
  static const Subject1ScoreOSSUrl =
      'http://feedback2.oss-cn-shenzhen.aliyuncs.com';

  static const SignInBucketName = 'feedback2';
  static const SignInBucketNameOSSUrl =
      'http://feedback2.oss-cn-shenzhen.aliyuncs.com';
}
