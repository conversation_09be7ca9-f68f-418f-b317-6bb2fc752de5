import 'dart:async';

class HttpEventBus {
  //私有构造函数
  HttpEventBus._internal();

  static HttpEventBus? _instance;

  static HttpEventBus get instance => _getInstance();

  static HttpEventBus _getInstance() {
    return _instance ??= HttpEventBus._internal();
  }
  final rateLimiter = RateLimiter();
  bool isTest = false;

  // 存储事件回调方法
  final Map<String, Function> _events = {};

  // 设置事件监听
  void addListener(String eventKey, Function callback) {
    _events[eventKey] = callback;
  }

  // 移除监听
  void removeListener(String eventKey) {
    _events.remove(eventKey);
  }

  // 提交事件
  void commit(String eventKey) {
    if(eventKey == EventKeys.logout){
      rateLimiter.trigger(() {
        _events[eventKey]?.call();
        print("ssssssssssssssssssssssssssssssssssssssssssssss");
      });
    }else{
      _events[eventKey]?.call();
    }
    print("");
  }
}

class EventKeys {
  static const String logout = "Logout";
  static const String hasToken = "hasToken";
  static const String addNewCar = "addNewCar";
  static const String updateLogs = "updateLogs";
  static const String renewToken = "renewDxjdToken";
}
class RateLimiter {
  Timer? _timer;

  void trigger(Function action) {
    if (_timer?.isActive ?? false) {
      // 如果计时器还在运行，则不执行动作
      return;
    }
    _timer?.cancel();

    // 执行提供的动作
    action();

    // 启动一个新的计时器，在5秒后自动取消限制
    _timer = Timer(Duration(seconds: 5), () {
      _timer = null; // 计时结束后，允许再次触发
    });
  }
}
