import 'dart:convert';
import 'package:api/src/http/http_constant.dart';
import 'package:component_library/component_library.dart';
import 'package:user_repository/user_repository.dart';
import '../dio/dio_adapter.dart';
import '../request/base_request.dart';
import 'http_adapter.dart';
import 'http_connectivity.dart';
import 'http_error.dart';
import 'http_event_bus.dart';
import 'http_method.dart';

typedef UserTokenSupplier = Future<String?> Function();

///1.支持网络库插拔设计，且不干扰业务层
///2.基于配置请求请求，简洁易用
///3.Adapter设计，扩展性强
///4.统一异常和返回处理
class HttpManager {
  HttpManager._() {
    init();
  }
  static HttpManager? _instance;
  late HttpAdapter http;

  static HttpManager getInstance() {
    if (_instance == null) {
      _instance = HttpManager._();
    }
    return _instance!;
  }

  void init() {
    http = DioAdapter();
  }

  UserTokenSupplier? _userTokenSupplier;
  set userTokenSupplier(UserTokenSupplier? _supplier) {
    _userTokenSupplier = _supplier;
    (http as DioAdapter).userTokenSupplier = _supplier;
  }

  Future fire(BaseRequest request,{bool showLoading=false}) async {
    if(showLoading){
      Loading.show();
    }
    if (!await NetworkConnectivity.connected) {
      if(showLoading){
        Loading.dismiss();
      }
      throw HttpsException(-1, '网络未连接', data: null);
    }

    HttpResponse? response;
    var error;

    try {
      response = await send(request);
    } on HttpsException catch (e) {
      // error = e;
      // response = e.data;
      rethrow;
    }

    final result = response.data;
    final status = response.statusCode;
    switch (status) {
      case 200:
        {
          if (request.httpMethod() == HttpMethod.DOWNLOAD) {
            return 'download is success';
          }
          // 需要处理
          final Map<String, dynamic> mapData = jsonDecode(result!);
          // printLog(mapData);
          final code = mapData['code'];

          // token无法续期必须退出登录
          if (code == HttpConstant.reLoginCode) {
            HttpEventBus.instance.commit(EventKeys.logout);
            if(showLoading){
              Loading.dismiss();
            }
            return;
          }

          // if (HttpErrorEventBus.instance.isTest) {
          //   response.statusCode = 401; // 将状态码修改为401
          //   throw DioException.badResponse(
          //       statusCode: 401,
          //       requestOptions: (response as Response).requestOptions,
          //       response: response as Response);
          //   // throw DioError.from(response); // 抛出自定义异常，包含修改后的状态码和其他信息
          // }

          if (code == 0 || code == 200) {
            if(showLoading){
              Loading.dismiss();
            }
            if (mapData['data'] != null) {
              return mapData['data'];
            } else {
              return Map();
            }
          } else {
            if(showLoading){
              Loading.dismiss();
              // Toast.show(mapData['message']);
            }
            throw HttpsException(
                status ?? -1, mapData['message'] ?? mapData.toString(),
                data: mapData);
          }
        }
      case 500:
        if(showLoading){
          Loading.dismiss();
          // Toast.show('服务器异常');
        }
        throw HttpsException(status ?? -1, '服务器异常', data: result);
      default:
        if(showLoading){
          Loading.dismiss();
          // Toast.show(error.message);
        }
        throw HttpsException(status ?? -1, error.message, data: result);
    }
  }

  Future<HttpResponse<T>> send<T>(BaseRequest request) async {
    return http.send(request);
  }

  void cancelRequest() {
    http.cancelRequests();
  }

  void printLog(log) {
    print('http_manager_print:' + log.toString());
  }
}
