import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';

class HeaderInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 对请求参数进行签名
    final header = options.headers;
    final token = header['token'];
    final accessKeyId = header['accesskeyid'];
    final signType = header['signtype'];
    final timestamp = header['timestamp'];
    final version = header['version'];
    final nonce = header['nonce'];

    var signBody = <String, dynamic>{};
    if (token == null || (token != null && (token as String).length == 0)) {
      signBody = {
        'accessKeyId': accessKeyId,
        'signType': signType,
        'timestamp': timestamp,
        'version': version,
        'nonce': nonce
      };
    } else {
      signBody = {
        'accessKeyId': accessKeyId,
        'signType': signType,
        'timestamp': timestamp,
        'token': token,
        'version': version,
        'nonce': nonce
      };
    }
    // if (options.method != 'DOWNLOAD') {
    //   signBody.addAll(options.data);
    // }

    print('options: $options.method');
    // add queryParameters
    // signBody.addAll(options.queryParameters);
    if (!options.path.contains('.gz')) {
      signBody.addAll(options.data);
    }

    // GET 特殊处理
    const addGetIdValue = false;
    if (options.method == 'GET' && addGetIdValue) {
      //如果url后有参数传入则获取最后字段值
      signBody['Id'] = '';
    }

    // signBody sorted by key
    final sortedKeys = signBody.keys.toList()
      ..sort((a, b) {
        return a.toLowerCase().compareTo(b.toLowerCase());
      });

    final sortedSignBody = Map();
    sortedKeys.forEach((element) {
      print('m ${signBody[element]}');
      // final type = signBody[element].runtimeType.toString();
      // print(signBody[element].runtimeType.toString());
      // if (element == 'Answers') {
      //   sortedSignBody[element] = 'object';
      // } else {
      // 判断是否是对象
      if ((signBody[element] is Map) || (signBody[element] is List)) {
        sortedSignBody[element] = 'object';
      } else {
        final value = signBody[element].toString();
        if (value.isNotEmpty) {
          sortedSignBody[element] = value;
        }
      }
      // }
    });

    // body's value => string
    final sortedSignBodyValueStr =
        sortedSignBody.values.reduce((value, element) {
      if (value.length == 0) {
        return value + element;
      } else {
        return value + ',' + element;
      }
    });

    // string => bytes
    final bytes = utf8.encode(sortedSignBodyValueStr);
    final digest = sha1.convert(bytes);
    options.headers['sign'] = digest.toString();

    super.onRequest(options, handler);
  }
}
