import 'package:dio/dio.dart';

// github: https://github.com/ipcjs/dio/blob/hotfix/token-interceptor/example/lib/queued_interceptor_crsftoken.dart
// github: https://github.com/maciogg/Dio-CustomInterceptor/blob/main/custom_interceptor.dart
// 👍🏻靠谱github: https://github.com/cfug/dio/blob/main/example/lib/queued_interceptor_crsftoken.dart
// github: https://gist.github.com/TimurMukhortov/a1c9819e3779015e54bc3964b7d2308a

class QueueTokenInterceptor extends QueuedInterceptor {
  QueueTokenInterceptor(
    Dio dio, {
    this.repeatDeep = 3,
  }) {
    tokenDio = Dio()
      ..options = dio.options
      ..httpClientAdapter = dio.httpClientAdapter;
  }
  late final Dio tokenDio;
  final int repeatDeep;

  Dio? _repeatDio;
  Dio get repeatDio {
    if (repeatDeep <= 1) {
      // tokenDio does not contain a token interceptor. It does not handle 401 errors.
      return tokenDio;
    }
    if (_repeatDio == null) {
      _repeatDio = Dio()
        ..options = tokenDio.options
        ..httpClientAdapter = tokenDio.httpClientAdapter;
      // repeatDio has a token interceptor, which refreshes the token if the request reports a 401 error.
      _repeatDio!.interceptors
          .add(QueueTokenInterceptor(_repeatDio!, repeatDeep: repeatDeep - 1));
    }
    return _repeatDio!;
  }

  String? csrfToken;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print('send request：path:${options.path}，baseURL:${options.baseUrl}');
    if (csrfToken == null) {
      print('no token，request token firstly...');
      tokenDio.get('/token').then((d) {
        options.headers['csrfToken'] = csrfToken = d.data['data']['token'];
        print('request token succeed, value: ' + d.data['data']['token']);
        print(
            'continue to perform request：path:${options.path}，baseURL:${options.path}');
        handler.next(options);
      }).catchError(
        (error, stackTrace) {
          handler.reject(error, true);
        },
        test: (e) => e is DioError,
      );
    } else {
      options.headers['csrfToken'] = csrfToken;
      return handler.next(options);
    }
  }

  @override
  void onError(DioException error, ErrorInterceptorHandler handler) {
    //print(error);
    // Assume 401 stands for token expired
    if (error.response?.statusCode == 401) {
      var options = error.response!.requestOptions;
      // If the token has been updated, repeat directly.
      if (csrfToken != options.headers['csrfToken']) {
        options.headers['csrfToken'] = csrfToken;
        //repeat
        repeatDio.fetch(options).then(
          (r) {
            handler.resolve(r);
          },
          onError: (e) {
            handler.reject(e);
          },
        );
        return;
      }

      tokenDio.get('/token').then((d) {
        //update csrfToken
        options.headers['csrfToken'] = csrfToken = d.data['data']['token'];
      }).then((e) {
        //repeat, The current dio has been blocked and needs to be re-requested using another instance.
        repeatDio.fetch(options).then(
          (r) {
            handler.resolve(r);
          },
          onError: (e) {
            handler.reject(e);
          },
        );
      });
      return;
    }
    return handler.next(error);
  }
}

// final _kRandom = Random(0);
//
// var _tag2InvokeCount = 0;
//
// class _MockAdapter extends HttpClientAdapter {
//   ResponseBody jsonBody(Object? obj) {
//     return ResponseBody.fromString(
//       jsonEncode(obj),
//       200,
//       headers: {
//         Headers.contentTypeHeader: [Headers.jsonContentType],
//       },
//     );
//   }
//
//   @override
//   Future<ResponseBody> fetch(RequestOptions options,
//       Stream<Uint8List>? requestStream, Future? cancelFuture) async {
//     final relativePath =
//     options.uri.path.substring(Uri.parse(options.baseUrl).path.length);
//     print([
//       '[mock]',
//       'uri: ${options.uri},',
//       'path: $relativePath,',
//     ].join(' '));
//
//     await Future.delayed(Duration(milliseconds: _kRandom.nextInt(1000)));
//
//     switch (relativePath) {
//       case 'token':
//         return jsonBody({
//           'data': {'token': 'mock-token'}
//         });
//       case 'test':
//         final tag = options.uri.queryParameters['tag'];
//         final isTag2 = tag == '2';
//         if (isTag2) {
//           switch (++_tag2InvokeCount) {
//             case 1:
//               return ResponseBody.fromString('', 401);
//             case 2:
//               return ResponseBody.fromString('', 500);
//           }
//         }
//         return jsonBody({
//           'data': {
//             'tag': tag,
//             if (isTag2) 'count': _tag2InvokeCount,
//           }
//         });
//     }
//     return ResponseBody.fromString('text', 404);
//   }
