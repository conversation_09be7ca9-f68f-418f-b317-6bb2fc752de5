import '../http/http_adapter.dart';
import '../request/base_request.dart';

///测试适配器，mock数据
class MockAdapter extends HttpAdapter {
  @override
  Future<HttpResponse<T>> send<T>(BaseRequest request) async {
    return Future.delayed(Duration(milliseconds: 1000), () {
      return HttpResponse(
          request: request,
          data: {'code': 0, 'message': 'success'} as T,
          statusCode: 403);
    });
  }

  @override
  void cancelRequests() {
    // TODO: implement cancelRequests
  }
}
