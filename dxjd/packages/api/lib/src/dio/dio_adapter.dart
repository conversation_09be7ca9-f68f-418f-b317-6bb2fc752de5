import 'dart:convert';
import 'dart:io';
import 'package:api/src/dio/retry_interceptor.dart';
import 'package:api/src/dio/token_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio/src/adapters/io_adapter.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../http/http_adapter.dart';
import '../http/http_constant.dart';
import '../http/http_error.dart';
import '../http/http_event_bus.dart';
import '../http/http_method.dart';
import '../request/base_request.dart';
import '../request/download.dart';
import '../request/request.dart';
import 'header_interceptor.dart';

typedef UserTokenSupplier = Future<String?> Function();
const _appTokenEnvironmentVariableKey = 'ovsx-app-token';

///Dio适配器
class DioAdapter extends HttpAdapter {
  static Dio? _dio;
  static Dio? get dio => _dio;
  //定义一个共用的取消令牌，可用于用户操作时token校验失败取消其他请求操作
  CancelToken cancelToken = CancelToken();

  UserTokenSupplier? _userTokenSupplier;
  set userTokenSupplier(UserTokenSupplier? _supplier) {
    _userTokenSupplier = _supplier;
  }

  @override
  Future<HttpResponse<T>> send<T>(BaseRequest request) async {
    // 这里是通过运行时的环境变量存储api的accessKeyId，在main configuration中
    // final accessKeyId = const String.fromEnvironment(
    //   _appTokenEnvironmentVariableKey,
    // );
    // request.addHeader('accessKeyId', accessKeyId);
    if (request.needLogin()) {
      if (_userTokenSupplier != null) {
        final token = await _userTokenSupplier!();
        if (token != null) {
          if (token.length != 0) {
            request.addHeader('token', token);
          }
        }
      }
    } else {
      request.addHeader('token', '');
    }
    var response, options = Options(headers: request.header);
    var error = null;
    try {
      if (request.httpMethod() == HttpMethod.GET) {
        response = await getDio()
            .get(request.url(), options: options, cancelToken: cancelToken);
      } else if (request.httpMethod() == HttpMethod.POST) {
        response = await getDio().post(request.url(),
            data: request.params, //request.params,
            options: options,
            cancelToken: cancelToken);
      } else if (request.httpMethod() == HttpMethod.DELETE) {
        response = await getDio().delete(request.url(),
            data: request.params, options: options, cancelToken: cancelToken);
      } else if (request.httpMethod() == HttpMethod.DOWNLOAD) {
        final downlaod_request = request as DownLoadRequest;
        response = await getDio().download(
            request.url(), downlaod_request.savePath.path,
            options: options, cancelToken: cancelToken);
      } else if (request.httpMethod() == HttpMethod.PUT) {
        response = await getDio().put(request.url(),
            data: request.params, //request.params,
            options: options,
            cancelToken: cancelToken);
      }
    } on DioException catch (e) {
      error = e.error;
      response = e.response;
    }

    if (error != null) {
      if (error is SocketException) {
        throw HttpsException(-1, error.message,
            data: await buildRes(response, request));
      } else {
        ///抛出HttpsException
        throw HttpsException(response?.statusCode ?? -1, error.message,
            data: await buildRes(response, request));
      }
    }

    return buildRes(response, request);
  }

  Dio getDio() {
    if (_dio == null) {
      final dio = Dio();
      dio.options = BaseOptions(
          receiveTimeout: Duration(milliseconds: HttpConstant.ReceiveTimeout),
          connectTimeout: Duration(
              milliseconds: HttpConstant.ConnectTimeout)); // 设置超时时间等 ...
      // if (HttpConstant.Proxy_Enable) {
      //   (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient =
      //       (client) {
      //     client.findProxy = (uri) {
      //       return 'PROXY ${HttpConstant.Proxy_Ip}:${HttpConstant.Proxy_Port}';
      //     };
      //   } as CreateHttpClient?;
      // }
      if (HttpConstant.Proxy_Enable) {
        dio.httpClientAdapter = IOHttpClientAdapter(
          createHttpClient: () {
            final client = HttpClient();
            client.findProxy = (uri) {
              // Proxy all request to localhost:8888.
              // Be aware, the proxy should went through you running device,
              // not the host platform.
              // return 'PROXY localhost:8888';
              return 'PROXY ${HttpConstant.Proxy_Ip}:${HttpConstant.Proxy_Port}';
            };
            client.badCertificateCallback =
                (X509Certificate cert, String host, int port) => true;
            return client;
          },
        );
      }
      dio.interceptors
          .add(HeaderInterceptor()); // 添加Header拦截器，如 token之类，需要全局使用的参数
      // dio.interceptors.add(
      //   LogInterceptor(responseBody: false),
      // );
      dio.interceptors.add(
        PrettyDioLogger(
          // 添加日志格式化工具类
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90,
        ),
      );
      dio.interceptors.add(RetryInterceptor(apiClient: dio));
      dio.interceptors.add(
        TokenInterceptor(dio),
      );

      //  https: //github.com/flutterchina/dio/blob/develop/example/lib/queued_interceptor_crsftoken.dart
      // dio.interceptors.add(
      //   QueuedInterceptorsWrapper(
      //     onResponse: (response, handler) async {
      //       dynamic mapData;
      //       try {
      //         mapData = jsonDecode(response.data);
      //       } catch (e) {}
      //
      //       print('HttpErrorEventBus.instance.isTest ${Api.testToken}');
      //       if (mapData['code'] == HttpConstant.renewalTokenCode ||
      //           Api.testToken) {
      //         print(
      //             'the token has expired, need to receive new token ${response.requestOptions}');
      //         final options = response.requestOptions;
      //
      //         /// assume receiving the token has no errors
      //         /// to check `null-safety` and error handling
      //         /// please check inside the [onRequest] closure
      //         final _ = await _refreshToken();
      //         options.headers['token'] =
      //             await UserSecureStorage().getUserToken();
      //         Api.testToken = false;
      //         if (options.headers['token'] != null) {
      //           print('the token has been updated');
      //
      //           final originResult = await dio.fetch(options..path);
      //           if (originResult.statusCode != null &&
      //               originResult.statusCode! ~/ 100 == 2) {
      //             return handler.resolve(originResult);
      //           }
      //         }
      //         print('the token has not been updated');
      //         return handler.reject(
      //           DioException(requestOptions: options),
      //         );
      //       }
      //
      //       return handler.next(response);
      //     },
      //   ),
      // );

      _dio = dio;
      return _dio!;
    } else {
      return _dio!;
    }
  }

  // Future<bool> _refreshToken() async {
  //   try {
  //     final _refreshTokenRequest = RenewalUserTokenRequest();
  //     final userName = await UserSecureStorage().getUsername();
  //     _refreshTokenRequest
  //       ..add('Client', 10)
  //       ..add('UserFlag', userName ?? '');
  //
  //     final accessKeyId = const String.fromEnvironment(
  //       _appTokenEnvironmentVariableKey,
  //     );
  //     _refreshTokenRequest.addHeader('accessKeyId', accessKeyId);
  //     if (_refreshTokenRequest.needLogin()) {
  //       if (_userTokenSupplier != null) {
  //         final token = await _userTokenSupplier!();
  //         _refreshTokenRequest.addHeader('token', token ?? '');
  //       } else {
  //         _refreshTokenRequest.addHeader('token', '');
  //       }
  //     } else {
  //       _refreshTokenRequest.addHeader('token', '');
  //     }
  //
  //     var response, options = Options(headers: _refreshTokenRequest.header);
  //
  //     final tokenDio = Dio();
  //     if (HttpConstant.Proxy_Enable) {
  //       tokenDio.httpClientAdapter = IOHttpClientAdapter(
  //         createHttpClient: () {
  //           final client = HttpClient();
  //           client.findProxy = (uri) {
  //             // Proxy all request to localhost:8888.
  //             // Be aware, the proxy should went through you running device,
  //             // not the host platform.
  //             return 'PROXY ${HttpConstant.Proxy_Ip}:${HttpConstant.Proxy_Port}';
  //           };
  //           client.badCertificateCallback =
  //               (X509Certificate cert, String host, int port) => true;
  //           return client;
  //         },
  //       );
  //     }
  //     tokenDio.interceptors
  //         .add(HeaderInterceptor()); // 添加Header拦截器，如 token之类，需要全局使用的参数
  //
  //     response = await tokenDio.post(_refreshTokenRequest.url(),
  //         data: _refreshTokenRequest.params, //request.params,
  //         options: options,
  //         cancelToken: _refreshTokenRequest.cancelToken());
  //
  //     final result = response.data;
  //     final status = response.statusCode;
  //
  //     if (status == 200) {
  //       // 需要处理
  //       final Map<String, dynamic> mapData = jsonDecode(result!);
  //
  //       final code = mapData['code'];
  //       // token无法续期必须退出登录
  //       if (code == HttpConstant.reLoginCode) {
  //         HttpEventBus.instance.commit(EventKeys.logout);
  //         return false;
  //       } else if (code == 0) {
  //         final data = mapData['data'];
  //         if (data != null && data is Map) {
  //           if (data['token'] != null) {
  //             final _token = data['token'];
  //             // UserSecureStorage().setUserToken(_token);
  //             print('token续期成功');
  //           }
  //         }
  //
  //         return true;
  //       }
  //       HttpEventBus.instance.commit(EventKeys.logout);
  //       return false;
  //     } else {
  //       HttpEventBus.instance.commit(EventKeys.logout);
  //       return false;
  //     }
  //   } catch (_) {
  //     HttpEventBus.instance.commit(EventKeys.logout);
  //     return false;
  //   }
  // }

  ///构建HiNetResponse
  Future<HttpResponse<T>> buildRes<T>(Response? response, BaseRequest request) {
    return Future.value(HttpResponse(
        //?.防止response为空
        data: response?.data,
        request: request,
        statusCode: response?.statusCode,
        statusMessage: response?.statusMessage,
        extra: response));
  }

  @override
  void cancelRequests() {
    cancelToken.cancel('');
  }
}

extension on DioAdapter {
  static Dio getDio() {
    final dio = Dio();
    dio.options = BaseOptions(
        receiveTimeout: Duration(milliseconds: HttpConstant.ReceiveTimeout),
        connectTimeout:
            Duration(milliseconds: HttpConstant.ConnectTimeout)); // 设置超时时间等 ...
    if (HttpConstant.Proxy_Enable) {
      dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final client = HttpClient();
          client.findProxy = (uri) {
            // Proxy all request to localhost:8888.
            // Be aware, the proxy should went through you running device,
            // not the host platform.
            return 'PROXY ${HttpConstant.Proxy_Ip}:${HttpConstant.Proxy_Port}';
          };
          client.badCertificateCallback =
              (X509Certificate cert, String host, int port) => true;
          return client;
        },
      );
    }

    dio.interceptors
        .add(HeaderInterceptor()); // 添加Header拦截器，如 token之类，需要全局使用的参数
    // dio.interceptors.add(ErrorInterceptor()); // 添加错误拦截器
    dio.interceptors.add(
      LogInterceptor(responseBody: false),
    );
    dio.interceptors.add(
      PrettyDioLogger(
        // 添加日志格式化工具类
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ),
    );

    return dio;
  }
}
