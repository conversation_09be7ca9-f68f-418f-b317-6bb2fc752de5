// import 'dart:io';
// import 'package:api/api.dart';
// import 'package:api/src/http/http_constant.dart';
// import 'package:connectivity/connectivity.dart';
// import 'package:dio/dio.dart';
// import '../http/http_error.dart';
//
// // 这里是一个我单独写得soket错误实例，因为dio默认生成的是不允许修改message内容的，我只能自定义一个使用
// class MyDioSocketException extends SocketException {
//   @override
//   String message = '';
//
//   MyDioSocketException(
//     message, {
//     osError,
//     address,
//     port,
//   }) : super(
//           message,
//           osError: osError,
//           address: address,
//           port: port,
//         );
// }
//
// /// 错误处理拦截器
// class ErrorInterceptor extends Interceptor {
//   // 是否有网
//   Future<bool> isConnected() async {
//     final connectivityResult = await (Connectivity().checkConnectivity());
//     return connectivityResult != ConnectivityResult.none;
//   }
//
//   @override
//   void onResponse(Response response, ResponseInterceptorHandler handler) {
//     super.onResponse(response, handler);
//   }
//
//   @override
//   Future<void> onError(DioException err, ErrorInterceptorHandler errCb) async {
//     if (err.error is SocketException) {
//       err.error = MyDioSocketException(
//         err.message,
//         osError: err.error?.osError,
//         address: err.error?.address,
//         port: err.error?.port,
//       );
//     }
//     // dio默认的错误实例，如果是没有网络，只能得到一个未知错误，无法精准的得知是否是无网络的情况
//     if (err.type == DioErrorType.other) {
//       final isConnectNetWork = await isConnected();
//       if (!isConnectNetWork && err.error is MyDioSocketException) {
//         err.error.message = '当前网络不可用，请检查您的网络';
//       }
//     }
//     // print('err.type is ${err.type}');
//     // error统一处理
//     final appException = HttpsException.create(err);
//     err.error = appException;
//
//     super.onError(err, errCb);
//   }
// }
