import '../../http/http_method.dart';
import '../base_request.dart';

///获取DXJD token
class GetDxjdToken extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/User/Login/Mobile/WithoutCode';
  }
}

// DXJD token 续期
class RenewDxjdToken extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/User/Token/Renewal/WithoutCode';
  }
}