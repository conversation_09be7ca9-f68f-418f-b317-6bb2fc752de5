import '../../http/http_method.dart';
import '../base_request.dart';

class UploadImageLogsRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/Logs/Picture/Create/Batch';
  }
}

// 追加image到事件
class AddImageToEventRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/Vehicle/Event/Add/Photos';
  }
}
