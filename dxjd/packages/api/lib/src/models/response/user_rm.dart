import 'package:json_annotation/json_annotation.dart';

part 'user_rm.g.dart';

@JsonSerializable(createToJson: false)
class UserRM {
  const UserRM(
    this.UserId,
    this.Name,
    this.Image,
    this.CreateTime,
    this.Mobile,
    this.Email,
    this.NickName,
    this.Location,
  );

  // @<PERSON><PERSON><PERSON><PERSON>(name: 'Id')
  // final String Id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'UserId')
  final int UserId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'UserName')
  final String? Name;
  @Json<PERSON>ey(name: 'Mobile')
  final String? Mobile;
  @Json<PERSON><PERSON>(name: 'Email')
  final String? Email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'NickName')
  final String? NickName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Image')
  final String? Image;
  @Json<PERSON>ey(name: 'Location')
  final int? Location;
  @JsonKey(name: 'CreateTime')
  final int? CreateTime;

  static const fromJson = _$UserRMFromJson;
}
