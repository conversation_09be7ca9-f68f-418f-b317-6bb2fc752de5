// To parse this JSON data, do
//
//     final couponList = couponListFromJson(jsonString);

import 'dart:convert';

CouponList couponListFromJson(String str) =>
    CouponList.fromJson(json.decode(str));

String couponListToJson(CouponList data) => json.encode(data.toJson());

class CouponList {
  int rows;
  int pages;
  int index;
  List<CouponItem> list;

  CouponList({
    required this.rows,
    required this.pages,
    required this.index,
    required this.list,
  });

  factory CouponList.fromJson(Map<String, dynamic> json) => CouponList(
        rows: json["rows"],
        pages: json["pages"],
        index: json["index"],
        list: List<CouponItem>.from(
            json["list"].map((x) => CouponItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "rows": rows,
        "pages": pages,
        "index": index,
        "list": List<dynamic>.from(list.map((x) => x.toJson())),
      };
}

class CouponItem {
  String id;
  String couponId;
  String studentId;
  String couponName;
  int vaildStartTime;
  int vaildEndTime;
  int status;
  List<String> goodIds;
  int discount;
  int minOrderAmount;
  int createTime;
  List<SellsGoodsInfoList> sellsGoodsInfoList = [];

  CouponItem({
    required this.id,
    required this.couponId,
    required this.studentId,
    required this.couponName,
    required this.vaildStartTime,
    required this.vaildEndTime,
    required this.status,
    required this.goodIds,
    required this.discount,
    required this.minOrderAmount,
    required this.createTime,
    required this.sellsGoodsInfoList,
  });

  factory CouponItem.fromJson(Map<String, dynamic> json) => CouponItem(
        id: json["Id"],
        couponId: json["CouponId"],
        studentId: json["StudentId"],
        couponName: json["CouponName"],
        vaildStartTime: json["VaildStartTime"],
        vaildEndTime: json["VaildEndTime"],
        status: json["Status"],
        goodIds: List<String>.from(json["GoodIds"].map((x) => x)),
        discount: json["Discount"],
        minOrderAmount: json["MinOrderAmount"],
        createTime: json["CreateTime"],
        sellsGoodsInfoList: List<SellsGoodsInfoList>.from(
            json["SellsGoodsInfoList"]
                .map((x) => SellsGoodsInfoList.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "CouponId": couponId,
        "StudentId": studentId,
        "CouponName": couponName,
        "VaildStartTime": vaildStartTime,
        "VaildEndTime": vaildEndTime,
        "Status": status,
        "GoodIds": List<dynamic>.from(goodIds.map((x) => x)),
        "Discount": discount,
        "MinOrderAmount": minOrderAmount,
        "CreateTime": createTime,
        "SellsGoodsInfoList":
            List<dynamic>.from(sellsGoodsInfoList.map((x) => x.toJson())),
      };
}

class SellsGoodsInfoList {
  String id;
  String category;
  String categorySub;
  List<String> labels;
  String title;
  List<String> cover;
  int? price;

  SellsGoodsInfoList({
    required this.id,
    required this.category,
    required this.categorySub,
    required this.labels,
    required this.title,
    required this.cover,
    required this.price,
  });

  factory SellsGoodsInfoList.fromJson(Map<String, dynamic> json) =>
      SellsGoodsInfoList(
        id: json["Id"],
        category: json["Category"],
        categorySub: json["CategorySub"],
        title: json["Title"],
        cover: List<String>.from(json["Cover"].map((x) => x)),
        price: json["Price"],
        labels: List<String>.from(json["Labels"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "Category": category,
        "CategorySub": categorySub,
        "Title": title,
        "Cover": List<dynamic>.from(cover.map((x) => x)),
        "Price": price,
        "Labels": List<dynamic>.from(labels.map((x) => x)),
      };
}
