// To parse this JSON data, do
//
//     final userAccountRm = userAccountRmFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'user_account_rm.g.dart';

UserAccountRm userAccountRmFromJson(String str) => UserAccountRm.fromJson(json.decode(str));

String userAccountRmToJson(UserAccountRm data) => json.encode(data.toJson());

@JsonSerializable()
class UserAccountRm {
  @JsonKey(name: "Uid")
  String uid;
  @<PERSON><PERSON><PERSON><PERSON>(name: "<PERSON>")
  String sid;
  @Json<PERSON>ey(name: "Name")
  String? name;
  @J<PERSON><PERSON><PERSON>(name: "BindMobile")
  String? bindMobile;
  @Json<PERSON>ey(name: "Mobile")
  String? mobile;
  @Json<PERSON>ey(name: "Birth")
  int? birth;
  @<PERSON><PERSON><PERSON><PERSON>(name: "Gender")
  int? gender;
  @J<PERSON><PERSON><PERSON>(name: "RegisterDivision")
  int registerDivision;
  @Json<PERSON>ey(name: "IsBind")
  int isBind;
  @Json<PERSON><PERSON>(name: "PlatCode")
  int platCode;
  @J<PERSON><PERSON><PERSON>(name: "RealAuthStatus")
  int? realAuthstatus;
  @JsonKey(name: "IsVip")
  int isVip = 0;
  @JsonKey(name: "IsRecorded")
  int isRecorded;
  @JsonKey(name: "Image")
  String? image;
  @JsonKey(name: "PlatSchoolId")
  String? platSchoolId;
  @JsonKey(name: "PlatSchoolName")
  String? platSchoolName;
  @JsonKey(name: "PlatTrainType")
  String? platTrainType;
  @JsonKey(name: "TopicType")
  String? topicType;
  @JsonKey(name: "IdCard")
  String? idCard;
  @JsonKey(name: "PlatRegisterDate")
  int? platRegisterDate;
  @JsonKey(name: "RegisterDate")
  int? registerDate;
  @JsonKey(name: "Vip1ExpireValue")
  int? vip1ExpireValue;
  @JsonKey(name: "Vip2ExpireValue")
  int? vip2ExpireValue;
  @JsonKey(name: "Vip3ExpireValue")
  int? vip3ExpireValue;
  @JsonKey(name: "Vip4ExpireValue")
  int? vip4ExpireValue;
  @JsonKey(name: "PlatGradStatus")
  int? platGradStatus;

  UserAccountRm({
    required this.uid,
    required this.sid,
    required this.name,
    required this.bindMobile,
    required this.mobile,
    required this.birth,
    required this.gender,
    required this.registerDivision,
    required this.isBind,
    required this.platCode,
    required this.realAuthstatus,
    required this.isVip,
    required this.isRecorded,
    required this.image,
    required this.platSchoolId,
    required this.platSchoolName,
    required this.platTrainType,
    required this.topicType,
    required this.idCard,
    required this.platRegisterDate,
    required this.registerDate,
    required this.vip1ExpireValue,
    required this.vip2ExpireValue,
    required this.vip3ExpireValue,
    required this.vip4ExpireValue,
    required this.platGradStatus,
  });

  factory UserAccountRm.fromJson(Map<String, dynamic> json) => _$UserAccountRmFromJson(json);

  Map<String, dynamic> toJson() => _$UserAccountRmToJson(this);
}
