
// To parse this JSON data, do
//
//     final bannerDataRm = bannerDataRmFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'home_banner_rm.g.dart';

BannerDataRm bannerDataRmFromJson(String str) => BannerDataRm.fromJson(json.decode(str));

String bannerDataRmToJson(BannerDataRm data) => json.encode(data.toJson());

@JsonSerializable()
class BannerDataRm {
  @Json<PERSON>ey(name: "Type")
  int type;
  @Json<PERSON>ey(name: "Description")
  String? description;
  @J<PERSON><PERSON><PERSON>(name: "RedirectPath")
  String? redirectPath;
  @J<PERSON><PERSON><PERSON>(name: "Title")
  String title;
  @Json<PERSON>ey(name: "SortOrder")
  int sortOrder;
  @Json<PERSON>ey(name: "Id")
  String id;
  @Json<PERSON>ey(name: "Url")
  String url;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ApcId")
  String? apcId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "RedirectType")
  int redirectType;
  @<PERSON><PERSON><PERSON><PERSON>(name: "Position")
  String? position;
  @J<PERSON><PERSON><PERSON>(name: "RedirectApp")
  String? redirectApp;

  BannerDataRm({
    required this.type,
    required this.description,
    required this.redirectPath,
    required this.title,
    required this.sortOrder,
    required this.id,
    required this.url,
    required this.redirectType,
    required this.position,
    required this.redirectApp,
    this.apcId
  });

  factory BannerDataRm.fromJson(Map<String, dynamic> json) => _$BannerDataRmFromJson(json);

  Map<String, dynamic> toJson() => _$BannerDataRmToJson(this);
}
