// To parse this JSON data, do
//
//     final theoryVideoListRm = theoryVideoListRmFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'theory_video_list_rm.g.dart';

TheoryVideoListRm theoryVideoListRmFromJson(String str) => TheoryVideoListRm.fromJson(json.decode(str));

String theoryVideoListRmToJson(TheoryVideoListRm data) => json.encode(data.toJson());

@JsonSerializable()
class TheoryVideoListRm {
  @JsonKey(name: "rows")
  int rows;
  @JsonKey(name: "pages")
  int pages;
  @JsonKey(name: "index")
  int index;
  @JsonKey(name: "list")
  List<ListElementRM> listRM;

  TheoryVideoListRm({
    required this.rows,
    required this.pages,
    required this.index,
    required this.listRM,
  });

  factory TheoryVideoListRm.fromJson(Map<String, dynamic> json) => _$TheoryVideoListRmFromJson(json);

  Map<String, dynamic> toJson() => _$TheoryVideoListRmToJson(this);
}

@JsonSerializable()
class ListElementRM {
  @JsonKey(name: "Group")
  List<String> group;
  @JsonKey(name: "Category")
  List<int> category;
  @JsonKey(name: "Description")
  String description;
  @JsonKey(name: "Address")
  String address;
  @JsonKey(name: "CreateTime")
  int createTime;
  @JsonKey(name: "Title")
  String title;
  @JsonKey(name: "ViewCount")
  int viewCount;
  @JsonKey(name: "Index")
  int index;
  @JsonKey(name: "Labels")
  List<String>? labels;
  @JsonKey(name: "Publisher")
  String publisher;
  @JsonKey(name: "Subject")
  List<int> subject;
  @JsonKey(name: "TrainTypes")
  List<String> trainTypes;
  @JsonKey(name: "Cover")
  List<String> cover;
  @JsonKey(name: "PraiseCount")
  int praiseCount;
  @JsonKey(name: "UpdateTime")
  int updateTime;
  @JsonKey(name: "Id")
  String id;
  @JsonKey(name: "ContentLength")
  int contentLength;
  @JsonKey(name: "ContemptCount")
  int contemptCount;

  ListElementRM({
    required this.group,
    required this.category,
    required this.description,
    required this.address,
    required this.createTime,
    required this.title,
    required this.viewCount,
    required this.index,
    required this.labels,
    required this.publisher,
    required this.subject,
    required this.trainTypes,
    required this.cover,
    required this.praiseCount,
    required this.updateTime,
    required this.id,
    required this.contentLength,
    required this.contemptCount,
  });

  factory ListElementRM.fromJson(Map<String, dynamic> json) => _$ListElementRMFromJson(json);

  Map<String, dynamic> toJson() => _$ListElementRMToJson(this);
}
