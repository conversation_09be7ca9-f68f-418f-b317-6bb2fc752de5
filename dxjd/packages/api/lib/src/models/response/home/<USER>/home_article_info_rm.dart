// To parse this JSON data, do
//
//     final articleInfoDataRm = articleInfoDataRmFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'home_article_info_rm.g.dart';

ArticleInfoDataRm articleInfoDataRmFromJson(String str) => ArticleInfoDataRm.fromJson(json.decode(str));

String articleInfoDataRmToJson(ArticleInfoDataRm data) => json.encode(data.toJson());

@JsonSerializable()
class ArticleInfoDataRm {
  @JsonKey(name: "rows")
  int rows;
  @JsonKey(name: "pages")
  int pages;
  @Json<PERSON>ey(name: "index")
  int index;
  @JsonKey(name: "list")
  List<ArticleInfoDataList> articleInfoList;

  ArticleInfoDataRm({
    required this.rows,
    required this.pages,
    required this.index,
    required this.articleInfoList,
  });

  factory ArticleInfoDataRm.fromJson(Map<String, dynamic> json) => _$ArticleInfoDataRmFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleInfoDataRmToJson(this);
}

@JsonSerializable()
class ArticleInfoDataList {
  @JsonKey(name: "User")
  String user;
  @JsonKey(name: "Category")
  String category;
  @JsonKey(name: "Views")
  int views;
  @JsonKey(name: "Comments")
  int comments;
  @JsonKey(name: "Channel")
  String channel;
  @JsonKey(name: "Ip")
  String ip;
  @JsonKey(name: "Opposes")
  int opposes;
  @JsonKey(name: "Title")
  String title;
  @JsonKey(name: "Cover")
  List<String> cover;
  @JsonKey(name: "Division")
  int division;
  @JsonKey(name: "Id")
  String id;
  @JsonKey(name: "Likes")
  int likes;
  @JsonKey(name: "Shares")
  int shares;
  @JsonKey(name: "Favorites")
  int favorites;

  @JsonKey(name: "PublishTime")
  int publishTime;

  @JsonKey(name: "Tags")
  List<String> tags;

  ArticleInfoDataList({
    required this.user,
    required this.category,
    required this.views,
    required this.comments,
    required this.channel,
    required this.ip,
    required this.opposes,
    required this.title,
    required this.cover,
    required this.division,
    required this.id,
    required this.likes,
    required this.shares,
    required this.favorites,
    required this.publishTime,
    required this.tags,
  });

  factory ArticleInfoDataList.fromJson(Map<String, dynamic> json) => _$ArticleInfoDataListFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleInfoDataListToJson(this);
}
