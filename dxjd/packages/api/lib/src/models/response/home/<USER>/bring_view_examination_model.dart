// To parse this JSON data, do
//
//     final bringVIewExamnationModel = bringVIewExamnationModelFromJson(jsonString);

import 'dart:convert';

BringVIewExaminationModel bringVIewExamnationModelFromJson(String str) => BringVIewExaminationModel.fromJson(json.decode(str));

String bringVIewExamnationModelToJson(BringVIewExaminationModel data) => json.encode(data.toJson());

class BringVIewExaminationModel {
  int? rows;
  int? pages;
  int? index;
  List<BringViewListElement>? list;

  BringVIewExaminationModel({
     this.rows,
     this.pages,
     this.index,
     this.list,
  });

  factory BringVIewExaminationModel.fromJson(Map<String, dynamic> json) => BringVIewExaminationModel(
    rows: json["rows"],
    pages: json["pages"],
    index: json["index"],
    list: List<BringViewListElement>.from(json["list"].map((x) => BringViewListElement.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "rows": rows,
    "pages": pages,
    "index": index,
    "list": List<dynamic>.from(list??[].map((x) => x.toJson())),
  };
}

class BringViewListElement {
  String? cover;
  String? examVenueAddress;
  List<String>? label;
  String? title;
  int? index;
  String? productId;
  String? id;
  int? city;
  int? district;
  int? province;
  Location? location;
  List<ExamCatalogInfo>? examCatalogInfo;

  BringViewListElement({
     this.cover,
     this.examVenueAddress,
     this.label,
     this.title,
     this.index,
    this.productId,
     this.id,
     this.city,
     this.district,
     this.province,
     this.location,
    this.examCatalogInfo,
  });

  factory BringViewListElement.fromJson(Map<String, dynamic> json) => BringViewListElement(
    cover: json["Cover"],
    examVenueAddress: json["ExamVenueAddress"],
    label: List<String>.from(json["Label"].map((x) => x)),
    title: json["Title"],
    index: json["Index"],
    productId: json["ProductId"],
    id: json["Id"],
    city: json["City"],
    district: json["District"],
    province: json["Province"],
    location: Location.fromJson(json["Location"]),
    examCatalogInfo: json["ExamCatalogInfo"] == null ? [] : List<ExamCatalogInfo>.from(json["ExamCatalogInfo"]!.map((x) => ExamCatalogInfo.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "Cover": cover,
    "ExamVenueAddress": examVenueAddress,
    "Label": List<dynamic>.from(label??[].map((x) => x)),
    "Title": title,
    "Index": index,
    "ProductId": productId,
    "Id": id,
    "City": city,
    "District": district,
    "Province": province,
    "Location": location?.toJson(),
    "ExamCatalogInfo": examCatalogInfo == null ? [] : List<dynamic>.from(examCatalogInfo!.map((x) => x.toJson())),
  };
}

class ExamCatalogInfo {
  String? cover;
  String? videoId;
  String? trailerId;
  int? index;
  String? videoName;
  String? trailerName;

  ExamCatalogInfo({
     this.cover,
     this.videoId,
     this.trailerId,
     this.videoName,
     this.trailerName,
     this.index,
  });

  factory ExamCatalogInfo.fromJson(Map<String, dynamic> json) => ExamCatalogInfo(
    cover: json["Cover"],
    videoId: json["VideoId"],
    trailerId: json["TrailerId"],
    videoName: json["VideoName"]?? "",
    trailerName: json["TrailerName"]?? "",
    index: json["Index"],
  );

  Map<String, dynamic> toJson() => {
    "Cover": cover,
    "VideoId": videoId,
    "TrailerId": trailerId,
    "VideoName": videoName,
    "TrailerName": trailerName,
    "Index": index,
  };
}

class Location {
  String? type;
  List<double>? coordinates;

  Location({
     this.type,
     this.coordinates,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
    type: json["type"],
    coordinates: List<double>.from(json["coordinates"].map((x) => x?.toDouble())),
  );

  Map<String, dynamic> toJson() => {
    "type": type,
    "coordinates": List<dynamic>.from(coordinates??[].map((x) => x)),
  };
}
