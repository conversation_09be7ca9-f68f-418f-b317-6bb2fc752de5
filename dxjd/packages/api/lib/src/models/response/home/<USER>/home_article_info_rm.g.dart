// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_article_info_rm.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ArticleInfoDataRm _$ArticleInfoDataRmFromJson(Map<String, dynamic> json) =>
    ArticleInfoDataRm(
      rows: (json['rows'] as int).toInt(),
      pages: (json['pages'] as int).toInt(),
      index: (json['index'] as int).toInt(),
      articleInfoList: (json['list'] as List<dynamic>)
          .map((e) => ArticleInfoDataList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ArticleInfoDataRmToJson(ArticleInfoDataRm instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'pages': instance.pages,
      'index': instance.index,
      'list': instance.articleInfoList,
    };

ArticleInfoDataList _$ArticleInfoDataListFromJson(Map<String, dynamic> json) =>
    ArticleInfoDataList(
      user: json['User'] as String,
      category: json['Category'] as String,
      views: (json['Views'] as int).toInt(),
      comments: (json['Comments'] as int).toInt(),
      channel: json['Channel'] as String,
      ip: json['Ip'] as String,
      opposes: (json['Opposes'] as int).toInt(),
      title: json['Title'] as String,
      cover: (json['Cover'] as List<dynamic>).map((e) => e as String).toList(),
      division: (json['Division'] as int).toInt(),
      id: json['Id'] as String,
      likes: (json['Likes'] as int).toInt(),
      shares: (json['Shares'] as int).toInt(),
      favorites: (json['Favorites'] as int).toInt(),
      publishTime: (json['PublishTime'] as int).toInt(),
      tags: (json['Tags'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ArticleInfoDataListToJson(
        ArticleInfoDataList instance) =>
    <String, dynamic>{
      'User': instance.user,
      'Category': instance.category,
      'Views': instance.views,
      'Comments': instance.comments,
      'Channel': instance.channel,
      'Ip': instance.ip,
      'Opposes': instance.opposes,
      'Title': instance.title,
      'Cover': instance.cover,
      'Division': instance.division,
      'Id': instance.id,
      'Likes': instance.likes,
      'Shares': instance.shares,
      'Favorites': instance.favorites,
      'PublishTime': instance.publishTime,
      'Tags': instance.tags,
    };
