// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'examination_room_route_rm.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExaminationRoomRouteListRm _$ExaminationRoomRouteListRmFromJson(
        Map<String, dynamic> json) =>
    ExaminationRoomRouteListRm(
      rows: (json['rows'] as int).toInt(),
      pages: (json['pages'] as int).toInt(),
      index: (json['index'] as int).toInt(),
      listRm: (json['list'] as List<dynamic>)
          .map((e) => ExaminationRoomRouteListElementRm.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ExaminationRoomRouteListRmToJson(
        ExaminationRoomRouteListRm instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'pages': instance.pages,
      'index': instance.index,
      'list': instance.listRm,
    };

ExaminationRoomRouteListElementRm _$ExaminationRoomRouteListElementRmFromJson(
        Map<String, dynamic> json) =>
    ExaminationRoomRouteListElementRm(
      status: (json['Status'] as int).toInt(),
      title: json['Title'] as String,
      carTransmission: (json['CarTransmission'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      index: (json['Index'] as int).toInt(),
      routes: (json['Routes'] as List<dynamic>)
          .map((e) => ExrRoute.fromJson(e as Map<String, dynamic>))
          .toList(),
      city: (json['City'] as int).toInt(),
      province: (json['Province'] as int).toInt(),
      subject: (json['Subject'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      remark: json['Remark'] as String,
      cover: json['Cover'] as String,
      id: json['Id'] as String,
      district: (json['District'] as int).toInt(),
    );

Map<String, dynamic> _$ExaminationRoomRouteListElementRmToJson(
        ExaminationRoomRouteListElementRm instance) =>
    <String, dynamic>{
      'Status': instance.status,
      'Title': instance.title,
      'CarTransmission': instance.carTransmission,
      'Index': instance.index,
      'Routes': instance.routes,
      'City': instance.city,
      'Province': instance.province,
      'Subject': instance.subject,
      'Remark': instance.remark,
      'Cover': instance.cover,
      'Id': instance.id,
      'District': instance.district,
    };

ExrRoute _$ExrRouteFromJson(Map<String, dynamic> json) => ExrRoute(
      title: json['Title'] as String,
      address: json['Address'] as String,
      carTransmission: (json['CarTransmission'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      subject: (json['Subject'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      remark: json['Remark'] as String,
      simulationVideoId: json['SimulationVideoId'] as String?,
      cover: json['Cover'] as String,
      videoId: json['VideoId'] as String,
      trailerId: json['TrailerId'] as String,
      index: (json['Index'] as int).toInt(),
    );

Map<String, dynamic> _$ExrRouteToJson(ExrRoute instance) => <String, dynamic>{
      'Title': instance.title,
      'Address': instance.address,
      'CarTransmission': instance.carTransmission,
      'Subject': instance.subject,
      'Remark': instance.remark,
      'Cover': instance.cover,
      'SimulationVideoId': instance.simulationVideoId,
      'VideoId': instance.videoId,
      'TrailerId': instance.trailerId,
      'Index': instance.index,
    };
