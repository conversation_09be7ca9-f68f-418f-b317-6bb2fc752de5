// To parse this JSON data, do
//
//     final bringVIewExaminationDetailModel = bringVIewExaminationDetailModelFromJson(jsonString);

import 'dart:convert';

BringVIewExaminationDetailModel bringVIewExaminationDetailModelFromJson(String str) => BringVIewExaminationDetailModel.fromJson(json.decode(str));

String bringVIewExaminationDetailModelToJson(BringVIewExaminationDetailModel data) => json.encode(data.toJson());

class BringVIewExaminationDetailModel {
  String? id;
  String? title;
  String? cover;
  int? province;
  int? city;
  int? district;
  List<int>? carTransmission;
  List<int>? subject;
  List<String>? label;
  String? examVenueAddress;
  DetailLocation? location;
  int? index;
  String? remark;
  List<ExamCatalogDetailInfo>? examCatalogInfo;
  PartInformation? part1Information;
  PartInformation? part2Information;
  PartInformation? part3Information;
  PartInformation? part4Information;
  String? productId;
  int? createTime;
  int? updateTime;
  String? shareAddress;
  String? introduction;
  String? describe;
  double? score;
  List<EvaluativeInformationList>? evaluativeInformationList;

  BringVIewExaminationDetailModel({
    this.id,
    this.title,
    this.cover,
    this.province,
    this.city,
    this.district,
    this.carTransmission,
    this.subject,
    this.label,
    this.examVenueAddress,
    this.location,
    this.index,
    this.remark,
    this.examCatalogInfo,
    this.part1Information,
    this.part2Information,
    this.part3Information,
    this.part4Information,
    this.productId,
    this.createTime,
    this.updateTime,
    this.shareAddress,
    this.introduction,
    this.describe,
    this.score,
    this.evaluativeInformationList,
  });

  factory BringVIewExaminationDetailModel.fromJson(Map<String, dynamic> json) => BringVIewExaminationDetailModel(
    id: json["Id"],
    title: json["Title"],
    cover: json["Cover"],
    province: json["Province"],
    city: json["City"],
    district: json["District"],
    carTransmission: List<int>.from(json["CarTransmission"].map((x) => x)),
    subject: List<int>.from(json["Subject"].map((x) => x)),
    label: List<String>.from(json["Label"].map((x) => x)),
    examVenueAddress: json["ExamVenueAddress"],
    location: DetailLocation.fromJson(json["Location"]),
    index: json["Index"],
    remark: json["Remark"],
    examCatalogInfo: List<ExamCatalogDetailInfo>.from(json["ExamCatalogInfo"].map((x) => ExamCatalogDetailInfo.fromJson(x))),
    part1Information: PartInformation.fromJson(json["Part1Information"]),
    part2Information: PartInformation.fromJson(json["Part2Information"]),
    part3Information: PartInformation.fromJson(json["Part3Information"]),
    part4Information: PartInformation.fromJson(json["Part4Information"]),
    productId: json["ProductId"],
    createTime: json["CreateTime"],
    updateTime: json["UpdateTime"],
    shareAddress: json["ShareAddress"],
    introduction: json["Introduction"],
    describe: json["Describe"],
    score: json["Score"],
    evaluativeInformationList: List<EvaluativeInformationList>.from(json["EvaluativeInformationList"].map((x) => EvaluativeInformationList.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "Id": id,
    "Title": title,
    "Cover": cover,
    "Province": province,
    "City": city,
    "District": district,
    "CarTransmission": List<dynamic>.from(carTransmission??[].map((x) => x)),
    "Subject": List<dynamic>.from(subject??[].map((x) => x)),
    "Label": List<dynamic>.from(label??[].map((x) => x)),
    "ExamVenueAddress": examVenueAddress,
    "Location": location?.toJson(),
    "Index": index,
    "Remark": remark,
    "ExamCatalogInfo": List<dynamic>.from(examCatalogInfo??[].map((x) => x.toJson())),
    "Part1Information": part1Information?.toJson(),
    "Part2Information": part2Information?.toJson(),
    "Part3Information": part3Information?.toJson(),
    "Part4Information": part4Information?.toJson(),
    "ProductId": productId,
    "CreateTime": createTime,
    "UpdateTime": updateTime,
    "ShareAddress": shareAddress,
    "Introduction": introduction,
    "Describe": describe,
    "Score": score,
    "EvaluativeInformationList": List<dynamic>.from(evaluativeInformationList??[].map((x) => x.toJson())),
  };
}

class EvaluativeInformationList {
  String? profilePicture;
  String? name;
  double? userRating;
  String? content;

  EvaluativeInformationList({
    this.profilePicture,
    this.name,
    this.userRating,
    this.content,
  });

  factory EvaluativeInformationList.fromJson(Map<String, dynamic> json) => EvaluativeInformationList(
    profilePicture: json["ProfilePicture"],
    name: json["Name"],
    userRating: json["UserRating"],
    content: json["Content"],
  );

  Map<String, dynamic> toJson() => {
    "ProfilePicture": profilePicture,
    "Name": name,
    "UserRating": userRating,
    "Content": content,
  };
}

class ExamCatalogDetailInfo {
  String? cover;
  String? videoId;
  String? trailerId;
  int? index;
  String? videoName;


  ExamCatalogDetailInfo({
    this.cover,
    this.videoId,
    this.trailerId,
    this.videoName,
    this.index,
  });

  factory ExamCatalogDetailInfo.fromJson(Map<String, dynamic> json) => ExamCatalogDetailInfo(
    cover: json["Cover"],
    videoId: json["VideoId"],
    trailerId: json["TrailerId"],
    index: json["Index"],
    videoName: json["VideoName"],
  );

  Map<String, dynamic> toJson() => {
    "Cover": cover,
    "VideoId": videoId,
    "TrailerId": trailerId,
    "VideoName": videoName,
    "Index": index,
  };
}

class DetailLocation {
  String? type;
  List<double>? coordinates;

  DetailLocation({
    this.type,
    this.coordinates,
  });

  factory DetailLocation.fromJson(Map<String, dynamic> json) => DetailLocation(
    type: json["type"],
    coordinates: List<double>.from(json["coordinates"].map((x) => x?.toDouble())),
  );

  Map<String, dynamic> toJson() => {
    "type": type,
    "coordinates": List<dynamic>.from(coordinates??[].map((x) => x)),
  };
}

class PartInformation {
  List<SubjectImage>? subjectImages;
  List<ExamCatalogDetailInfo>? subjectVideo;

  PartInformation({
    this.subjectImages,
    this.subjectVideo,
  });

  factory PartInformation.fromJson(Map<String, dynamic> json) => PartInformation(
    subjectImages: json["SubjectImages"]== null?[]:List<SubjectImage>.from(json["SubjectImages"].map((x) => SubjectImage.fromJson(x))),
    subjectVideo: json["SubjectVideo"]== null?[]:List<ExamCatalogDetailInfo>.from(json["SubjectVideo"].map((x) => ExamCatalogDetailInfo.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "SubjectImages": List<dynamic>.from(subjectImages??[].map((x) => x.toJson())),
    "SubjectVideo": List<dynamic>.from(subjectVideo??[].map((x) => x.toJson())),
  };
}

class SubjectImage {
  String? picture;
  int? index;
  int? click;
  int? clickPurchased;
  int? clickNotPurchased;
  String? clickPurchasedUrl;
  String? clickNotPurchasedUrl;

  SubjectImage({
    this.picture,
    this.index,
    this.click,
    this.clickPurchased,
    this.clickNotPurchased,
    this.clickPurchasedUrl,
    this.clickNotPurchasedUrl,
  });

  factory SubjectImage.fromJson(Map<String, dynamic> json) => SubjectImage(
    picture: json["Picture"],
    index: json["Index"],
    click: json["Click"],
    clickPurchased: json["ClickPurchased"],
    clickNotPurchased: json["ClickNotPurchased"],
    clickPurchasedUrl: json["ClickPurchasedUrl"],
    clickNotPurchasedUrl: json["ClickNotPurchasedUrl"],
  );

  Map<String, dynamic> toJson() => {
    "Picture": picture,
    "Index": index,
    "Click": click,
    "ClickPurchased": clickPurchased,
    "ClickNotPurchased": clickNotPurchased,
    "ClickPurchasedUrl": clickPurchasedUrl,
    "ClickNotPurchasedUrl": clickNotPurchasedUrl,
  };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
