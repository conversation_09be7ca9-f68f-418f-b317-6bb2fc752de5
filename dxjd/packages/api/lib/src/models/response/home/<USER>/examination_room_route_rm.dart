// To parse this JSON data, do
//
//     final examinationRoomRouteListRm = examinationRoomRouteListRmFromJson(jsonString);

import 'package:api/api.dart';
import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'examination_room_route_rm.g.dart';

ExaminationRoomRouteListRm examinationRoomRouteListRmFromJson(String str) => ExaminationRoomRouteListRm.fromJson(json.decode(str));

String examinationRoomRouteListRmToJson(ExaminationRoomRouteListRm data) => json.encode(data.toJson());

@JsonSerializable()
class ExaminationRoomRouteListRm {
  @JsonKey(name: "rows")
  int rows;
  @JsonKey(name: "pages")
  int pages;
  @JsonKey(name: "index")
  int index;
  @JsonKey(name: "list")
  List<ExaminationRoomRouteListElementRm> listRm;

  ExaminationRoomRouteListRm({
    required this.rows,
    required this.pages,
    required this.index,
    required this.listRm,
  });

  factory ExaminationRoomRouteListRm.fromJson(Map<String, dynamic> json) => _$ExaminationRoomRouteListRmFromJson(json);

  Map<String, dynamic> toJson() => _$ExaminationRoomRouteListRmToJson(this);
}

@JsonSerializable()
class ExaminationRoomRouteListElementRm {
  @JsonKey(name: "Status")
  int status;
  @JsonKey(name: "Title")
  String title;
  @JsonKey(name: "CarTransmission")
  List<int> carTransmission;
  @JsonKey(name: "Index")
  int index;
  @JsonKey(name: "Routes")
  List<ExrRoute> routes;
  @JsonKey(name: "City")
  int city;
  @JsonKey(name: "Province")
  int province;
  @JsonKey(name: "Subject")
  List<int> subject;
  @JsonKey(name: "Remark")
  String remark;
  @JsonKey(name: "Cover")
  String cover;
  @JsonKey(name: "Id")
  String id;
  @JsonKey(name: "District")
  int district;

  ExaminationRoomRouteListElementRm({
    required this.status,
    required this.title,
    required this.carTransmission,
    required this.index,
    required this.routes,
    required this.city,
    required this.province,
    required this.subject,
    required this.remark,
    required this.cover,
    required this.id,
    required this.district,
  });

  factory ExaminationRoomRouteListElementRm.fromJson(Map<String, dynamic> json) => _$ExaminationRoomRouteListElementRmFromJson(json);

  Map<String, dynamic> toJson() => _$ExaminationRoomRouteListElementRmToJson(this);
}

@JsonSerializable()
class ExrRoute {
  @JsonKey(name: "Title")
  String title;
  @JsonKey(name: "Address")
  String address;
  @JsonKey(name: "CarTransmission")
  List<int> carTransmission;
  @JsonKey(name: "Subject")
  List<int> subject;
  @JsonKey(name: "Remark")
  String remark;
  @JsonKey(name: "Cover")
  String cover;
  @JsonKey(name: "VideoId")
  String videoId;
  @JsonKey(name: "TrailerId")
  String trailerId;
  @JsonKey(name: "SimulationVideoId")
  String? simulationVideoId;
  @JsonKey(name: "Index")
  int index;

  ExrRoute({
    required this.title,
    required this.address,
    required this.carTransmission,
    required this.subject,
    required this.remark,
    required this.cover,
    required this.simulationVideoId,
    required this.videoId,
    required this.trailerId,
    required this.index,
  });

  factory ExrRoute.fromJson(Map<String, dynamic> json) => _$ExrRouteFromJson(json);

  Map<String, dynamic> toJson() => _$ExrRouteToJson(this);
}
