// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theory_video_list_rm.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TheoryVideoListRm _$TheoryVideoListRmFromJson(Map<String, dynamic> json) =>
    TheoryVideoListRm(
      rows: (json['rows'] as int).toInt(),
      pages: (json['pages'] as int).toInt(),
      index: (json['index'] as int).toInt(),
      listRM: (json['list'] as List<dynamic>)
          .map((e) => ListElementRM.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TheoryVideoListRmToJson(TheoryVideoListRm instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'pages': instance.pages,
      'index': instance.index,
      'list': instance.listRM,
    };

ListElementRM _$ListElementRMFromJson(Map<String, dynamic> json) =>
    ListElementRM(
      group: (json['Group'] as List<dynamic>).map((e) => e as String).toList(),
      category: (json['Category'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      description: json['Description'] as String,
      address: json['Address'] as String,
      createTime: (json['CreateTime'] as int).toInt(),
      title: json['Title'] as String,
      viewCount: (json['ViewCount'] as int).toInt(),
      index: (json['Index'] as int).toInt(),
      labels: (json['Labels'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      publisher: json['Publisher'] as String,
      subject: (json['Subject'] as List<dynamic>)
          .map((e) => (e as int).toInt())
          .toList(),
      trainTypes: (json['TrainTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      cover: (json['Cover'] as List<dynamic>).map((e) => e as String).toList(),
      praiseCount: (json['PraiseCount'] as int).toInt(),
      updateTime: (json['UpdateTime'] as int).toInt(),
      id: json['Id'] as String,
      contentLength: (json['ContentLength'] as int).toInt(),
      contemptCount: (json['ContemptCount'] as int).toInt(),
    );

Map<String, dynamic> _$ListElementRMToJson(ListElementRM instance) =>
    <String, dynamic>{
      'Group': instance.group,
      'Category': instance.category,
      'Description': instance.description,
      'Address': instance.address,
      'CreateTime': instance.createTime,
      'Title': instance.title,
      'ViewCount': instance.viewCount,
      'Index': instance.index,
      'Labels': instance.labels,
      'Publisher': instance.publisher,
      'Subject': instance.subject,
      'TrainTypes': instance.trainTypes,
      'Cover': instance.cover,
      'PraiseCount': instance.praiseCount,
      'UpdateTime': instance.updateTime,
      'Id': instance.id,
      'ContentLength': instance.contentLength,
      'ContemptCount': instance.contemptCount,
    };
