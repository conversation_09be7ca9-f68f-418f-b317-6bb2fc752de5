// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theory_video_menu_rm.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TheoryVideoMenuRm _$TheoryVideoMenuRmFromJson(Map<String, dynamic> json) =>
    TheoryVideoMenuRm(
      parentId: json['ParentId'] as String?,
      group: json['Group'] as String?,
      description: json['Description'] as String?,
      enable: (json['Enable'] as int),
      level: (json['Level'] as int?),
      index: (json['Index'] as int),
      id: json['Id'] as String,
      name: json['Name'] as String,
    );

Map<String, dynamic> _$TheoryVideoMenuRmToJson(TheoryVideoMenuRm instance) =>
    <String, dynamic>{
      'ParentId': instance.parentId,
      'Group': instance.group,
      'Description': instance.description,
      'Enable': instance.enable,
      'Level': instance.level,
      'Index': instance.index,
      'Id': instance.id,
      'Name': instance.name,
    };
