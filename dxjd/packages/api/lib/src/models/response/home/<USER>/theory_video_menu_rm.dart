// To parse this JSON data, do
//
//     final theoryVideoMenuRm = theoryVideoMenuRmFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'theory_video_menu_rm.g.dart';

TheoryVideoMenuRm theoryVideoMenuRmFromJson(String str) => TheoryVideoMenuRm.fromJson(json.decode(str));

String theoryVideoMenuRmToJson(TheoryVideoMenuRm data) => json.encode(data.toJson());

@JsonSerializable()
class TheoryVideoMenuRm {
  @Json<PERSON><PERSON>(name: "ParentId")
  String? parentId;
  @Json<PERSON>ey(name: "Group")
  String? group;
  @JsonKey(name: "Description")
  String? description;
  @JsonKey(name: "Enable")
  int enable;
  @JsonKey(name: "Level")
  int? level;
  @<PERSON>son<PERSON>ey(name: "Index")
  int index;
  @<PERSON><PERSON><PERSON><PERSON>(name: "Id")
  String id;
  @J<PERSON><PERSON>ey(name: "Name")
  String name;

  TheoryVideoMenuRm({
    required this.parentId,
    required this.group,
    required this.description,
    required this.enable,
    required this.level,
    required this.index,
    required this.id,
    required this.name,
  });

  factory TheoryVideoMenuRm.fromJson(Map<String, dynamic> json) => _$TheoryVideoMenuRmFromJson(json);

  Map<String, dynamic> toJson() => _$TheoryVideoMenuRmToJson(this);
}
