import 'package:json_annotation/json_annotation.dart';

part 'user_info_rm.g.dart';

@JsonSerializable(createFactory: false)
class UserInfoRM {
  const UserInfoRM({
    required this.username,
    required this.email,
    required this.password,
  });

  @<PERSON><PERSON><PERSON>ey(name: 'login')
  final String username;
  @<PERSON>son<PERSON>ey(name: 'email')
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'password')
  final String? password;

  Map<String, dynamic> toJson() => _$UserInfoRMToJson(this);
}
