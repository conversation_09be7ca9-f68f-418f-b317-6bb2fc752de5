
import 'package:dxjd/app_routes.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tools/tools.dart';

import 'generated/l10n.dart';
import 'l10n/app_localizations.dart';


final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();
void main() {
  // debug mode 时打印出点击事件的路径
  debugPrintHitTestResults = true;
  runApp(const MyApp());
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.dark,
    statusBarIconBrightness: Brightness.dark,
  ));
}

class MyApp extends StatelessWidget {
   const MyApp({super.key, this.navigatorObserver});
  final NavigatorObserver? navigatorObserver;

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    final easyLoading = EasyLoading.init();
    Tools.setPortrait();
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: false,
      splitScreenMode: true,
      child: KeyboardDismissOnTap(
        child: GetMaterialApp(
          defaultTransition: Transition.cupertino,
          routingCallback: (routing) {

          },
          supportedLocales: const [
            Locale('en', 'US'),
            Locale('zh', 'CN'),
          ],
          theme: ThemeData(
            splashColor: Colors.transparent, // 去除水波纹效果
            highlightColor: Colors.transparent, // 去除点击高亮效果
          ),
          localizationsDelegates: const [
            S.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            AppLocalizations.delegate,
          ],
          title: '大象驾到Pro',
          debugShowCheckedModeBanner: false,
          navigatorObservers: [routeObserver],
          getPages: AppRoutes.routes,
          locale: const Locale('zh', 'CN'),
          initialRoute: '/',
          navigatorKey: Global.navigatorKey,
          builder: (context, child) {
            child = easyLoading(context, child);
            return MediaQuery(
              data: MediaQuery.of(context)
                  .copyWith(textScaler: const TextScaler.linear(1.0)),
              child: child,
            );
          },
        ),
      ),
    );
  }
}
// extension on DarkModePreference {
//   ThemeMode toThemeMode() {
//     switch (this) {
//       case DarkModePreference.useSystemSettings:
//         return ThemeMode.system;
//       case DarkModePreference.alwaysLight:
//         return ThemeMode.light;
//       case DarkModePreference.alwaysDark:
//         return ThemeMode.dark;
//     }
//   }
// }

// class DxjdObserver extends RoutemasterObserver {
//   // RoutemasterObserver extends NavigatorObserver and
//   // receives all nested Navigator events
//   final UserRepository userRepository;
//   final HomeRepository homeRepository;
//
//   DxjdObserver({
//     required this.userRepository,
//     required this.homeRepository,
//   });
//
//   @override
//   void didPop(Route route, Route? previousRoute) {
//     print('Popped a route');
//   }
//
//   // Routemaster-specific observer method
//   @override
//   void didChangeRoute(RouteData routeData, Page page) async {
//     print('New route: ${routeData.path}---page:${page}');
//     userRepository.getUserToken().then((token) {
//       if (page.name == 'mine' && token == null) {
//         // _MyHomePageState()._routerDelegate.push('wechat_login');
//       }
//       print('token: $token');
//     });
//     if (page.name == 'home') {
//       // homeRepository.getSubjectOneTimeTable(subject: subject)
//       final String token = await userRepository.getUserToken() ?? "";
//       if (token.isNotEmpty) {
//         try {
//           await homeRepository.getUnReadMessage() ?? "";
//         } catch (e) {}
//       }
//       debugPrint('首页获取网络数据拿到当前科目${homeRepository.subjectIndex + 1}');
//     }
//     if (page.name == 'find') {
//       const shopPath = '/pages/index/index';
//       // Mop.instance.openApplet('fc2259710004813765', path: shopPath);
//       Mop.instance.startApplet(RemoteAppletRequest(apiServer: AppConfig.mopApiServer, appletId:'fc2259710004813765',
//           startParams: {'path': shopPath},isSingleProcess: true,isSingTask: true));
//     }
//   }
// }
