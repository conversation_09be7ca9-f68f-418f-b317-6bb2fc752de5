import 'package:component_library/component_library.dart';
import 'package:dxjd/app_routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'dart:io';

import 'package:api/api.dart';
import 'package:domain_models/domain_models.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';
import 'package:home_repository/home_repository.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:mop/mop.dart';
import 'package:oss_repository/oss_repository.dart';
import 'package:push_repository/push_repository.dart';
import 'package:quiz/quiz.dart';
import 'package:timing/timing.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';

import 'config.dart';

class MainController extends GetxController {
//  是否存在token
  static bool isExistToken = false;
  //是否第一次打开APP
  static bool isFirstOpen = true;
  // 初始化微信SDK
  Fluwx fluwx = Fluwx();
  //极光推送
  JPush jpush = JPush();
//  是否安装微信
  static bool isInstalledVx = false;
  //是否第一次进入（安装）app
  static bool isFirstEnterApp = true;

  late Function(WeChatResponse) responseListener;
  final _weChatMiniProgramExtMsgController =
      StreamController<String>.broadcast();
  //测试
  final _keyValueStorage = KeyValueStorage();
  late final Api _api = Api(
    userTokenSupplier: () => userRepository.getUserToken(),
  );
  late final userRepository = UserRepository(
    remoteApi: _api,
    noSqlStorage: _keyValueStorage,
    pushRepository: pushRepository,
  );

  late final homeRepository = HomeRepository(
    remoteApi: _api,
    noSqlStorage: _keyValueStorage,
    userRepository: userRepository,
  );

  late final ossRepository = OssRepository(
    remoteApi: _api,
    noSqlStorage: _keyValueStorage,
  );

  late final pushRepository = PushRepository(
    remoteApi: _api,
    noSqlStorage: _keyValueStorage,
  );

  static bool showVxLogin() {
    if (Platform.isAndroid) {
      return true;
    } else {
      if (isInstalledVx) {
        return true;
      }
    }
    return false;
  }

  late final TimingRepository timingRepository = TimingRepository.init(
      context: Get.context,
      noSqlStorage: _keyValueStorage,
      remoteApi: _api,
      userRepository: userRepository,
      homeRepository: homeRepository);

  final _lightTheme = LightDxjdThemeData();
  final _darkTheme = DarkDxjdThemeData();
  late final StreamSubscription _authChangesSubscription;
  @override
  void onReady() async {
    await refreshToken();
    await initAdvertisement();
    _initFluwx();
    // _initUmeng();
    await _initMop();
    await init();
    ossRepository.init();
    if (isFirstEnterApp) {
      dellAll();
    }
    //防止进入首页获取banner时候 vip状态还未变化
    if (MainController.isExistToken) {
      userRepository.setVipProduct();
    }
    // _initPlatformState();
    HttpEventBus.instance.addListener(EventKeys.logout, () async {
      if (Get.currentRoute != AppRoutes.home &&
          Get.currentRoute != AppRoutes.wechatLogin &&
          Get.currentRoute != AppRoutes.phoneLogin &&
          Get.currentRoute != AppRoutes.passwordLogin) {
        await userLogout();
      }
    });
    super.onReady();
  }

  initAdvertisement() async {
    isFirstEnterApp = await PreferencesService().getBool('fist_run') ?? true;
    await PreferencesService().removeKey('exitAppTime');
    if (!isFirstEnterApp) {
      try {
        await homeRepository.getSlashAdvertisingConfig();
        BeiziAdPlugin.instance.initSdk();
      } catch (e) {
        Get.offAndToNamed('/home');
      }
    } else {
      try {
        await homeRepository.getSlashAdvertisingConfig();
      } catch (e) {}
      Get.offAndToNamed('/home');
    }
  }

  initPush() {
    pushRepository.registerPush();
  }

//  退出登录
  Future<void> userLogout() async {
    try {
      final loginOut = await userRepository.loginOut();
      await PreferencesService().setString('useTopicType', '');
      await PreferencesService().setInt('useDivision', 0);
      ITools.get().clear();
    } catch (e) {}
    // await Get.find<ExaminationController>(tag: ExaminationPageState.uniqueKey.toString()).userSubject?.cancel();
    print("-------ExaminationPage initState cancel userSubject-------");
    JumpPageUntil.jumpHomeAndCloseAll();
    // if (Platform.isAndroid) {
    //   Get.toNamed(AppRoutes.wechatLogin);
    // } else {
    //   if (isInstalledVx) {
    //     Get.toNamed(AppRoutes.wechatLogin);
    //   } else {
    //     Get.toNamed(AppRoutes.phoneLogin);
    //   }
    // }
  }

  static isLoginIntercept() {
    // 检查用户是否已登录
    if (!MainController.isExistToken) {
      // 如果未登录，跳转到登录页面
      if (Platform.isAndroid) {
        Get.toNamed(AppRoutes.wechatLogin);
      } else {
        if (isInstalledVx) {
          Get.toNamed(AppRoutes.wechatLogin);
        } else {
          Get.toNamed(AppRoutes.phoneLogin);
        }
      }
      return false;
    }
    return true;
// 已登录，继续跳转
  }

  Future<void> init() async {
    await ITiming.get().init();
    await DBManager.get().init();
    ITools.get().init();
  }

  /// 第三方
  // 微信
  _initFluwx() async {
    await fluwx.registerApi(
      appId: 'wx3628d3e48d1e93a4',
      doOnAndroid: true,
      doOnIOS: true,
      universalLink: 'https://dxjk.daxiangjd.com/app/',
    );
    isInstalledVx = await fluwx.isWeChatInstalled;
    debugPrint('is installedVx $isInstalledVx');

    responseListener = (response) {
      if (response is WeChatLaunchMiniProgramResponse) {
        debugPrint('launch mini program ${response.extMsg}');
        // Toast.show('launch mini program ${response.extMsg}');
        _weChatMiniProgramExtMsgController.add(response.extMsg ?? '');
      }
    };

    fluwx.addSubscriber(responseListener);
  }

  // 友盟
  // _initUmeng() async {
  // pushRepository.setMessageCallback((msg) => debugPrint(msg));
  // pushRepository.setTokenCallback((deviceToken) => debugPrint(deviceToken));
  // pushRepository.setNotificationCallback(
  //   (receive) => debugPrint(receive),
  //   (open) => debugPrint(open),
  // );
  // final result=await pushRepository.getDeviceToken();
  // }

  //初始化极光推送
  // Future<void> _initPlatformState() async {
  //   String? platformVersion;
  //
  //   try {
  //     jpush.addEventHandler(
  //         onReceiveNotification: (Map<String, dynamic> message) async {
  //           print("flutter onReceiveNotification: $message");
  //           },
  //         onOpenNotification: (Map<String, dynamic> message) async {
  //           print("flutter onOpenNotification: $message");
  //           },
  //         onReceiveMessage: (Map<String, dynamic> message) async {
  //           print("flutter onReceiveMessage: $message");
  //           },
  //         onReceiveNotificationAuthorization: (Map<String, dynamic> message) async {
  //           print("flutter onReceiveNotificationAuthorization: $message");
  //           },
  //         onNotifyMessageUnShow: (Map<String, dynamic> message) async {
  //           print("flutter onNotifyMessageUnShow: $message");
  //           },
  //         onInAppMessageShow: (Map<String, dynamic> message) async {
  //           print("flutter onInAppMessageShow: $message");
  //           }, onCommandResult: (Map<String, dynamic> message) async {
  //           print("flutter onCommandResult: $message");
  //           },
  //         onInAppMessageClick: (Map<String, dynamic> message) async {
  //           print("flutter onInAppMessageClick: $message");
  //           },
  //         onConnected: (Map<String, dynamic> message) async {
  //           print("flutter onConnected: $message");
  //         });
  //   } on PlatformException {
  //     platformVersion = 'Failed to get platform version.';
  //   }
  //
  //   jpush.isNotificationEnabled().then((bool isEnabled) {
  //     debugPrint('通知权限是否开启: $isEnabled');
  //     if (!isEnabled) {
  //
  //       jpush.openSettingsForNotification();
  //       // jpush.requestRequiredPermission();
  //
  //       // Get.snackbar(
  //       //   "提示",
  //       //   "没有通知权限,点击跳转打开通知设置界面",
  //       //   duration: const Duration(seconds: 6),
  //       //   onTap: (_) {
  //       //     jPush.openSettingsForNotification();
  //       //   },
  //       // );
  //     }
  //   }).catchError((error) {
  //     debugPrint('通知权限是否开启 Error: ${error.toString()}');
  //   });
  //
  //   jpush.setAuth(enable: true);
  //   jpush.setup(
  //     appKey: "b4d0b660fa2cffe2fc8e07a0", //你自己应用的 AppKey
  //     channel: "theChannel",
  //     production: false,
  //     debug: true,
  //   );
  //   jpush.applyPushAuthority(
  //       new NotificationSettingsIOS(sound: true, alert: true, badge: true));
  //
  //   // Platform messages may fail, so we use a try/catch PlatformException.
  //   jpush.getRegistrationID().then((rid) {
  //     print("flutter get registration id : $rid");
  //   });
  //
  //   // iOS要是使用应用内消息，请在页面进入离开的时候配置pageEnterTo 和  pageLeave 函数，参数为页面名。
  //   jpush.pageEnterTo("HomePage"); // 在离开页面的时候请调用 jpush.pageLeave("HomePage");
  //
  //   // If the widget was removed from the tree while the asynchronous platform
  //   // message was in flight, we want to discard the reply rather than calling
  //   // setState to update our non-existent appearance.
  // }

  //初始化计时仓库
  // _initTimingRepository() {
  // 初始化单例对象，使用：TimingRepository.instance

  // // 测试计时逻辑
  // Future.delayed(const Duration(seconds: 3), () {
  //   // _timingRepository.showXiAnDriveSchoolNoticeDialog();
  //   _timingRepository.navigateToPhotoScreen(TrainLogType.signIn);
  // });
  // }

  // 初始化FinClip SDK
  Future<void> _initMop() async {
    if (Platform.isIOS || Platform.isAndroid) {
      UIConfig uiConfig = UIConfig();
      // CapsuleConfig capsuleConfig = CapsuleConfig();
      // capsuleConfig.capsuleWidth = 0;
      // uiConfig.capsuleConfig = capsuleConfig;
      uiConfig.disableSlideCloseAppletGesture = true;
      final res = await Mop.instance.initialize(
          AppConfig.mopSDKKey, AppConfig.mopSDKSecret,
          apiServer: AppConfig.mopApiServer,
          apiPrefix: AppConfig.mopApiPrefix,
          userId: "18102276914",
          uiConfig: uiConfig);
      debugPrint(res.toString());
    }

    Future.delayed(const Duration(seconds: 5), () {
      // FinClip获取用户token
      // Mop.instance.registerExtensionApi('getToken', (params) async {
      //   debugPrint("getToken:$params");
      //   Toast.show('getToken:$params');
      //   await Future.delayed(const Duration(seconds: 2));
      //   Toast.show('返回token：123456', duration: 10);
      //   // 生成一个100以内的随机数
      //   var rng = Random();
      //   var randomNumber = rng.nextInt(100);
      //   return {'token': '$randomNumber'};
      // });
      //
      // // FinClip刷新用户token
      // Mop.instance.registerExtensionApi('refreshToken', (params) async {
      //   debugPrint("refreshToken:$params");
      //   Toast.show('refreshToken:$params');
      //   await Future.delayed(const Duration(seconds: 2));
      //   Toast.show('返回refresh token：654321', duration: 10);
      //   var rng = Random();
      //   var randomNumber = rng.nextInt(100);
      //   return {'token': '$randomNumber'};
      // });

      Mop.instance.registerExtensionApi('launchMiniProgram', (params) async {
        debugPrint("launchMiniProgram:$params");
        try {
          // 唤醒微信粤学车小程序
          const path =
              'pages/soild/soild-sign-in?from=app&appName=大象驾到Pro&responseType=code';
          fluwx.open(
              target: MiniProgram(
                  username: 'gh_7a520a05e35a',
                  path: path,
                  miniProgramType: WXMiniProgramType.release));

          // 等待 _weChatMiniProgramExtMsgController 流中的第一个事件，
          // 如果在120秒内没有事件，将会抛出一个TimeoutException
          // asBroadcastStream 用于将流转换为广播流，以便多次监听
          String extMsg = await _weChatMiniProgramExtMsgController.stream
              .asBroadcastStream()
              .first
              .timeout(const Duration(seconds: 120));

          if (Platform.isAndroid) {
            // 将当前正在运行的最后一个打开的小程序移至任务栈前台，仅Android生效，
            // 用于微信粤学车小程序返回App时回到原来的小程序窗口
            Mop.instance.moveCurrentAppletToFront();
          }

          return {'signMsg': extMsg};
        } catch (e) {
          debugPrint('Error: $e');
          if (e is TimeoutException) {
            return {'error': '获取粤学车签到码超时，请重试~'};
          } else {
            if (kDebugMode) {
              return {'error': '获取粤学车签到码失败，$e.toString()'};
            }
            return {'error': '获取粤学车签到码失败，请重试~'};
          }
        }
      });

      // 唤醒Apple 授权登录
      // Mop.instance.registerExtensionApi('refreshToken', (params) async {
      //   debugPrint("refreshToken:$params");
      //   final credential = await SignInWithApple.getAppleIDCredential(
      //     scopes: [
      //       AppleIDAuthorizationScopes.email,
      //       AppleIDAuthorizationScopes.fullName,
      //     ],
      //   );
      //   return {'Apple ID Credential': '$credential'};
      // });

      // 安卓生效
      // Mop.instance.moveCurrentAppletToFront();

      // 推出拍照界面

      // 唤醒微信登录
    });

    // Mop.instance.openApplet('5e3c147a188211000141e9b1');
  }

  Future<void> _refreshToken() async {
    try {
      // await userRepository.renewalToken();
      userRepository.getUserAccountInfo().then((user) {
        if (user != null) {
          if (user.bindMobile != null) {
            if (user.topicType == null) {
              Get.toNamed(AppRoutes.homeStudyTypeSelect);
            }
          }
        }
      });
    } catch (e) {
      if (e is DomainException) {}
      if (Platform.isAndroid) {
        Get.toNamed(AppRoutes.wechatLogin);
      } else {
        if (isInstalledVx) {
          Get.toNamed(AppRoutes.wechatLogin);
        } else {
          Get.toNamed(AppRoutes.phoneLogin);
        }
      }
    }

    // _authChangesSubscription = userRepository.getUserAccount().listen((user) {
    //   if (user != null) {
    //     if(user.topicType==null){
    //       print('object================');
    //       _routerDelegate.push('study_type_select');
    //     }
    //   }
    // });
  }

  Future<void> refreshToken() async {
    HttpDao.get().setApi(_api);
    try {
      await userRepository.renewalToken();
    } catch (e) {
      if (e is DomainException) {}
      HttpEventBus.instance.commit(EventKeys.logout);
    }
  }

  Future<void> dellAll() async {
    await userRepository.deleteSecureStorage();
    // FlutterSecureStorage storage = FlutterSecureStorage();
    // await storage.deleteAll();
  }

  Future<bool> getUsToken() async {
    final bool T;
    if (await userRepository.isAutoLogin() &&
        await userRepository.getUserName() != null &&
        await userRepository.getUserName() != '') {
      T = true;
      userRepository.checkOrRegisterPushId();
    } else {
      T = false;
    }
    debugPrint('T:-----$T');
    return T;
  }

  @override
  void onClose() {
    HttpEventBus.instance.removeListener(
      EventKeys.logout,
    );
    fluwx.removeSubscriber(responseListener);
    HttpDao.reset();
    super.onClose();
  }
}
