import 'dart:io';

import 'package:flutter/foundation.dart';

class WeChatSDKConstant {
  static String WeChatAppId = 'wxaebaa2f7d896e0e7';
  static String WeChatAppSecret = '2fee83aed4947ec6d1443fde7bde739b';
  static const String miniAppId = 'gh_d8c0d081380a';
// static const String UniversalLinks = AppConfig.IsIOS
//     ? 'https://xw-web.yixc.com/app/'
//     : 'https://dxjd.yixc.com/app/';
}

class AppConfig {
  static const String appName = '大象驾到Pro';
  static const String appVersion = '1.0.0';
  static const String appPackageName = 'com.dxjk.xw';
  static const String appChannel = 'stable';
  static const String appBuildNumber = '1';
  static bool isDebug = kDebugMode;
  static bool isIOS = Platform.isIOS;
  static bool isAndroid = Platform.isAndroid;
  static const bool loginPrompt = false;

  // FinClip SDK
  static const String mopSDKKey = 'VrGPjtCYmEG6dOTewxfwQPyf4yH7CfXiN9Ut6BRumx4=';
  static const String mopSDKSecret = '6d772aa2ce0cb4db';
  static const String mopApiServer = 'https://finclip.daxiangjd.com';
  static const String mopApmServer = 'https://finclip.daxiangjd.com';
  static const String mopApiPrefix = '/api/v1/mop/';
}
