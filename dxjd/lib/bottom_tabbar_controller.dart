import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:key_value_storage/key_value_storage.dart';

class BottomTabBarController extends GetxController {
  int selectIndex = 0;
  final PageController pageController = PageController();
  bool isShowMineRedPoint = false;
  //第一次打开app 发现红点提示
  bool isShowRedPointInFind = false;
  updateSelectIndex(int index) {
    selectIndex = index;
    update();
  }

  void changeMineRedPoint() {
    isShowMineRedPoint = !isShowMineRedPoint;
    PreferencesService().setBool('red_point_in_mine',isShowMineRedPoint);
    update();
  }

  void getMineRedPoint() async {
    isShowMineRedPoint =
        await PreferencesService().getBool('red_point_in_mine') ?? false;
    update();
  }

  void getFindRedPoint() async {
    PreferencesService().getBool("is_open_find").then((value) {
      if (value == null) {
        isShowRedPointInFind = true;
      }
    });
    update();
  }

  void setFindRedPoint() {
    if (isShowRedPointInFind) {
      isShowRedPointInFind = false;
      PreferencesService().setBool("is_open_find", true);
      update();
    }
  }

  @override
  void onReady() {
    // TODO: implement onReady
    getMineRedPoint();
    getFindRedPoint();
    super.onReady();
  }
}
