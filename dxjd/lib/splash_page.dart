import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:login/login.dart';
import 'package:tools/tools.dart';
import 'package:get/get.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late MainController mainController;
  // final _keyValueStorage = KeyValueStorage();
  // late final Api _api = Api(
  //   userTokenSupplier: () => _userRepository.getUserToken(),
  // );
  // late final _userRepository = UserRepository(
  //   remoteApi: _api,
  //   noSqlStorage: _keyValueStorage,
  // );
  String _splashImage = 'img_qidong_zuoti';

  @override
  void initState() {
    mainController = Get.put(MainController(), permanent: true);
    // _refreshToken();

    PreferencesService().getBool('fist_run').then((value) {
      if (value == null) {
        _firstRunChange();
        // PreferencesService().setBool('fist_run', true);
      }
    });

    super.initState();
  }

  _firstRunChange() {
    Future.delayed(Duration(milliseconds: 1000), () {
      setState(() {
        _splashImage = 'img_qidong_jishi';
      });
    });
  }
  //
  // _refreshToken() async {
  //   await _userRepository.renewalToken();
  // }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    UIUtils.initScreenParam(context);
    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          assImg(
            img: 'img_qidong_zuoti',
            fit: BoxFit.fill,
            h: double.infinity,
            w: MediaQuery.of(context).size.width,
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.19,
            color: Colors.white,
            child: Center(
              child: assImg(img: 'logo', w: 230.0, h: 60.0),
            ),
          ),
        ],
      ),
    );
  }
}

class AdPlaceholder extends StatelessWidget {
  const AdPlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: assImg(
          img: 'img_qidong_zuoti',
          fit: BoxFit.fill,
          w: MediaQuery.of(context).size.width,
          h: double.infinity),
    );
  }
}

