import 'package:flutter/material.dart';

/// Exercise BLoC 演示页面
class ExerciseBlocDemoPage extends StatelessWidget {
  const ExerciseBlocDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exercise BLoC 重构功能演示'),
        backgroundColor: const Color(0xFF1992EF),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF5F7FA), Color(0xFFE8EDF5)],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 标题区域
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.auto_awesome,
                        size: 48,
                        color: Color(0xFF1992EF),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Exercise BLoC 重构版本',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '基于BLoC架构的全新做题系统\n支持顺序练习、模拟考试、视频播放等功能',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // 功能按钮区域
                Expanded(
                  child: Column(
                    children: [
                      // 顺序练习按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.quiz,
                        title: '顺序练习',
                        subtitle: '完整的做题流程演示',
                        color: const Color(0xFF1992EF),
                        onTap: () => _showFeatureDialog(context, '顺序练习',
                            '基于BLoC架构的顺序练习功能，支持题目加载、答题、进度保存等完整流程。'),
                      ),

                      const SizedBox(height: 16),

                      // 模拟考试按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.timer,
                        title: '模拟考试',
                        subtitle: '计时考试模式演示',
                        color: const Color(0xFFFF6B35),
                        onTap: () => _showFeatureDialog(
                            context, '模拟考试', '支持计时功能的模拟考试模式，包含完整的考试流程和成绩统计。'),
                      ),

                      const SizedBox(height: 16),

                      // 功能对比按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.compare_arrows,
                        title: '功能对比',
                        subtitle: '新旧版本功能对比',
                        color: const Color(0xFF9C27B0),
                        onTap: () => _showComparisonDialog(context),
                      ),

                      const SizedBox(height: 16),

                      // 架构说明按钮
                      _buildFeatureButton(
                        context,
                        icon: Icons.architecture,
                        title: '架构说明',
                        subtitle: 'BLoC架构设计理念',
                        color: const Color(0xFF4CAF50),
                        onTap: () => _showArchitectureDialog(context),
                      ),

                      const Spacer(),

                      // 版本信息
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.info_outline,
                                    size: 20, color: Colors.grey[600]),
                                const SizedBox(width: 8),
                                Text(
                                  '版本信息',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                _buildInfoItem('版本', 'v1.0.0'),
                                _buildInfoItem('架构', 'BLoC'),
                                _buildInfoItem('状态', '已完成'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1992EF),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _showFeatureDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            const Icon(Icons.info_outline, color: Color(0xFF1992EF)),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('了解', style: TextStyle(color: Color(0xFF1992EF))),
          ),
        ],
      ),
    );
  }

  void _showComparisonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.compare_arrows, color: Color(0xFF9C27B0)),
            SizedBox(width: 8),
            Text('功能对比'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('✅ 新版本优势:',
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: Color(0xFF4CAF50))),
              SizedBox(height: 8),
              Text('• BLoC状态管理，更清晰的架构'),
              Text('• 类型安全的状态流'),
              Text('• 更好的性能表现'),
              Text('• 模块化设计，易于维护'),
              Text('• 完整的错误处理机制'),
              SizedBox(height: 16),
              Text('🔄 兼容性:',
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: Color(0xFF2196F3))),
              SizedBox(height: 8),
              Text('• UI布局100%兼容原版'),
              Text('• 交互逻辑完全一致'),
              Text('• 数据格式向下兼容'),
              Text('• 支持平滑迁移'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('了解', style: TextStyle(color: Color(0xFF9C27B0))),
          ),
        ],
      ),
    );
  }

  void _showArchitectureDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.architecture, color: Color(0xFF4CAF50)),
            SizedBox(width: 8),
            Text('架构说明'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🏗️ BLoC架构层次:',
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: Color(0xFF4CAF50))),
              SizedBox(height: 8),
              Text('• Presentation Layer (UI层)'),
              Text('• Domain Layer (业务逻辑层)'),
              Text('• Data Layer (数据层)'),
              SizedBox(height: 16),
              Text('📊 状态管理:',
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: Color(0xFF2196F3))),
              SizedBox(height: 8),
              Text('• Event驱动的状态变更'),
              Text('• 不可变状态对象'),
              Text('• 可预测的状态流'),
              Text('• 易于测试和调试'),
              SizedBox(height: 16),
              Text('🔧 技术栈:',
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: Color(0xFF9C27B0))),
              SizedBox(height: 8),
              Text('• flutter_bloc ^8.1.3'),
              Text('• equatable ^2.0.5'),
              Text('• get_it ^7.6.4'),
              Text('• injectable ^2.3.2'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('了解', style: TextStyle(color: Color(0xFF4CAF50))),
          ),
        ],
      ),
    );
  }
}
