// import 'dart:math';
//
// import 'package:component_library/component_library.dart';
// import 'package:domain_models/domain_models.dart';
// import 'package:dxjd/tab_container_screen.dart';
// import 'package:dxjd_test/dxjd_test.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:fluwx/fluwx.dart';
// import 'package:home/home.dart';
// import 'package:home_repository/home_repository.dart';
// import 'package:login/login.dart';
// import 'package:mine/mine.dart';
// import 'package:mop/mop.dart';
// import 'package:push_repository/push_repository.dart';
// import 'package:routemaster/routemaster.dart';
// import 'package:sign_in_with_apple/sign_in_with_apple.dart';
// import 'package:timing_repository/timing_repository.dart';
// import 'package:timing/timing.dart';
// import 'package:tools/tools.dart';
// import 'package:user_repository/user_repository.dart';
//
// Map<String, PageBuilder> buildRoutingTable({
//   required RoutemasterDelegate routerDelegate,
//   required UserRepository userRepository,
//   required PushRepository pushRepository,
//   required TimingRepository timingRepository,
//   required HomeRepository homeRepository,
// }) {
//   return {
//     // _PathConstants.homePath: (route) {
//     //   return MaterialPage(
//     //       name: 'home',
//     //       child: ExaminationPage(
//     //         key: homeRepository.homeGlobalKey,
//     //         title: '广州市',
//     //         gotoTheoryVideoCall: () {
//     //           routerDelegate.push(_PathConstants.subOneTheoryVideoPath);
//     //         },
//     //         gotoPractitionerVideoCall: () {
//     //           routerDelegate.push(_PathConstants.subOnePracticalVideoPath);
//     //         },
//     //         goWebView: (String url, String title) {
//     //           routerDelegate.push(_PathConstants.homeWebViewPath,
//     //               queryParameters: {'url': url, 'title': title});
//     //         },
//     //         gotoSelectCity: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.cityListSelectHomePagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //         homeRepository: homeRepository,
//     //         gotoSubjectTwoPracticalVideoDetail: (
//     //             {int? index,
//     //             TheoryVideoListDm? theoryVideoListDM,
//     //             String? vid}) {
//     //           homeRepository.setSubTwoPracticalModeAndIndex(
//     //               theoryVideoListDM, index!, vid);
//     //           routerDelegate
//     //               .push(_PathConstants.subTwoPracticalVideoDetailPath);
//     //         },
//     //         gotoSubjectThreePracticalVideoDetail: (
//     //             {int? index,
//     //             TheoryVideoListDm? theoryVideoListDM,
//     //             String? vid}) {
//     //           homeRepository.setSubThreePracticalModeAndIndex(
//     //               theoryVideoListDM, index!, vid);
//     //           routerDelegate
//     //               .push(_PathConstants.subThreePracticalVideoDetailPath);
//     //         },
//     //         gotoSelectTrainingPage: () {
//     //           routerDelegate.push(_PathConstants.homeStudyTypeSelectPath);
//     //         },
//     //         userRepository: userRepository,
//     //         switchExaminationRoomCallback: (menuMap, exmListData) {
//     //           homeRepository.setExmMenuAndListData(menuMap, exmListData);
//     //           routerDelegate
//     //               .push(_PathConstants.examinationRoomSelectScreenPath);
//     //         },
//     //         switchExaminationRoomSubThreeCallback: (menuMap, exmListData) {
//     //           homeRepository.setExmMenuAndListData(menuMap, exmListData);
//     //           routerDelegate
//     //               .push(_PathConstants.examinationRoomSubThreeSelectScreenPath);
//     //         },
//     //         subThreeVip45DetailVideoCallback:
//     //             (ExaminationRoomRouteListElementDm?
//     //         examinationRoomRouteListElementDm) {
//     //           homeRepository.setExaminationRoomSubThreeRouteListElementDm(
//     //               examinationRoomRouteListElementDm);
//     //           routerDelegate.push(_PathConstants.subThreeVip45DetailHomePagePath);
//     //         },
//     //         gotoRoadTestDetailCallback: (menuMap, exmListData) {
//     //           homeRepository.setExmMenuAndListData(menuMap, exmListData);
//     //           routerDelegate.push(_PathConstants.subThreeRoadTestPath);
//     //         },
//     //         timingRepository: timingRepository,
//     //       ));
//     // },
//     _PathConstants.findPath: (route) {
//       // 初始化微信SDK
//       Fluwx fluwx = Fluwx();
//       const channel = MethodChannel('xw_dy_channel');
//       return const MaterialPage(name: 'find', child: Scaffold()
//           // Scaffold(
//           //   appBar: AppBar(
//           //     title: const Text("发现"),
//           //   ),
//           //   body: Center(
//           //     child: Column(
//           //       children: [
//           //         // TextButton(
//           //         //     onPressed: () {
//           //         //       timingRepository.validateBeforeTraining();
//           //         //     },
//           //         //     child: const Text('开始计时')),
//           //         // TextButton(onPressed: () {}, child: const Text('结束计时')),
//           //         TextButton(
//           //             onPressed: () {
//           //               const path =
//           //                   'pages/soild/soild-sign-in?from=app&appName=大象驾到Pro&responseType=code';
//           //               fluwx.open(
//           //                   target: MiniProgram(
//           //                       username: 'gh_7a520a05e35a',
//           //                       path: path,
//           //                       miniProgramType: WXMiniProgramType.release));
//           //             },
//           //             child: const Text('粤学车小程序')),
//           //         TextButton(
//           //             onPressed: () {
//           //               const path =
//           //                   'pages/home/<USER>';
//           //               fluwx.open(
//           //                   target: MiniProgram(
//           //                       username: 'gh_b47e16cc160c',
//           //                       path: path,
//           //                       miniProgramType: WXMiniProgramType.release));
//           //             },
//           //             child: const Text('象乐园')),
//           //         TextButton(
//           //             onPressed: () async {
//           //               timingRepository.openTimingMiniProgram();
//           //               // PermissionUtils.checkCameraPermissionNoContext().then((value) {
//           //               //   if (value) {
//           //               //     // Mop.instance.closeAllApplets();
//           //               //     // 打开小程序
//           //               //     Mop.instance.openApplet('fc2230308094514309');
//           //               //   } else {
//           //               //     Toast.show('请前往设置开启相机权限', duration: 5);
//           //               //   }
//           //               // });
//           //
//           //               // Future.delayed(const Duration(seconds: 5), () {
//           //               //   var rng = Random();
//           //               //   var randomNumber = rng.nextInt(100);
//           //               //   Map<String, dynamic> eventData = {
//           //               //     'app': '大象驾到Pro',
//           //               //     'token': '$randomNumber'
//           //               //   };
//           //               //   // 发送全局消息给小程序
//           //               //   Mop.instance.sendCustomEvent(
//           //               //       '6646c631e56b75000130d735', eventData);
//           //               //   Toast.show('发送全局消息给小程序:$eventData', duration: 10);
//           //               // });
//           //             },
//           //             child: const Text('launch FinClip')),
//           //         TextButton(
//           //             onPressed: () {
//           //               var rng = Random();
//           //               var randomNumber = rng.nextInt(100);
//           //               Map<String, dynamic> eventData = {
//           //                 'app': '大象驾到Pro',
//           //                 'token': '$randomNumber'
//           //               };
//           //               Mop.instance.sendCustomEvent(
//           //                   'fc2230308094514309', eventData);
//           //             },
//           //             child: const Text('Native Send Msg To FinClip')),
//           //         TextButton(
//           //             onPressed: () {
//           //               const shopPath ='/pages/index/index';
//           //               Mop.instance.openApplet(
//           //                   'fc2259710004813765', path: shopPath);
//           //             },
//           //             child: const Text('商城小程序')),
//           //         TextButton(
//           //             onPressed: () {
//           //               const shopPath ='pages/my/history/history';
//           //               Mop.instance.openApplet(
//           //                   'fc2230308094514309', path: shopPath);
//           //             },
//           //             child: const Text('驾考历程')),
//           //         TextButton(
//           //             onPressed: () {
//           //               const shopPath ='pages/my/set/feedback/feedback';
//           //               Mop.instance.openApplet(
//           //                   'fc2230308094514309', path: shopPath);
//           //             },
//           //             child: const Text('意见反馈')),
//           //         TextButton(
//           //             onPressed: () async {
//           //               final res=await channel.invokeMethod("getPlatformVersion");
//           //               debugPrint("Native Send Msg To Flutter:$res");
//           //             },
//           //             child: const Text('Native Send Msg ')),
//           //         TextButton(
//           //             onPressed: () async {
//           //               final res=await channel.invokeMethod("initDy");
//           //               debugPrint("initDy To Flutter:$res");
//           //             },
//           //             child: const Text('初始化抖音sdk')),
//           //         TextButton(
//           //             onPressed: () async {
//           //               final res=await channel.invokeMethod("startDy");
//           //               debugPrint("startDy To Flutter:$res");
//           //             },
//           //             child: const Text('启动抖音sdk')),
//           //       ],
//           //     ),
//           //   ),
//           // )
//           );
//     },
//     // _PathConstants.minePath: (route) {
//     //   return MaterialPage(
//     //       name: 'mine',
//     //       child: MinePage(
//     // isLogin: () async {
//     //   debugPrint(
//     //       "---------${await userRepository.getUserToken() != null}");
//     //   if (await userRepository.getUserToken() != null) {
//     //     routerDelegate.push(_PathConstants.wechatLoginPath);
//     //   } else {
//     //     routerDelegate.push(_PathConstants.wechatLoginPath);
//     //   }
//     // },
//     // gotoSetting: () {
//     //   routerDelegate.push(_PathConstants.settingPath);
//     // },
//     // userRepository: userRepository,
//     // gotoPersonalInfo: () {
//     //   routerDelegate.push(_PathConstants.personalInfoPath);
//     // },
//     // gotoSelectTrain: () {
//     //   routerDelegate.push(_PathConstants.mineStudyTypeSelectPath);
//     // },
//     // gotoMyDoc: () {
//     //   routerDelegate.push(_PathConstants.myDocPath);
//     // },
//     //       ));
//     // },
//     // _PathConstants.homeWebViewPath: (route) {
//     //   return MaterialPage(name: 'home_web_view', child: HomeWebView(
//     //     queryParameters: route.queryParameters,
//     //   ));
//     // },
//     // _PathConstants.wechatLoginPath: (route) {
//     //   return MaterialPage(
//     //       name: 'wechat_login',
//     //       child: WechatLoginPage(
//     //         gotoPhoneLogin: () {
//     //           // routerDelegate.replace(_PathConstants.bindPhoneLoginPath,queryParameters: {"loginMode":"phone"});
//     //           routerDelegate.push(_PathConstants.phoneLoginPath);
//     //         },
//     //         gotoPasswordLogin: () {
//     //           routerDelegate.push(_PathConstants.passwordLoginPath);
//     //         },
//     //         gotoIosLogin: () {
//     //           // routerDelegate.push(_PathConstants.phoneLoginPath);
//     //         },
//     //         bindPhoneLogin: () {
//     //           routerDelegate.push(_PathConstants.phoneLoginPath,
//     //               queryParameters: {"bindOrLogin": "bind"});
//     //           // routerDelegate.push(_PathConstants.bindPhoneLoginPath,queryParameters: {"loginMode":"wechat"});
//     //         },
//     //         userRepository: userRepository,
//     //         gotoStudyTypePage: () {
//     //           routerDelegate.push(_PathConstants.studyTypeSelectPath);
//     //         },
//     //         gotoHomePage: () {
//     //           // userRepository.setVipProduct();
//     //           routerDelegate.push(_PathConstants.tabContainerPath);
//     //         },
//     //         gotoPrivacyPolicy: () {
//     //           routerDelegate.push(_PathConstants.privacyPolicyPath);
//     //         },
//     //         gotoUserServiceProtocol: () {
//     //           routerDelegate.push(_PathConstants.userServiceProtocolPath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.phoneLoginPath: (route) {
//     //   return MaterialPage(
//     //       name: 'phone_login',
//     //       child: PhoneLoginPage(
//     //         goToWechatLogin: () {
//     //           routerDelegate.replace(_PathConstants.wechatLoginPath);
//     //         },
//     //         goToPassWordLogin: () {
//     //           routerDelegate.replace(_PathConstants.passwordLoginPath);
//     //         },
//     //         userRepository: userRepository,
//     //         successToStudyType: () {
//     //           routerDelegate.replace(_PathConstants.studyTypeSelectPath);
//     //         },
//     //         successToHome: () {
//     //           // userRepository.setVipProduct();
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //         queryParameters: route.queryParameters,
//     //       ));
//     // },
//     // _PathConstants.passwordLoginPath: (route) {
//     //   return MaterialPage(
//     //       name: 'password_login',
//     //       child: PassWordLogin(
//     //         gotoWechatLogin: () {
//     //           routerDelegate.push(_PathConstants.wechatLoginPath);
//     //         },
//     //         gotoPhoneLogin: () {
//     //           routerDelegate.replace(_PathConstants.phoneLoginPath);
//     //         },
//     //         forgetPassWord: () {
//     //           routerDelegate.push(_PathConstants.forgetPasswordPath);
//     //         },
//     //         userRepository: userRepository,
//     //         gotoStudyTypePage: () {
//     //           routerDelegate.push(_PathConstants.studyTypeSelectPath);
//     //         },
//     //         gotoHomePage: () {
//     //           // userRepository.setVipProduct();
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //         bindPhone: () {
//     //           routerDelegate.push(_PathConstants.phoneLoginPath,
//     //               queryParameters: {"bindOrLogin": "bind"});
//     //         },
//     //       ));
//     // },
//     // _PathConstants.forgetPasswordPath: (route) {
//     //   return MaterialPage(
//     //       name: 'forget_password',
//     //       child: ForgetPassWordPage(
//     //         userRepository: userRepository,
//     //       ));
//     // },
//     // _PathConstants.personalInfoPath: (route) {
//     //   return MaterialPage(
//     //       name: 'personal_info',
//     //       child: PersonalInfoPage(
//     //         userRepository: userRepository,
//     //       ));
//     // },
//     // _PathConstants.bindPhoneLoginPath: (route) {
//     //   return MaterialPage(
//     //       name: 'bind_phone_login',
//     //       child: OneKeyLoginPage(
//     //         bindPhoneCall: () {
//     //           routerDelegate.push(_PathConstants.phoneLoginPath,
//     //               queryParameters: {"bindOrLogin": "bind"});
//     //         },
//     //         gotoOtherPhoneLogin: () {
//     //           routerDelegate.push(_PathConstants.phoneLoginPath,
//     //               queryParameters: {"bindOrLogin": "login"});
//     //         },
//     //         goToWechatLogin: () {
//     //           routerDelegate.push(_PathConstants.wechatLoginPath);
//     //         },
//     //         goToPassWordLogin: () {
//     //           routerDelegate.push(_PathConstants.passwordLoginPath);
//     //         },
//     //         gotoStudyTypeSelect: () {
//     //           routerDelegate.push(_PathConstants.studyTypeSelectPath);
//     //         },
//     //         userRepository: userRepository,
//     //         successToStudyType: () {
//     //           routerDelegate.push(_PathConstants.studyTypeSelectPath);
//     //         },
//     //         successToHome: () {
//     //           routerDelegate.replace(_PathConstants.homePath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.studyTypeSelectPath: (route) {
//     //   return MaterialPage(
//     //       name: 'study_type_select',
//     //       child: StudyTypeSelect(
//     //         gotoSelectCityCall: () {
//     //           routerDelegate.push(_PathConstants.cityListSelectPagePath);
//     //         },
//     //         gotoStudyHomeCall: () {
//     //           routerDelegate.push(_PathConstants.tabContainerPath);
//     //         },
//     //         call: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.cityListSelectPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //           // return res.result.then((value) => value as int);
//     //         },
//     //         userRepository: userRepository,
//     //         gotoBindStudentCall: (Map<dynamic, dynamic> studentInfo) {
//     //           userRepository.setStudentInfo(studentInfo);
//     //           routerDelegate.push(_PathConstants.bindStudentPagePath);
//     //         },
//     //       ));
//     // },
//     //首页学习类型选择(题库选择)
//     // _PathConstants.homeStudyTypeSelectPath: (route) {
//     //   return MaterialPage(
//     //       name: 'home_study_type_select',
//     //       child: StudyTypeSelect(
//     //         gotoSelectCityCall: () {
//     //           // routerDelegate.push(_PathConstants.cityListSelectPagePath);
//     //         },
//     //         gotoStudyHomeCall: () {
//     //           routerDelegate.push(_PathConstants.tabContainerPath);
//     //         },
//     //         call: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.homeCityListSelectPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //           // return res.result.then((value) => value as int);
//     //         },
//     //         userRepository: userRepository,
//     //         gotoBindStudentCall: (Map<dynamic, dynamic> studentInfo) {
//     //           userRepository.setStudentInfo(studentInfo);
//     //           routerDelegate.push(_PathConstants.bindHomeStudentPagePath);
//     //         },
//     //       ));
//     // },
//
//     //我的学习类型选择(题库选择)
//     // _PathConstants.mineStudyTypeSelectPath: (route) {
//     //   return MaterialPage(
//     //       name: 'mine_study_type_select',
//     //       child: StudyTypeSelect(
//     //         gotoSelectCityCall: () {
//     //           // routerDelegate.push(_PathConstants.cityListSelectPagePath);
//     //         },
//     //         gotoStudyHomeCall: () {
//     //           routerDelegate.push(_PathConstants.minePath);
//     //         },
//     //         call: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.mineCityListSelectPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //           // return res.result.then((value) => value as int);
//     //         },
//     //         userRepository: userRepository,
//     //         gotoBindStudentCall: (Map<dynamic, dynamic> studentInfo) {
//     //           userRepository.setStudentInfo(studentInfo);
//     //           routerDelegate.push(_PathConstants.bindMineStudentPagePath);
//     //         },
//     //       ));
//     // },
//     // //考试-车型选择-城市选择
//     // _PathConstants.homeCityListSelectPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     // //我的-车型选择-城市选择
//     // _PathConstants.mineCityListSelectPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     // //学习类型城市选择
//     // _PathConstants.cityListSelectPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     // //首页城市选择
//     // _PathConstants.cityListSelectHomePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     // //科二考试路线城市选择
//     // _PathConstants.cityListSelectExaminationRoomPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     //
//     // //科三考试路线城市选择
//     // _PathConstants.cityListSelectExaminationRoomSubThreePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//     //
//     // //科三真实考场模拟城市选择
//     // _PathConstants.cityListSelectRoadTestSubThreePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'city_list_select', child: CityListCustomHeaderPage());
//     // },
//
//     // _PathConstants.settingPath: (route) {
//     //   return MaterialPage(
//     //       name: 'setting',
//     //       child: SettingPage(
//     //           // settingGotoAccountManage: (map) {
//     //           //   routerDelegate.push(_PathConstants.accountManagePagePath);
//     //           // },
//     //           // userRepository: userRepository,
//     //           loginOutGotoHome: () {
//     //             routerDelegate.push(_PathConstants.tabContainerPath);
//     //           },
//     //           privacySetting: () {
//     //             routerDelegate.push(_PathConstants.privacySettingsPagePath);
//     //           },
//     //           goSharedlist: () {
//     //             routerDelegate.push(_PathConstants.sharedListPagePath);
//     //           },
//     //           personInformation: () {
//     //             routerDelegate.push(_PathConstants
//     //                 .personalInformationCollectionChecklistPagePath);
//     //           }));
//     // },
//     // _PathConstants.accountManagePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'account_manage',
//     //       child: AccountManagePage(
//     //         settingGotoBindPhone: () {
//     //           routerDelegate.push(_PathConstants.settingBindPhonePagePath);
//     //         },
//     //         logOffAccount: () {
//     //           routerDelegate.push(_PathConstants.logOffAccountPagePath);
//     //         },
//     //         settingGotoModifyPw: () {
//     //           routerDelegate.push(_PathConstants.modifyPassWordPagePath);
//     //         },
//     //         userRepository: userRepository,
//     //       ));
//     // },
//     // _PathConstants.settingBindPhonePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'setting_bind_phone',
//     //       child: SettingBindPhonePage(
//     //         userRepository: userRepository,
//     //         successToHome: () {
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.logOffAccountPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'logoff_account',
//     //       child: LogoffAccountPage(
//     //         userRepository: userRepository,
//     //         onLogoff: () {
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.subOneTheoryVideoPath: (route) {
//     //   final isStrict = ITiming.get().openStrictTiming ?? false;
//     //   return MaterialPage(
//     //       name: 'sub_one_theory_video',
//     //       child: SubjectOneTheoryVideoScreen(
//     //         key: homeRepository.subjectOneTheoryVideoScreenKey,
//     //         gotoCatalogCall: (map, categories) {
//     //           homeRepository.setCatalogMap(map);
//     //           homeRepository.setTheoryVideoMenuQ(categories);
//     //           return routerDelegate
//     //               .push(_PathConstants.subOneVideoCatalogPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //         homeRepository: homeRepository,
//     //         videoId: (String videoOnDemandId, List<TheoryVideoDM?> categories,
//     //             List<ListElementDM?> listDMAll, int menuIndex, int listIndex) {
//     //           homeRepository.setVideoOnDemandId(videoOnDemandId);
//     //           homeRepository.setVideoType(isStrict
//     //               ? StudyVideoType.theoryStrictTiming
//     //               : StudyVideoType.normal);
//     //           homeRepository.setListIndex(listIndex);
//     //           homeRepository.setMenuIndex(menuIndex);
//     //           homeRepository.setTheoryVideoListQ(listDMAll);
//     //           homeRepository.setTheoryVideoMenuQ(categories);
//     //           routerDelegate.push(_PathConstants.theoryVideoDetailPagePath);
//     //         },
//     //         videoType: isStrict
//     //             ? StudyVideoType.theoryStrictTiming
//     //             : StudyVideoType.normal,
//     //       ));
//     // },
//     // _PathConstants.subOnePracticalVideoPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_one_practical_video',
//     //       child: SubjectOneTheoryVideoScreen(
//     //         key: homeRepository.subjectOneTheoryVideoScreenKey,
//     //         gotoCatalogCall: (map, categories) {
//     //           homeRepository.setCatalogMap(map);
//     //           homeRepository.setTheoryVideoMenuQ(categories);
//     //           return routerDelegate
//     //               .push(_PathConstants.subOneVideoCatalogPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //         homeRepository: homeRepository,
//     //         videoId: (
//     //           String vid,
//     //           List<TheoryVideoDM?> categories,
//     //           List<ListElementDM?> listDMAll,
//     //           int menuIndex,
//     //           int listIndex,
//     //         ) {
//     //           homeRepository.setVideoOnDemandId(vid);
//     //           homeRepository.setVideoType(StudyVideoType.practitioner);
//     //           homeRepository.setListIndex(listIndex);
//     //           homeRepository.setMenuIndex(menuIndex);
//     //           homeRepository.setTheoryVideoListQ(listDMAll);
//     //           homeRepository.setTheoryVideoMenuQ(categories);
//     //           routerDelegate.push(
//     //             _PathConstants.theoryVideoDetailPagePath,
//     //           );
//     //         },
//     //         videoType: StudyVideoType.practitioner,
//     //       ));
//     // },
//     // _PathConstants.subTwoPracticalVideoDetailPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_two_practical_video',
//     //       child: SubjectTwoPracticalVideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //       ));
//     // },
//
//     // _PathConstants.examinationRoomSelectScreenPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_two_switch_examination_room',
//     //       child: ExaminationRoomSelectScreen(
//     //         homeRepository: homeRepository,
//     //         userRepository: userRepository,
//     //         vipDetailVideoCallback: (ExaminationRoomRouteListElementDm?
//     //             examinationRoomRouteListElementDm) {
//     //           homeRepository.setExaminationRoomRouteListElementDm(
//     //               examinationRoomRouteListElementDm);
//     //           routerDelegate.push(_PathConstants.subTwoVipDetailPagePath);
//     //         },
//     //         gotoSelectCity: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.cityListSelectExaminationRoomPagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //       ));
//     // },
//
//     // _PathConstants.examinationRoomSubThreeSelectScreenPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_three_switch_examination_room',
//     //       child: ExaminationRoomSelectSubThreeScreen(
//     //         homeRepository: homeRepository,
//     //         timingRepository: timingRepository,
//     //         userRepository: userRepository,
//     //         subThreeVipDetailVideoCallback: (ExaminationRoomRouteListElementDm?
//     //             examinationRoomRouteListElementDm) {
//     //           homeRepository.setExaminationRoomSubThreeRouteListElementDm(
//     //               examinationRoomRouteListElementDm);
//     //           routerDelegate.push(_PathConstants.subThreeVipDetailPagePath);
//     //         },
//     //         subThreeVip45DetailVideoCallback:
//     //             (ExaminationRoomRouteListElementDm?
//     //                 examinationRoomRouteListElementDm) {
//     //           homeRepository.setExaminationRoomSubThreeRouteListElementDm(
//     //               examinationRoomRouteListElementDm);
//     //           routerDelegate.push(_PathConstants.subThreeVip45DetailPagePath);
//     //         },
//     //         gotoSelectCity: () {
//     //           return routerDelegate
//     //               .push(_PathConstants
//     //                   .cityListSelectExaminationRoomSubThreePagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //       ));
//     // },
//
//     // _PathConstants.subThreeVip45DetailPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_three_vip_45_examination_room',
//     //       child: SubjectThreeVip45VideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //         goTo68VipDetailPage: () {
//     //           routerDelegate.push(_PathConstants.subThreeVipDetailPagePath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.subThreeVip45DetailHomePagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_three_vip_45_home_examination_room',
//     //       child: SubjectThreeVip45VideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //         goTo68VipDetailPage: () {
//     //           routerDelegate.push(_PathConstants.subThreeVipDetailPagePath);
//     //         },
//     //       ));
//     // },
//     // _PathConstants.subThreeRoadTestPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_three_road_test',
//     //       child: SubjectThreeRoadTestScreen(
//     //         homeRepository: homeRepository,
//     //         gotoSelectCity: () {
//     //           return routerDelegate
//     //               .push(_PathConstants.cityListSelectRoadTestSubThreePagePath)
//     //               .result
//     //               .then((value) {
//     //             return value;
//     //           });
//     //         },
//     //       ));
//     // },
//     // _PathConstants.subThreeVipDetailPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_three_vip_examination_room',
//     //       child: SubjectThreeVipVideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //       ));
//     // },
//
//     // _PathConstants.subTwoVipDetailPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_two_vip_examination_room',
//     //       child: SubjectTwoVipVideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //       ));
//     // },
//
//     // _PathConstants.subThreePracticalVideoDetailPath: (route) {
//     //   return MaterialPage(
//     //       name: 'sub_two_practical_video',
//     //       child: SubjectThreePracticalVideoDetailScreen(
//     //           homeRepository: homeRepository, userRepository: userRepository, timingRepository: timingRepository,));
//     // },
//     // _PathConstants.subOneVideoCatalogPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'video_theory_catalog',
//     //       child: TheoryVideoCatalogPage(
//     //         homeRepository: homeRepository,
//     //       ));
//     // },
//     // _PathConstants.theoryVideoDetailPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'video_theory_detail',
//     //       child: TheoryVideoDetailScreen(
//     //         homeRepository: homeRepository,
//     //       ));
//     // },
//     // _PathConstants.modifyPassWordPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'modify_pass_word',
//     //       child: SettingModifyPwPage(
//     //         userRepository: userRepository,
//     //       ));
//     // },
//     // _PathConstants.bindStudentPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'bind_student',
//     //       child: BindStudentPageScreen(
//     //         userRepository: userRepository,
//     //         gotoHomeScreen: () {
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //       ));
//     // },
//
//     // _PathConstants.bindHomeStudentPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'bind_student',
//     //       child: BindStudentPageScreen(
//     //         userRepository: userRepository,
//     //         gotoHomeScreen: () {
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //       ));
//     // },
//     //
//     // _PathConstants.bindMineStudentPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'bind_student',
//     //       child: BindStudentPageScreen(
//     //         userRepository: userRepository,
//     //         gotoHomeScreen: () {
//     //           routerDelegate.replace(_PathConstants.tabContainerPath);
//     //         },
//     //       ));
//     // },
//
//     // //隐私协议
//     // _PathConstants.privacyPolicyPath: (route) {
//     //   return MaterialPage(name: 'privacy_policy', child: PrivacyProtocol());
//     // },
//     //
//     // //用户服务协议
//     // _PathConstants.userServiceProtocolPath: (route) {
//     //   return MaterialPage(
//     //       name: 'user_service_protocol', child: UserServiceProtocol());
//     // },
//
//     // //隐私设置
//     // _PathConstants.privacySettingsPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'privacy_setting',
//     //       child: PrivacySettingPage(
//     //         userRepository: userRepository,
//     //       ));
//     // },
//
//     // //个人信息收集清单
//     // _PathConstants.personalInformationCollectionChecklistPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'personal_information_collection_checklist',
//     //       child: PersonalInformationCollectionChecklistPage(
//     //         userRepository: userRepository,
//     //         goDetail: (String title, String content, String aim, String scene) {
//     //           routerDelegate.push(_PathConstants.collectionDetailPagePath,
//     //               queryParameters: {
//     //                 'title': title,
//     //                 'content': content,
//     //                 'aim': aim,
//     //                 'scene': scene
//     //               });
//     //         },
//     //       ));
//     // },
//
//     // //个人信息收集清单详细信息
//     // _PathConstants.collectionDetailPagePath: (route) {
//     //   return MaterialPage(
//     //       name: 'collect_detail',
//     //       child: CollectionDetailPage(
//     //         content: route.queryParameters,
//     //       ));
//     // },
//
//     // //第三方共享清单
//     // _PathConstants.sharedListPagePath: (route) {
//     //   return MaterialPage(name: 'shared_list', child: SharedListPage());
//     // },
//
//     //我的档案
//     // _PathConstants.myDocPath: (route) {
//     //   return MaterialPage(
//     //       name: 'my_doc',
//     //       child: MyStudentDoc(
//     //         userRepository: userRepository,
//     //         timingRepository: timingRepository,
//     //       ));
//     // },
//     _PathConstants.testPath: (route) {
//       return const MaterialPage(name: 'test', child: TestVideoCache());
//     },
//   };
// }
//
// class _PathConstants {
//   const _PathConstants._();
//
//   static String get tabContainerPath => '/';
//
//   static String get homePath => '${tabContainerPath}home';
//
//   static String get findPath => '${tabContainerPath}find';
//
//   static String get minePath => '${tabContainerPath}mine';
//
//   // static String get detailPath => '${minePath}/detail';
//   static String get subOneTheoryVideoPath =>
//       '${tabContainerPath}sub_one_theory_video';
//
//   static String get subOnePracticalVideoPath =>
//       '${tabContainerPath}sub_one_practical_video';
//
//   static String get homeWebViewPath => '${homePath}home_web_view';
//
//   static String get subTwoPracticalVideoDetailPath =>
//       '${tabContainerPath}sub_two_practical_video';
//
//   static String get examinationRoomSelectScreenPath =>
//       '${tabContainerPath}sub_two_switch_examination_room';
//
//   static String get examinationRoomSubThreeSelectScreenPath =>
//       '${tabContainerPath}sub_three_switch_examination_room';
//
//   static String get subThreeRoadTestPath =>
//       '${tabContainerPath}sub_three_road_test';
//
//   static String get subThreeVip45DetailPagePath =>
//       '${examinationRoomSubThreeSelectScreenPath}/sub_three_vip_45_examination_room';
//
//   static String get subThreeVip45DetailHomePagePath =>
//       '${homePath}/sub_three_vip_45_examination_room';
//
//   static String get subThreeVipDetailPagePath =>
//       '${subThreeVip45DetailPagePath}/sub_three_vip_examination_room';
//
//   static String get subTwoVipDetailPagePath =>
//       '${examinationRoomSelectScreenPath}/sub_two_vip_examination_room';
//
//   static String get subThreePracticalVideoDetailPath =>
//       '${tabContainerPath}sub_three_practical_video';
//
//   static String get subOneVideoCatalogPagePath =>
//       '${subOneTheoryVideoPath}/video_theory_catalog';
//
//   static String get theoryVideoDetailPagePath =>
//       '${subOneTheoryVideoPath}/video_theory_detail';
//
//   static String get wechatLoginPath => '${tabContainerPath}wechat_login';
//
//   static String get phoneLoginPath => '${wechatLoginPath}/phone_login';
//
//   static String get passwordLoginPath => '${wechatLoginPath}/password_login';
//
//   static String get forgetPasswordPath => '${wechatLoginPath}/forget_password';
//
//   static String get bindPhoneLoginPath => '${wechatLoginPath}/bind_phone_login';
//
//   static String get studyTypeSelectPath =>
//       '${wechatLoginPath}/study_type_select';
//
//   //考试页面题库类型选择
//   static String get homeStudyTypeSelectPath =>
//       '${tabContainerPath}home_study_type_select';
//   //我的页面题库类型选择
//   static String get mineStudyTypeSelectPath =>
//       '${tabContainerPath}mine_study_type_select';
//   //考试页面题库类型选择-城市选择
//   static String get homeCityListSelectPagePath =>
//       '${homeStudyTypeSelectPath}/city_list_select';
//   //我的页面题库类型选择-城市选择
//   static String get mineCityListSelectPagePath =>
//       '${mineStudyTypeSelectPath}/city_list_select';
//
//   //城市选择页
//   static String get cityListSelectPagePath =>
//       '${studyTypeSelectPath}/city_list_select';
//
//   static String get cityListSelectHomePagePath =>
//       '${tabContainerPath}city_list_select';
//
//   static String get cityListSelectExaminationRoomPagePath =>
//       '${examinationRoomSelectScreenPath}/city_list_select';
//
//   static String get cityListSelectExaminationRoomSubThreePagePath =>
//       '${examinationRoomSubThreeSelectScreenPath}/city_list_select';
//
//   static String get cityListSelectRoadTestSubThreePagePath =>
//       '${subThreeRoadTestPath}/city_list_select';
//
//   static String get bindStudentPagePath =>
//       '${studyTypeSelectPath}/bind_student';
//
//   static String get bindHomeStudentPagePath =>
//       '${homeStudyTypeSelectPath}/bind_student';
//
//   static String get bindMineStudentPagePath =>
//       '${mineStudyTypeSelectPath}/bind_student';
//
//   static String get settingPath => '${tabContainerPath}setting';
//
//   // static String get personalInfoPath => '${tabContainerPath}personal_info';
//
//   static String get myDocPath => '${tabContainerPath}my_doc';
//
//   static String get privacySettingsPagePath => '${settingPath}/privacy_setting';
//
//   static String get sharedListPagePath => '${settingPath}shared_list';
//
//   static String get personalInformationCollectionChecklistPagePath =>
//       '${settingPath}/personal_information_collection_checklist';
//
//   static String get collectionDetailPagePath =>
//       '${personalInformationCollectionChecklistPagePath}/collect_detail';
//
//   static String get accountManagePagePath => '${settingPath}/account_manage';
//
//   static String get settingBindPhonePagePath =>
//       '${accountManagePagePath}/setting_bind_phone';
//
//   static String get logOffAccountPagePath =>
//       '${accountManagePagePath}/logoff_account';
//
//   static String get modifyPassWordPagePath =>
//       '${accountManagePagePath}/modify_pass_word';
//
//   static String get privacyPolicyPath => '${wechatLoginPath}/privacy_policy';
//
//   static String get userServiceProtocolPath =>
//       '${wechatLoginPath}/user_service_protocol';
//
//   static String get testPath => '${tabContainerPath}test';
//
//   static String get idPathParameter => 'id';
// }
//
// void _loginByWeChat() {
//   Fluwx fluwx = Fluwx();
//   fluwx
//       .authBy(
//           which: NormalAuth(
//         scope: 'snsapi_userinfo',
//         state: 'wechat_sdk_demo_test',
//       ))
//       .then((data) {});
// }
//
// void _loginByApple() async {
//   final credential = await SignInWithApple.getAppleIDCredential(
//     scopes: [
//       AppleIDAuthorizationScopes.email,
//       AppleIDAuthorizationScopes.fullName,
//     ],
//   );
//   if (kDebugMode) {
//     debugPrint(credential.toString());
//   }
// }
