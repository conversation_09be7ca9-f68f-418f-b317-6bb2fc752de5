import 'app_localizations.dart';

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => 'dxjd';

  @override
  String get activeBottomNavigationBarItemLabel => '活动';

  @override
  String get homeBottomNavigationBarItemLabel => '考试';

  @override
  String get carBottomNavigationBarItemLabel => '车辆';

  @override
  String get findBottomNavigationBarItemLabel => '发现';

  @override
  String get storyBottomNavigationBarItemLabel => '故事';

  @override
  String get mineBottomNavigationBarItemLabel => '我的';

  @override
  String signedInUserGreeting(String username) {
    return '您好, $username!';
  }
}
