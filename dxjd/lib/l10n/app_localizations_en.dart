import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'dxjd';

  @override
  String get activeBottomNavigationBarItemLabel => 'active';

  @override
  String get homeBottomNavigationBarItemLabel => 'home';

  @override
  String get carBottomNavigationBarItemLabel => 'chle';

  @override
  String get findBottomNavigationBarItemLabel => 'find';

  @override
  String get storyBottomNavigationBarItemLabel => 'story';

  @override
  String get mineBottomNavigationBarItemLabel => 'mine';

  @override
  String signedInUserGreeting(String username) {
    return '您好, $username!';
  }
}
