<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/dxjd/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>