{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "dxjd",
            "cwd": "dxjd",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "args": ["--verbose"]
        },
        {
            "name": "dxjd (profile mode)",
            "cwd": "dxjd",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "dxjd (release mode)",
            "cwd": "dxjd",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "api",
            "cwd": "dxjd/packages/api",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "api (profile mode)",
            "cwd": "dxjd/packages/api",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "api (release mode)",
            "cwd": "dxjd/packages/api",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "component_library",
            "cwd": "dxjd/packages/component_library",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "component_library (profile mode)",
            "cwd": "dxjd/packages/component_library",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "component_library (release mode)",
            "cwd": "dxjd/packages/component_library",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "domain_models",
            "cwd": "dxjd/packages/domain_models",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "domain_models (profile mode)",
            "cwd": "dxjd/packages/domain_models",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "domain_models (release mode)",
            "cwd": "dxjd/packages/domain_models",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "home_repository",
            "cwd": "dxjd/packages/home_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "home_repository (profile mode)",
            "cwd": "dxjd/packages/home_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "home_repository (release mode)",
            "cwd": "dxjd/packages/home_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "key_value_storage",
            "cwd": "dxjd/packages/key_value_storage",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "key_value_storage (profile mode)",
            "cwd": "dxjd/packages/key_value_storage",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "key_value_storage (release mode)",
            "cwd": "dxjd/packages/key_value_storage",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "oss_repository",
            "cwd": "dxjd/packages/oss_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "oss_repository (profile mode)",
            "cwd": "dxjd/packages/oss_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "oss_repository (release mode)",
            "cwd": "dxjd/packages/oss_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "push_repository",
            "cwd": "dxjd/packages/push_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "push_repository (profile mode)",
            "cwd": "dxjd/packages/push_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "push_repository (release mode)",
            "cwd": "dxjd/packages/push_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "quiz",
            "cwd": "dxjd/packages/quiz",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "quiz (profile mode)",
            "cwd": "dxjd/packages/quiz",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "quiz (release mode)",
            "cwd": "dxjd/packages/quiz",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "timing",
            "cwd": "dxjd/packages/timing",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "timing (profile mode)",
            "cwd": "dxjd/packages/timing",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "timing (release mode)",
            "cwd": "dxjd/packages/timing",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "timing_repository",
            "cwd": "dxjd/packages/timing_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "timing_repository (profile mode)",
            "cwd": "dxjd/packages/timing_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "timing_repository (release mode)",
            "cwd": "dxjd/packages/timing_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "tools",
            "cwd": "dxjd/packages/tools",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tools (profile mode)",
            "cwd": "dxjd/packages/tools",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "tools (release mode)",
            "cwd": "dxjd/packages/tools",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "user_repository",
            "cwd": "dxjd/packages/user_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "user_repository (profile mode)",
            "cwd": "dxjd/packages/user_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "user_repository (release mode)",
            "cwd": "dxjd/packages/user_repository",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "chewie-1.8.3",
            "cwd": "dxjd/plugins/chewie-1.8.3",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "chewie-1.8.3 (profile mode)",
            "cwd": "dxjd/plugins/chewie-1.8.3",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "chewie-1.8.3 (release mode)",
            "cwd": "dxjd/plugins/chewie-1.8.3",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_cjadsdk_plugin",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_cjadsdk_plugin (profile mode)",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_cjadsdk_plugin (release mode)",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "native_camera",
            "cwd": "dxjd/plugins/native_camera",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "native_camera (profile mode)",
            "cwd": "dxjd/plugins/native_camera",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "native_camera (release mode)",
            "cwd": "dxjd/plugins/native_camera",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "take_photo_view",
            "cwd": "dxjd/plugins/take_photo_view",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "take_photo_view (profile mode)",
            "cwd": "dxjd/plugins/take_photo_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "take_photo_view (release mode)",
            "cwd": "dxjd/plugins/take_photo_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "video_cache",
            "cwd": "dxjd/plugins/video_cache",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "video_cache (profile mode)",
            "cwd": "dxjd/plugins/video_cache",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "video_cache (release mode)",
            "cwd": "dxjd/plugins/video_cache",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "TEST",
            "cwd": "dxjd/packages/features/TEST",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "TEST (profile mode)",
            "cwd": "dxjd/packages/features/TEST",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "TEST (release mode)",
            "cwd": "dxjd/packages/features/TEST",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "home",
            "cwd": "dxjd/packages/features/home",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "home (profile mode)",
            "cwd": "dxjd/packages/features/home",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "home (release mode)",
            "cwd": "dxjd/packages/features/home",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "login",
            "cwd": "dxjd/packages/features/login",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "login (profile mode)",
            "cwd": "dxjd/packages/features/login",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "login (release mode)",
            "cwd": "dxjd/packages/features/login",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "mine",
            "cwd": "dxjd/packages/features/mine",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "mine (profile mode)",
            "cwd": "dxjd/packages/features/mine",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "mine (release mode)",
            "cwd": "dxjd/packages/features/mine",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "dxjd/plugins/chewie-1.8.3/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "dxjd/plugins/chewie-1.8.3/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "dxjd/plugins/chewie-1.8.3/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "dxjd/plugins/flutter_cjadsdk_plugin/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "dxjd/plugins/native_camera/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "dxjd/plugins/native_camera/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "dxjd/plugins/native_camera/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "dxjd/plugins/take_photo_view/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "dxjd/plugins/take_photo_view/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "dxjd/plugins/take_photo_view/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "dxjd/plugins/video_cache/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "dxjd/plugins/video_cache/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "dxjd/plugins/video_cache/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}